// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStartupConfigurations = "startup_configurations"

// StartupConfigurations 启动配置
type StartupConfigurations struct {
	ID                     int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt              time.Time `gorm:"column:created_at;not null;default:current_timestamp();comment:创建时间" json:"-"`              // 创建时间
	UpdatedAt              time.Time `gorm:"column:updated_at;not null;default:current_timestamp();comment:更新时间" json:"-"`              // 更新时间
	Deleted                bool      `gorm:"column:deleted;comment:是否已删除" json:"-"`                                                     // 是否已删除
	DbVersion              int64     `gorm:"column:db_version;not null;comment:数据库版本用于加锁" json:"-"`                                     // 数据库版本用于加锁
	ProjectID              int64     `gorm:"column:project_id;not null;comment:所属项目ID" json:"project_id"`                               // 所属项目ID
	TenantID               int64     `gorm:"column:tenant_id;not null;comment:所属租户ID" json:"tenant_id"`                                 // 所属租户ID
	ProjectConfigurationID int64     `gorm:"column:project_configuration_id;not null;comment:所属项目配置ID" json:"project_configuration_id"` // 所属项目配置ID
	UUID                   string    `gorm:"column:uuid;not null;comment:UUID" json:"uuid"`                                             // UUID
	Name                   string    `gorm:"column:name;not null;comment:名称" json:"name"`                                               // 名称
	ImageConfigID          int64     `gorm:"column:image_config_id;comment:镜像配置ID" json:"image_config_id"`                              // 镜像配置ID
	ImageConfigCt          int64     `gorm:"column:image_config_ct;comment:镜像配置创建的时间" json:"image_config_ct"`                           // 镜像配置创建的时间
	ImageTag               string    `gorm:"column:image_tag;comment:镜像标签" json:"image_tag"`                                            // 镜像标签
}

// TableName StartupConfigurations's table name
func (*StartupConfigurations) TableName() string {
	return TableNameStartupConfigurations
}

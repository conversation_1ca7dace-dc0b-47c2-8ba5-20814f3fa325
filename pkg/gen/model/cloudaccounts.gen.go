// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCloudaccounts = "cloudaccounts"

// Cloudaccounts 云账号
type Cloudaccounts struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt     time.Time `gorm:"column:created_at;not null;default:current_timestamp();comment:创建时间" json:"-"` // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;not null;default:current_timestamp();comment:更新时间" json:"-"` // 更新时间
	Deleted       bool      `gorm:"column:deleted;comment:是否已删除" json:"-"`                                        // 是否已删除
	DbVersion     int64     `gorm:"column:db_version;not null;comment:数据库版本用于加锁" json:"-"`                        // 数据库版本用于加锁
	Name          string    `gorm:"column:name;not null;comment:云账号名称" json:"name"`                               // 云账号名称
	Description   string    `gorm:"column:description;comment:账号描述" json:"description"`                           // 账号描述
	Status        string    `gorm:"column:status;not null;default:init;comment:状态" json:"status"`                 // 状态
	Enabled       bool      `gorm:"column:enabled;not null;default:1;comment:是否启用" json:"enabled"`                // 是否启用
	ProviderID    int64     `gorm:"column:provider_id;not null;default:1;comment:云服务提供商ID" json:"provider_id"`    // 云服务提供商ID
	AccountName   string    `gorm:"column:account_name;not null;comment:云账号名称" json:"account_name"`               // 云账号名称
	AccountType   string    `gorm:"column:account_type;not null;comment:账号类型" json:"account_type"`                // 账号类型
	Ak            string    `gorm:"column:ak;not null;comment:Access Key" json:"ak"`                              // Access Key
	Sk            string    `gorm:"column:sk;not null;comment:Secret Key" json:"sk"`                              // Secret Key
	Endpoint      string    `gorm:"column:endpoint;not null;comment:Endpoint" json:"endpoint"`                    // Endpoint
	LastSyncAt    time.Time `gorm:"column:last_sync_at;comment:最后同步时间" json:"last_sync_at"`                       // 最后同步时间
	LastSyncError string    `gorm:"column:last_sync_error;not null;comment:最后同步错误" json:"last_sync_error"`        // 最后同步错误
	Options       string    `gorm:"column:options;not null;default:{};comment:其他配置" json:"options"`               // 其他配置
}

// TableName Cloudaccounts's table name
func (*Cloudaccounts) TableName() string {
	return TableNameCloudaccounts
}

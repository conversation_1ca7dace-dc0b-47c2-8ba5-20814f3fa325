// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserRegion = "user_region"

// UserRegion 用户地域绑定关系表
type UserRegion struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt   time.Time `gorm:"column:created_at;not null;default:current_timestamp();comment:创建时间" json:"-"` // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;not null;default:current_timestamp();comment:更新时间" json:"-"` // 更新时间
	Deleted     bool      `gorm:"column:deleted;comment:是否已删除" json:"-"`                                        // 是否已删除
	DbVersion   int64     `gorm:"column:db_version;not null;comment:数据库版本用于加锁" json:"-"`                        // 数据库版本用于加锁
	UserID      int64     `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                          // 用户ID
	UserName    string    `gorm:"column:user_name;not null;comment:用户名称" json:"user_name"`                      // 用户名称
	RegionName  string    `gorm:"column:region_name;not null;comment:地域名称" json:"region_name"`                  // 地域名称
	Zone        string    `gorm:"column:zone;not null;comment:地区名称" json:"zone"`                                // 地区名称
	EnableTime  time.Time `gorm:"column:enable_time;not null;comment:启用时间" json:"enable_time"`                  // 启用时间
	DisableTime time.Time `gorm:"column:disable_time;not null;comment:禁用时间" json:"disable_time"`                // 禁用时间
	Enable      int32     `gorm:"column:enable;not null;default:1;comment:启用状态" json:"enable"`                  // 启用状态
}

// TableName UserRegion's table name
func (*UserRegion) TableName() string {
	return TableNameUserRegion
}

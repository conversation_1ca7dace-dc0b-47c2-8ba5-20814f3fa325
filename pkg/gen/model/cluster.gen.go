// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCluster = "cluster"

// Cluster 集群信息表
type Cluster struct {
	ID                int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt         time.Time `gorm:"column:created_at;not null;default:current_timestamp();comment:创建时间" json:"-"`  // 创建时间
	UpdatedAt         time.Time `gorm:"column:updated_at;not null;default:current_timestamp();comment:更新时间" json:"-"`  // 更新时间
	Deleted           bool      `gorm:"column:deleted;comment:是否已删除" json:"-"`                                         // 是否已删除
	DbVersion         int64     `gorm:"column:db_version;not null;comment:数据库版本用于加锁" json:"-"`                         // 数据库版本用于加锁
	MasterIP          string    `gorm:"column:master_ip;not null;comment:主节点ip" json:"master_ip"`                      // 主节点ip
	Port              int64     `gorm:"column:port;not null;comment:端口" json:"port"`                                   // 端口
	PodSubnet         string    `gorm:"column:pod_subnet;not null;comment:pod网段地址" json:"pod_subnet"`                  // pod网段地址
	ServiceSubnet     string    `gorm:"column:service_subnet;not null;comment:service网段" json:"service_subnet"`        // service网段
	Name              string    `gorm:"column:name;not null;comment:集群名称" json:"name"`                                 // 集群名称
	ImageRepository   string    `gorm:"column:image_repository;not null;comment:镜像库" json:"image_repository"`          // 镜像库
	WorkerIps         string    `gorm:"column:worker_ips;not null;comment:工作节点ip" json:"worker_ips"`                   // 工作节点ip
	Region            string    `gorm:"column:region;not null;comment:地域" json:"region"`                               // 地域
	Zone              string    `gorm:"column:zone;not null;comment:地区" json:"zone"`                                   // 地区
	ClusterConfigName string    `gorm:"column:cluster_config_name;not null;comment:集群配置名称" json:"cluster_config_name"` // 集群配置名称
	ClusterConfigPath string    `gorm:"column:cluster_config_path;not null;comment:集群配置路径" json:"cluster_config_path"` // 集群配置路径
}

// TableName Cluster's table name
func (*Cluster) TableName() string {
	return TableNameCluster
}

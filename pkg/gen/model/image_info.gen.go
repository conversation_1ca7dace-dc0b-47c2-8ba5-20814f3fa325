// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameImageInfo = "image_info"

// ImageInfo 镜像信息
type ImageInfo struct {
	ID                     int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt              time.Time `gorm:"column:created_at;not null;default:current_timestamp();comment:创建时间" json:"-"`              // 创建时间
	UpdatedAt              time.Time `gorm:"column:updated_at;not null;default:current_timestamp();comment:更新时间" json:"-"`              // 更新时间
	Deleted                bool      `gorm:"column:deleted;comment:是否已删除" json:"-"`                                                     // 是否已删除
	DbVersion              int64     `gorm:"column:db_version;not null;comment:数据库版本用于加锁" json:"-"`                                     // 数据库版本用于加锁
	ProjectConfigurationID int64     `gorm:"column:project_configuration_id;not null;comment:所属项目配置ID" json:"project_configuration_id"` // 所属项目配置ID
	TagName                string    `gorm:"column:tag_name;not null;comment:镜像标签" json:"tag_name"`                                     // 镜像标签
	UUID                   string    `gorm:"column:uuid;not null;comment:UUID" json:"uuid"`                                             // UUID
	SystemName             string    `gorm:"column:system_name;not null;comment:操作系统名称" json:"system_name"`                             // 操作系统名称
	ImageURL               string    `gorm:"column:image_url;not null;comment:镜像URL 创建pod时用到" json:"image_url"`                         // 镜像URL 创建pod时用到
}

// TableName ImageInfo's table name
func (*ImageInfo) TableName() string {
	return TableNameImageInfo
}

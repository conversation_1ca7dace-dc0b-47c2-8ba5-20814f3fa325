// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameBaseImageConfig = "base_image_config"

// BaseImageConfig 基础镜像配置
type BaseImageConfig struct {
	Name        string `gorm:"column:name" json:"name"`
	Image       string `gorm:"column:image" json:"image"`
	Description string `gorm:"column:description" json:"description"`
	Category    string `gorm:"column:category" json:"category"`
	Icon        string `gorm:"column:icon" json:"icon"`
}

// TableName BaseImageConfig's table name
func (*BaseImageConfig) TableName() string {
	return TableNameBaseImageConfig
}

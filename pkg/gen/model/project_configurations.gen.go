// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameProjectConfigurations = "project_configurations"

// ProjectConfigurations 项目配置
type ProjectConfigurations struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt     time.Time `gorm:"column:created_at;not null;default:current_timestamp();comment:创建时间" json:"-"` // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;not null;default:current_timestamp();comment:更新时间" json:"-"` // 更新时间
	Deleted       bool      `gorm:"column:deleted;comment:是否已删除" json:"-"`                                        // 是否已删除
	DbVersion     int64     `gorm:"column:db_version;not null;comment:数据库版本用于加锁" json:"-"`                        // 数据库版本用于加锁
	ProjectID     int64     `gorm:"column:project_id;not null;comment:所属项目ID" json:"project_id"`                  // 所属项目ID
	TenantID      int64     `gorm:"column:tenant_id;not null;comment:所属租户ID" json:"tenant_id"`                    // 所属租户ID
	ServerTimeout int64     `gorm:"column:server_timeout;not null;comment:服务器超时时长" json:"server_timeout"`         // 服务器超时时长
	ServerOs      string    `gorm:"column:server_os;not null;comment:服务器操作系统" json:"server_os"`                   // 服务器操作系统
	CallbackURL   string    `gorm:"column:callback_url;not null;comment:分配完成后回调URL" json:"callback_url"`          // 分配完成后回调URL
}

// TableName ProjectConfigurations's table name
func (*ProjectConfigurations) TableName() string {
	return TableNameProjectConfigurations
}

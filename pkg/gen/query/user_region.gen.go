// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/gen/model"
)

func newUserRegion(db *gorm.DB, opts ...gen.DOOption) userRegion {
	_userRegion := userRegion{}

	_userRegion.userRegionDo.UseDB(db, opts...)
	_userRegion.userRegionDo.UseModel(&model.UserRegion{})

	tableName := _userRegion.userRegionDo.TableName()
	_userRegion.ALL = field.NewAsterisk(tableName)
	_userRegion.ID = field.NewInt64(tableName, "id")
	_userRegion.CreatedAt = field.NewTime(tableName, "created_at")
	_userRegion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_userRegion.Deleted = field.NewBool(tableName, "deleted")
	_userRegion.DbVersion = field.NewInt64(tableName, "db_version")
	_userRegion.UserID = field.NewInt64(tableName, "user_id")
	_userRegion.UserName = field.NewString(tableName, "user_name")
	_userRegion.RegionName = field.NewString(tableName, "region_name")
	_userRegion.Zone = field.NewString(tableName, "zone")
	_userRegion.EnableTime = field.NewTime(tableName, "enable_time")
	_userRegion.DisableTime = field.NewTime(tableName, "disable_time")
	_userRegion.Enable = field.NewInt32(tableName, "enable")

	_userRegion.fillFieldMap()

	return _userRegion
}

// userRegion 用户地域绑定关系表
type userRegion struct {
	userRegionDo

	ALL         field.Asterisk
	ID          field.Int64
	CreatedAt   field.Time   // 创建时间
	UpdatedAt   field.Time   // 更新时间
	Deleted     field.Bool   // 是否已删除
	DbVersion   field.Int64  // 数据库版本用于加锁
	UserID      field.Int64  // 用户ID
	UserName    field.String // 用户名称
	RegionName  field.String // 地域名称
	Zone        field.String // 地区名称
	EnableTime  field.Time   // 启用时间
	DisableTime field.Time   // 禁用时间
	Enable      field.Int32  // 启用状态

	fieldMap map[string]field.Expr
}

func (u userRegion) Table(newTableName string) *userRegion {
	u.userRegionDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userRegion) As(alias string) *userRegion {
	u.userRegionDo.DO = *(u.userRegionDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userRegion) updateTableName(table string) *userRegion {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt64(table, "id")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")
	u.Deleted = field.NewBool(table, "deleted")
	u.DbVersion = field.NewInt64(table, "db_version")
	u.UserID = field.NewInt64(table, "user_id")
	u.UserName = field.NewString(table, "user_name")
	u.RegionName = field.NewString(table, "region_name")
	u.Zone = field.NewString(table, "zone")
	u.EnableTime = field.NewTime(table, "enable_time")
	u.DisableTime = field.NewTime(table, "disable_time")
	u.Enable = field.NewInt32(table, "enable")

	u.fillFieldMap()

	return u
}

func (u *userRegion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userRegion) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 12)
	u.fieldMap["id"] = u.ID
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["deleted"] = u.Deleted
	u.fieldMap["db_version"] = u.DbVersion
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["user_name"] = u.UserName
	u.fieldMap["region_name"] = u.RegionName
	u.fieldMap["zone"] = u.Zone
	u.fieldMap["enable_time"] = u.EnableTime
	u.fieldMap["disable_time"] = u.DisableTime
	u.fieldMap["enable"] = u.Enable
}

func (u userRegion) clone(db *gorm.DB) userRegion {
	u.userRegionDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userRegion) replaceDB(db *gorm.DB) userRegion {
	u.userRegionDo.ReplaceDB(db)
	return u
}

type userRegionDo struct{ gen.DO }

type IUserRegionDo interface {
	gen.SubQuery
	Debug() IUserRegionDo
	WithContext(ctx context.Context) IUserRegionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserRegionDo
	WriteDB() IUserRegionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserRegionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserRegionDo
	Not(conds ...gen.Condition) IUserRegionDo
	Or(conds ...gen.Condition) IUserRegionDo
	Select(conds ...field.Expr) IUserRegionDo
	Where(conds ...gen.Condition) IUserRegionDo
	Order(conds ...field.Expr) IUserRegionDo
	Distinct(cols ...field.Expr) IUserRegionDo
	Omit(cols ...field.Expr) IUserRegionDo
	Join(table schema.Tabler, on ...field.Expr) IUserRegionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserRegionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserRegionDo
	Group(cols ...field.Expr) IUserRegionDo
	Having(conds ...gen.Condition) IUserRegionDo
	Limit(limit int) IUserRegionDo
	Offset(offset int) IUserRegionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserRegionDo
	Unscoped() IUserRegionDo
	Create(values ...*model.UserRegion) error
	CreateInBatches(values []*model.UserRegion, batchSize int) error
	Save(values ...*model.UserRegion) error
	First() (*model.UserRegion, error)
	Take() (*model.UserRegion, error)
	Last() (*model.UserRegion, error)
	Find() ([]*model.UserRegion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserRegion, err error)
	FindInBatches(result *[]*model.UserRegion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserRegion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserRegionDo
	Assign(attrs ...field.AssignExpr) IUserRegionDo
	Joins(fields ...field.RelationField) IUserRegionDo
	Preload(fields ...field.RelationField) IUserRegionDo
	FirstOrInit() (*model.UserRegion, error)
	FirstOrCreate() (*model.UserRegion, error)
	FindByPage(offset int, limit int) (result []*model.UserRegion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserRegionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userRegionDo) Debug() IUserRegionDo {
	return u.withDO(u.DO.Debug())
}

func (u userRegionDo) WithContext(ctx context.Context) IUserRegionDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userRegionDo) ReadDB() IUserRegionDo {
	return u.Clauses(dbresolver.Read)
}

func (u userRegionDo) WriteDB() IUserRegionDo {
	return u.Clauses(dbresolver.Write)
}

func (u userRegionDo) Session(config *gorm.Session) IUserRegionDo {
	return u.withDO(u.DO.Session(config))
}

func (u userRegionDo) Clauses(conds ...clause.Expression) IUserRegionDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userRegionDo) Returning(value interface{}, columns ...string) IUserRegionDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userRegionDo) Not(conds ...gen.Condition) IUserRegionDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userRegionDo) Or(conds ...gen.Condition) IUserRegionDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userRegionDo) Select(conds ...field.Expr) IUserRegionDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userRegionDo) Where(conds ...gen.Condition) IUserRegionDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userRegionDo) Order(conds ...field.Expr) IUserRegionDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userRegionDo) Distinct(cols ...field.Expr) IUserRegionDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userRegionDo) Omit(cols ...field.Expr) IUserRegionDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userRegionDo) Join(table schema.Tabler, on ...field.Expr) IUserRegionDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userRegionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserRegionDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userRegionDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserRegionDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userRegionDo) Group(cols ...field.Expr) IUserRegionDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userRegionDo) Having(conds ...gen.Condition) IUserRegionDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userRegionDo) Limit(limit int) IUserRegionDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userRegionDo) Offset(offset int) IUserRegionDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userRegionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserRegionDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userRegionDo) Unscoped() IUserRegionDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userRegionDo) Create(values ...*model.UserRegion) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userRegionDo) CreateInBatches(values []*model.UserRegion, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userRegionDo) Save(values ...*model.UserRegion) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userRegionDo) First() (*model.UserRegion, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserRegion), nil
	}
}

func (u userRegionDo) Take() (*model.UserRegion, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserRegion), nil
	}
}

func (u userRegionDo) Last() (*model.UserRegion, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserRegion), nil
	}
}

func (u userRegionDo) Find() ([]*model.UserRegion, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserRegion), err
}

func (u userRegionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserRegion, err error) {
	buf := make([]*model.UserRegion, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userRegionDo) FindInBatches(result *[]*model.UserRegion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userRegionDo) Attrs(attrs ...field.AssignExpr) IUserRegionDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userRegionDo) Assign(attrs ...field.AssignExpr) IUserRegionDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userRegionDo) Joins(fields ...field.RelationField) IUserRegionDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userRegionDo) Preload(fields ...field.RelationField) IUserRegionDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userRegionDo) FirstOrInit() (*model.UserRegion, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserRegion), nil
	}
}

func (u userRegionDo) FirstOrCreate() (*model.UserRegion, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserRegion), nil
	}
}

func (u userRegionDo) FindByPage(offset int, limit int) (result []*model.UserRegion, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userRegionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userRegionDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userRegionDo) Delete(models ...*model.UserRegion) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userRegionDo) withDO(do gen.Dao) *userRegionDo {
	u.DO = *do.(*gen.DO)
	return u
}

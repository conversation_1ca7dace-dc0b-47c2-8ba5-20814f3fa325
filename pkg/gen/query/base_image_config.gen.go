// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/gen/model"
)

func newBaseImageConfig(db *gorm.DB, opts ...gen.DOOption) baseImageConfig {
	_baseImageConfig := baseImageConfig{}

	_baseImageConfig.baseImageConfigDo.UseDB(db, opts...)
	_baseImageConfig.baseImageConfigDo.UseModel(&model.BaseImageConfig{})

	tableName := _baseImageConfig.baseImageConfigDo.TableName()
	_baseImageConfig.ALL = field.NewAsterisk(tableName)
	_baseImageConfig.Name = field.NewString(tableName, "name")
	_baseImageConfig.Image = field.NewString(tableName, "image")
	_baseImageConfig.Description = field.NewString(tableName, "description")
	_baseImageConfig.Category = field.NewString(tableName, "category")
	_baseImageConfig.Icon = field.NewString(tableName, "icon")

	_baseImageConfig.fillFieldMap()

	return _baseImageConfig
}

// baseImageConfig 基础镜像配置
type baseImageConfig struct {
	baseImageConfigDo

	ALL         field.Asterisk
	Name        field.String
	Image       field.String
	Description field.String
	Category    field.String
	Icon        field.String

	fieldMap map[string]field.Expr
}

func (b baseImageConfig) Table(newTableName string) *baseImageConfig {
	b.baseImageConfigDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b baseImageConfig) As(alias string) *baseImageConfig {
	b.baseImageConfigDo.DO = *(b.baseImageConfigDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *baseImageConfig) updateTableName(table string) *baseImageConfig {
	b.ALL = field.NewAsterisk(table)
	b.Name = field.NewString(table, "name")
	b.Image = field.NewString(table, "image")
	b.Description = field.NewString(table, "description")
	b.Category = field.NewString(table, "category")
	b.Icon = field.NewString(table, "icon")

	b.fillFieldMap()

	return b
}

func (b *baseImageConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *baseImageConfig) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 5)
	b.fieldMap["name"] = b.Name
	b.fieldMap["image"] = b.Image
	b.fieldMap["description"] = b.Description
	b.fieldMap["category"] = b.Category
	b.fieldMap["icon"] = b.Icon
}

func (b baseImageConfig) clone(db *gorm.DB) baseImageConfig {
	b.baseImageConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return b
}

func (b baseImageConfig) replaceDB(db *gorm.DB) baseImageConfig {
	b.baseImageConfigDo.ReplaceDB(db)
	return b
}

type baseImageConfigDo struct{ gen.DO }

type IBaseImageConfigDo interface {
	gen.SubQuery
	Debug() IBaseImageConfigDo
	WithContext(ctx context.Context) IBaseImageConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IBaseImageConfigDo
	WriteDB() IBaseImageConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IBaseImageConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IBaseImageConfigDo
	Not(conds ...gen.Condition) IBaseImageConfigDo
	Or(conds ...gen.Condition) IBaseImageConfigDo
	Select(conds ...field.Expr) IBaseImageConfigDo
	Where(conds ...gen.Condition) IBaseImageConfigDo
	Order(conds ...field.Expr) IBaseImageConfigDo
	Distinct(cols ...field.Expr) IBaseImageConfigDo
	Omit(cols ...field.Expr) IBaseImageConfigDo
	Join(table schema.Tabler, on ...field.Expr) IBaseImageConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IBaseImageConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IBaseImageConfigDo
	Group(cols ...field.Expr) IBaseImageConfigDo
	Having(conds ...gen.Condition) IBaseImageConfigDo
	Limit(limit int) IBaseImageConfigDo
	Offset(offset int) IBaseImageConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IBaseImageConfigDo
	Unscoped() IBaseImageConfigDo
	Create(values ...*model.BaseImageConfig) error
	CreateInBatches(values []*model.BaseImageConfig, batchSize int) error
	Save(values ...*model.BaseImageConfig) error
	First() (*model.BaseImageConfig, error)
	Take() (*model.BaseImageConfig, error)
	Last() (*model.BaseImageConfig, error)
	Find() ([]*model.BaseImageConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.BaseImageConfig, err error)
	FindInBatches(result *[]*model.BaseImageConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.BaseImageConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IBaseImageConfigDo
	Assign(attrs ...field.AssignExpr) IBaseImageConfigDo
	Joins(fields ...field.RelationField) IBaseImageConfigDo
	Preload(fields ...field.RelationField) IBaseImageConfigDo
	FirstOrInit() (*model.BaseImageConfig, error)
	FirstOrCreate() (*model.BaseImageConfig, error)
	FindByPage(offset int, limit int) (result []*model.BaseImageConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IBaseImageConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (b baseImageConfigDo) Debug() IBaseImageConfigDo {
	return b.withDO(b.DO.Debug())
}

func (b baseImageConfigDo) WithContext(ctx context.Context) IBaseImageConfigDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b baseImageConfigDo) ReadDB() IBaseImageConfigDo {
	return b.Clauses(dbresolver.Read)
}

func (b baseImageConfigDo) WriteDB() IBaseImageConfigDo {
	return b.Clauses(dbresolver.Write)
}

func (b baseImageConfigDo) Session(config *gorm.Session) IBaseImageConfigDo {
	return b.withDO(b.DO.Session(config))
}

func (b baseImageConfigDo) Clauses(conds ...clause.Expression) IBaseImageConfigDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b baseImageConfigDo) Returning(value interface{}, columns ...string) IBaseImageConfigDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b baseImageConfigDo) Not(conds ...gen.Condition) IBaseImageConfigDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b baseImageConfigDo) Or(conds ...gen.Condition) IBaseImageConfigDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b baseImageConfigDo) Select(conds ...field.Expr) IBaseImageConfigDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b baseImageConfigDo) Where(conds ...gen.Condition) IBaseImageConfigDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b baseImageConfigDo) Order(conds ...field.Expr) IBaseImageConfigDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b baseImageConfigDo) Distinct(cols ...field.Expr) IBaseImageConfigDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b baseImageConfigDo) Omit(cols ...field.Expr) IBaseImageConfigDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b baseImageConfigDo) Join(table schema.Tabler, on ...field.Expr) IBaseImageConfigDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b baseImageConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IBaseImageConfigDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b baseImageConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IBaseImageConfigDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b baseImageConfigDo) Group(cols ...field.Expr) IBaseImageConfigDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b baseImageConfigDo) Having(conds ...gen.Condition) IBaseImageConfigDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b baseImageConfigDo) Limit(limit int) IBaseImageConfigDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b baseImageConfigDo) Offset(offset int) IBaseImageConfigDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b baseImageConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IBaseImageConfigDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b baseImageConfigDo) Unscoped() IBaseImageConfigDo {
	return b.withDO(b.DO.Unscoped())
}

func (b baseImageConfigDo) Create(values ...*model.BaseImageConfig) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b baseImageConfigDo) CreateInBatches(values []*model.BaseImageConfig, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b baseImageConfigDo) Save(values ...*model.BaseImageConfig) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b baseImageConfigDo) First() (*model.BaseImageConfig, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.BaseImageConfig), nil
	}
}

func (b baseImageConfigDo) Take() (*model.BaseImageConfig, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.BaseImageConfig), nil
	}
}

func (b baseImageConfigDo) Last() (*model.BaseImageConfig, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.BaseImageConfig), nil
	}
}

func (b baseImageConfigDo) Find() ([]*model.BaseImageConfig, error) {
	result, err := b.DO.Find()
	return result.([]*model.BaseImageConfig), err
}

func (b baseImageConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.BaseImageConfig, err error) {
	buf := make([]*model.BaseImageConfig, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b baseImageConfigDo) FindInBatches(result *[]*model.BaseImageConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b baseImageConfigDo) Attrs(attrs ...field.AssignExpr) IBaseImageConfigDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b baseImageConfigDo) Assign(attrs ...field.AssignExpr) IBaseImageConfigDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b baseImageConfigDo) Joins(fields ...field.RelationField) IBaseImageConfigDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b baseImageConfigDo) Preload(fields ...field.RelationField) IBaseImageConfigDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b baseImageConfigDo) FirstOrInit() (*model.BaseImageConfig, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.BaseImageConfig), nil
	}
}

func (b baseImageConfigDo) FirstOrCreate() (*model.BaseImageConfig, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.BaseImageConfig), nil
	}
}

func (b baseImageConfigDo) FindByPage(offset int, limit int) (result []*model.BaseImageConfig, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b baseImageConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b baseImageConfigDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b baseImageConfigDo) Delete(models ...*model.BaseImageConfig) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *baseImageConfigDo) withDO(do gen.Dao) *baseImageConfigDo {
	b.DO = *do.(*gen.DO)
	return b
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/gen/model"
)

func newImageInfo(db *gorm.DB, opts ...gen.DOOption) imageInfo {
	_imageInfo := imageInfo{}

	_imageInfo.imageInfoDo.UseDB(db, opts...)
	_imageInfo.imageInfoDo.UseModel(&model.ImageInfo{})

	tableName := _imageInfo.imageInfoDo.TableName()
	_imageInfo.ALL = field.NewAsterisk(tableName)
	_imageInfo.ID = field.NewInt64(tableName, "id")
	_imageInfo.CreatedAt = field.NewTime(tableName, "created_at")
	_imageInfo.UpdatedAt = field.NewTime(tableName, "updated_at")
	_imageInfo.Deleted = field.NewBool(tableName, "deleted")
	_imageInfo.DbVersion = field.NewInt64(tableName, "db_version")
	_imageInfo.ProjectConfigurationID = field.NewInt64(tableName, "project_configuration_id")
	_imageInfo.TagName = field.NewString(tableName, "tag_name")
	_imageInfo.UUID = field.NewString(tableName, "uuid")
	_imageInfo.SystemName = field.NewString(tableName, "system_name")
	_imageInfo.ImageURL = field.NewString(tableName, "image_url")

	_imageInfo.fillFieldMap()

	return _imageInfo
}

// imageInfo 镜像信息
type imageInfo struct {
	imageInfoDo

	ALL                    field.Asterisk
	ID                     field.Int64
	CreatedAt              field.Time   // 创建时间
	UpdatedAt              field.Time   // 更新时间
	Deleted                field.Bool   // 是否已删除
	DbVersion              field.Int64  // 数据库版本用于加锁
	ProjectConfigurationID field.Int64  // 所属项目配置ID
	TagName                field.String // 镜像标签
	UUID                   field.String // UUID
	SystemName             field.String // 操作系统名称
	ImageURL               field.String // 镜像URL 创建pod时用到

	fieldMap map[string]field.Expr
}

func (i imageInfo) Table(newTableName string) *imageInfo {
	i.imageInfoDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i imageInfo) As(alias string) *imageInfo {
	i.imageInfoDo.DO = *(i.imageInfoDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *imageInfo) updateTableName(table string) *imageInfo {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewInt64(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Deleted = field.NewBool(table, "deleted")
	i.DbVersion = field.NewInt64(table, "db_version")
	i.ProjectConfigurationID = field.NewInt64(table, "project_configuration_id")
	i.TagName = field.NewString(table, "tag_name")
	i.UUID = field.NewString(table, "uuid")
	i.SystemName = field.NewString(table, "system_name")
	i.ImageURL = field.NewString(table, "image_url")

	i.fillFieldMap()

	return i
}

func (i *imageInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *imageInfo) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted"] = i.Deleted
	i.fieldMap["db_version"] = i.DbVersion
	i.fieldMap["project_configuration_id"] = i.ProjectConfigurationID
	i.fieldMap["tag_name"] = i.TagName
	i.fieldMap["uuid"] = i.UUID
	i.fieldMap["system_name"] = i.SystemName
	i.fieldMap["image_url"] = i.ImageURL
}

func (i imageInfo) clone(db *gorm.DB) imageInfo {
	i.imageInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i imageInfo) replaceDB(db *gorm.DB) imageInfo {
	i.imageInfoDo.ReplaceDB(db)
	return i
}

type imageInfoDo struct{ gen.DO }

type IImageInfoDo interface {
	gen.SubQuery
	Debug() IImageInfoDo
	WithContext(ctx context.Context) IImageInfoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IImageInfoDo
	WriteDB() IImageInfoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IImageInfoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IImageInfoDo
	Not(conds ...gen.Condition) IImageInfoDo
	Or(conds ...gen.Condition) IImageInfoDo
	Select(conds ...field.Expr) IImageInfoDo
	Where(conds ...gen.Condition) IImageInfoDo
	Order(conds ...field.Expr) IImageInfoDo
	Distinct(cols ...field.Expr) IImageInfoDo
	Omit(cols ...field.Expr) IImageInfoDo
	Join(table schema.Tabler, on ...field.Expr) IImageInfoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IImageInfoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IImageInfoDo
	Group(cols ...field.Expr) IImageInfoDo
	Having(conds ...gen.Condition) IImageInfoDo
	Limit(limit int) IImageInfoDo
	Offset(offset int) IImageInfoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IImageInfoDo
	Unscoped() IImageInfoDo
	Create(values ...*model.ImageInfo) error
	CreateInBatches(values []*model.ImageInfo, batchSize int) error
	Save(values ...*model.ImageInfo) error
	First() (*model.ImageInfo, error)
	Take() (*model.ImageInfo, error)
	Last() (*model.ImageInfo, error)
	Find() ([]*model.ImageInfo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ImageInfo, err error)
	FindInBatches(result *[]*model.ImageInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ImageInfo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IImageInfoDo
	Assign(attrs ...field.AssignExpr) IImageInfoDo
	Joins(fields ...field.RelationField) IImageInfoDo
	Preload(fields ...field.RelationField) IImageInfoDo
	FirstOrInit() (*model.ImageInfo, error)
	FirstOrCreate() (*model.ImageInfo, error)
	FindByPage(offset int, limit int) (result []*model.ImageInfo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IImageInfoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (i imageInfoDo) Debug() IImageInfoDo {
	return i.withDO(i.DO.Debug())
}

func (i imageInfoDo) WithContext(ctx context.Context) IImageInfoDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i imageInfoDo) ReadDB() IImageInfoDo {
	return i.Clauses(dbresolver.Read)
}

func (i imageInfoDo) WriteDB() IImageInfoDo {
	return i.Clauses(dbresolver.Write)
}

func (i imageInfoDo) Session(config *gorm.Session) IImageInfoDo {
	return i.withDO(i.DO.Session(config))
}

func (i imageInfoDo) Clauses(conds ...clause.Expression) IImageInfoDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i imageInfoDo) Returning(value interface{}, columns ...string) IImageInfoDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i imageInfoDo) Not(conds ...gen.Condition) IImageInfoDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i imageInfoDo) Or(conds ...gen.Condition) IImageInfoDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i imageInfoDo) Select(conds ...field.Expr) IImageInfoDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i imageInfoDo) Where(conds ...gen.Condition) IImageInfoDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i imageInfoDo) Order(conds ...field.Expr) IImageInfoDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i imageInfoDo) Distinct(cols ...field.Expr) IImageInfoDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i imageInfoDo) Omit(cols ...field.Expr) IImageInfoDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i imageInfoDo) Join(table schema.Tabler, on ...field.Expr) IImageInfoDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i imageInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IImageInfoDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i imageInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) IImageInfoDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i imageInfoDo) Group(cols ...field.Expr) IImageInfoDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i imageInfoDo) Having(conds ...gen.Condition) IImageInfoDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i imageInfoDo) Limit(limit int) IImageInfoDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i imageInfoDo) Offset(offset int) IImageInfoDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i imageInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IImageInfoDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i imageInfoDo) Unscoped() IImageInfoDo {
	return i.withDO(i.DO.Unscoped())
}

func (i imageInfoDo) Create(values ...*model.ImageInfo) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i imageInfoDo) CreateInBatches(values []*model.ImageInfo, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i imageInfoDo) Save(values ...*model.ImageInfo) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i imageInfoDo) First() (*model.ImageInfo, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ImageInfo), nil
	}
}

func (i imageInfoDo) Take() (*model.ImageInfo, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ImageInfo), nil
	}
}

func (i imageInfoDo) Last() (*model.ImageInfo, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ImageInfo), nil
	}
}

func (i imageInfoDo) Find() ([]*model.ImageInfo, error) {
	result, err := i.DO.Find()
	return result.([]*model.ImageInfo), err
}

func (i imageInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ImageInfo, err error) {
	buf := make([]*model.ImageInfo, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i imageInfoDo) FindInBatches(result *[]*model.ImageInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i imageInfoDo) Attrs(attrs ...field.AssignExpr) IImageInfoDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i imageInfoDo) Assign(attrs ...field.AssignExpr) IImageInfoDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i imageInfoDo) Joins(fields ...field.RelationField) IImageInfoDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i imageInfoDo) Preload(fields ...field.RelationField) IImageInfoDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i imageInfoDo) FirstOrInit() (*model.ImageInfo, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ImageInfo), nil
	}
}

func (i imageInfoDo) FirstOrCreate() (*model.ImageInfo, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ImageInfo), nil
	}
}

func (i imageInfoDo) FindByPage(offset int, limit int) (result []*model.ImageInfo, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i imageInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i imageInfoDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i imageInfoDo) Delete(models ...*model.ImageInfo) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *imageInfoDo) withDO(do gen.Dao) *imageInfoDo {
	i.DO = *do.(*gen.DO)
	return i
}

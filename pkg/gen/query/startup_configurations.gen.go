// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/gen/model"
)

func newStartupConfigurations(db *gorm.DB, opts ...gen.DOOption) startupConfigurations {
	_startupConfigurations := startupConfigurations{}

	_startupConfigurations.startupConfigurationsDo.UseDB(db, opts...)
	_startupConfigurations.startupConfigurationsDo.UseModel(&model.StartupConfigurations{})

	tableName := _startupConfigurations.startupConfigurationsDo.TableName()
	_startupConfigurations.ALL = field.NewAsterisk(tableName)
	_startupConfigurations.ID = field.NewInt64(tableName, "id")
	_startupConfigurations.CreatedAt = field.NewTime(tableName, "created_at")
	_startupConfigurations.UpdatedAt = field.NewTime(tableName, "updated_at")
	_startupConfigurations.Deleted = field.NewBool(tableName, "deleted")
	_startupConfigurations.DbVersion = field.NewInt64(tableName, "db_version")
	_startupConfigurations.ProjectID = field.NewInt64(tableName, "project_id")
	_startupConfigurations.TenantID = field.NewInt64(tableName, "tenant_id")
	_startupConfigurations.ProjectConfigurationID = field.NewInt64(tableName, "project_configuration_id")
	_startupConfigurations.UUID = field.NewString(tableName, "uuid")
	_startupConfigurations.Name = field.NewString(tableName, "name")
	_startupConfigurations.ImageConfigID = field.NewInt64(tableName, "image_config_id")
	_startupConfigurations.ImageConfigCt = field.NewInt64(tableName, "image_config_ct")
	_startupConfigurations.ImageTag = field.NewString(tableName, "image_tag")

	_startupConfigurations.fillFieldMap()

	return _startupConfigurations
}

// startupConfigurations 启动配置
type startupConfigurations struct {
	startupConfigurationsDo

	ALL                    field.Asterisk
	ID                     field.Int64
	CreatedAt              field.Time   // 创建时间
	UpdatedAt              field.Time   // 更新时间
	Deleted                field.Bool   // 是否已删除
	DbVersion              field.Int64  // 数据库版本用于加锁
	ProjectID              field.Int64  // 所属项目ID
	TenantID               field.Int64  // 所属租户ID
	ProjectConfigurationID field.Int64  // 所属项目配置ID
	UUID                   field.String // UUID
	Name                   field.String // 名称
	ImageConfigID          field.Int64  // 镜像配置ID
	ImageConfigCt          field.Int64  // 镜像配置创建的时间
	ImageTag               field.String // 镜像标签

	fieldMap map[string]field.Expr
}

func (s startupConfigurations) Table(newTableName string) *startupConfigurations {
	s.startupConfigurationsDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s startupConfigurations) As(alias string) *startupConfigurations {
	s.startupConfigurationsDo.DO = *(s.startupConfigurationsDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *startupConfigurations) updateTableName(table string) *startupConfigurations {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.Deleted = field.NewBool(table, "deleted")
	s.DbVersion = field.NewInt64(table, "db_version")
	s.ProjectID = field.NewInt64(table, "project_id")
	s.TenantID = field.NewInt64(table, "tenant_id")
	s.ProjectConfigurationID = field.NewInt64(table, "project_configuration_id")
	s.UUID = field.NewString(table, "uuid")
	s.Name = field.NewString(table, "name")
	s.ImageConfigID = field.NewInt64(table, "image_config_id")
	s.ImageConfigCt = field.NewInt64(table, "image_config_ct")
	s.ImageTag = field.NewString(table, "image_tag")

	s.fillFieldMap()

	return s
}

func (s *startupConfigurations) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *startupConfigurations) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 13)
	s.fieldMap["id"] = s.ID
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted"] = s.Deleted
	s.fieldMap["db_version"] = s.DbVersion
	s.fieldMap["project_id"] = s.ProjectID
	s.fieldMap["tenant_id"] = s.TenantID
	s.fieldMap["project_configuration_id"] = s.ProjectConfigurationID
	s.fieldMap["uuid"] = s.UUID
	s.fieldMap["name"] = s.Name
	s.fieldMap["image_config_id"] = s.ImageConfigID
	s.fieldMap["image_config_ct"] = s.ImageConfigCt
	s.fieldMap["image_tag"] = s.ImageTag
}

func (s startupConfigurations) clone(db *gorm.DB) startupConfigurations {
	s.startupConfigurationsDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s startupConfigurations) replaceDB(db *gorm.DB) startupConfigurations {
	s.startupConfigurationsDo.ReplaceDB(db)
	return s
}

type startupConfigurationsDo struct{ gen.DO }

type IStartupConfigurationsDo interface {
	gen.SubQuery
	Debug() IStartupConfigurationsDo
	WithContext(ctx context.Context) IStartupConfigurationsDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStartupConfigurationsDo
	WriteDB() IStartupConfigurationsDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStartupConfigurationsDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStartupConfigurationsDo
	Not(conds ...gen.Condition) IStartupConfigurationsDo
	Or(conds ...gen.Condition) IStartupConfigurationsDo
	Select(conds ...field.Expr) IStartupConfigurationsDo
	Where(conds ...gen.Condition) IStartupConfigurationsDo
	Order(conds ...field.Expr) IStartupConfigurationsDo
	Distinct(cols ...field.Expr) IStartupConfigurationsDo
	Omit(cols ...field.Expr) IStartupConfigurationsDo
	Join(table schema.Tabler, on ...field.Expr) IStartupConfigurationsDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStartupConfigurationsDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStartupConfigurationsDo
	Group(cols ...field.Expr) IStartupConfigurationsDo
	Having(conds ...gen.Condition) IStartupConfigurationsDo
	Limit(limit int) IStartupConfigurationsDo
	Offset(offset int) IStartupConfigurationsDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStartupConfigurationsDo
	Unscoped() IStartupConfigurationsDo
	Create(values ...*model.StartupConfigurations) error
	CreateInBatches(values []*model.StartupConfigurations, batchSize int) error
	Save(values ...*model.StartupConfigurations) error
	First() (*model.StartupConfigurations, error)
	Take() (*model.StartupConfigurations, error)
	Last() (*model.StartupConfigurations, error)
	Find() ([]*model.StartupConfigurations, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StartupConfigurations, err error)
	FindInBatches(result *[]*model.StartupConfigurations, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StartupConfigurations) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStartupConfigurationsDo
	Assign(attrs ...field.AssignExpr) IStartupConfigurationsDo
	Joins(fields ...field.RelationField) IStartupConfigurationsDo
	Preload(fields ...field.RelationField) IStartupConfigurationsDo
	FirstOrInit() (*model.StartupConfigurations, error)
	FirstOrCreate() (*model.StartupConfigurations, error)
	FindByPage(offset int, limit int) (result []*model.StartupConfigurations, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStartupConfigurationsDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s startupConfigurationsDo) Debug() IStartupConfigurationsDo {
	return s.withDO(s.DO.Debug())
}

func (s startupConfigurationsDo) WithContext(ctx context.Context) IStartupConfigurationsDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s startupConfigurationsDo) ReadDB() IStartupConfigurationsDo {
	return s.Clauses(dbresolver.Read)
}

func (s startupConfigurationsDo) WriteDB() IStartupConfigurationsDo {
	return s.Clauses(dbresolver.Write)
}

func (s startupConfigurationsDo) Session(config *gorm.Session) IStartupConfigurationsDo {
	return s.withDO(s.DO.Session(config))
}

func (s startupConfigurationsDo) Clauses(conds ...clause.Expression) IStartupConfigurationsDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s startupConfigurationsDo) Returning(value interface{}, columns ...string) IStartupConfigurationsDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s startupConfigurationsDo) Not(conds ...gen.Condition) IStartupConfigurationsDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s startupConfigurationsDo) Or(conds ...gen.Condition) IStartupConfigurationsDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s startupConfigurationsDo) Select(conds ...field.Expr) IStartupConfigurationsDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s startupConfigurationsDo) Where(conds ...gen.Condition) IStartupConfigurationsDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s startupConfigurationsDo) Order(conds ...field.Expr) IStartupConfigurationsDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s startupConfigurationsDo) Distinct(cols ...field.Expr) IStartupConfigurationsDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s startupConfigurationsDo) Omit(cols ...field.Expr) IStartupConfigurationsDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s startupConfigurationsDo) Join(table schema.Tabler, on ...field.Expr) IStartupConfigurationsDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s startupConfigurationsDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStartupConfigurationsDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s startupConfigurationsDo) RightJoin(table schema.Tabler, on ...field.Expr) IStartupConfigurationsDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s startupConfigurationsDo) Group(cols ...field.Expr) IStartupConfigurationsDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s startupConfigurationsDo) Having(conds ...gen.Condition) IStartupConfigurationsDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s startupConfigurationsDo) Limit(limit int) IStartupConfigurationsDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s startupConfigurationsDo) Offset(offset int) IStartupConfigurationsDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s startupConfigurationsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStartupConfigurationsDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s startupConfigurationsDo) Unscoped() IStartupConfigurationsDo {
	return s.withDO(s.DO.Unscoped())
}

func (s startupConfigurationsDo) Create(values ...*model.StartupConfigurations) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s startupConfigurationsDo) CreateInBatches(values []*model.StartupConfigurations, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s startupConfigurationsDo) Save(values ...*model.StartupConfigurations) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s startupConfigurationsDo) First() (*model.StartupConfigurations, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StartupConfigurations), nil
	}
}

func (s startupConfigurationsDo) Take() (*model.StartupConfigurations, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StartupConfigurations), nil
	}
}

func (s startupConfigurationsDo) Last() (*model.StartupConfigurations, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StartupConfigurations), nil
	}
}

func (s startupConfigurationsDo) Find() ([]*model.StartupConfigurations, error) {
	result, err := s.DO.Find()
	return result.([]*model.StartupConfigurations), err
}

func (s startupConfigurationsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StartupConfigurations, err error) {
	buf := make([]*model.StartupConfigurations, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s startupConfigurationsDo) FindInBatches(result *[]*model.StartupConfigurations, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s startupConfigurationsDo) Attrs(attrs ...field.AssignExpr) IStartupConfigurationsDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s startupConfigurationsDo) Assign(attrs ...field.AssignExpr) IStartupConfigurationsDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s startupConfigurationsDo) Joins(fields ...field.RelationField) IStartupConfigurationsDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s startupConfigurationsDo) Preload(fields ...field.RelationField) IStartupConfigurationsDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s startupConfigurationsDo) FirstOrInit() (*model.StartupConfigurations, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StartupConfigurations), nil
	}
}

func (s startupConfigurationsDo) FirstOrCreate() (*model.StartupConfigurations, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StartupConfigurations), nil
	}
}

func (s startupConfigurationsDo) FindByPage(offset int, limit int) (result []*model.StartupConfigurations, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s startupConfigurationsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s startupConfigurationsDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s startupConfigurationsDo) Delete(models ...*model.StartupConfigurations) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *startupConfigurationsDo) withDO(do gen.Dao) *startupConfigurationsDo {
	s.DO = *do.(*gen.DO)
	return s
}

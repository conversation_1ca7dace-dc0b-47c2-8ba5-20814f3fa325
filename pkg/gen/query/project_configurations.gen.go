// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/gen/model"
)

func newProjectConfigurations(db *gorm.DB, opts ...gen.DOOption) projectConfigurations {
	_projectConfigurations := projectConfigurations{}

	_projectConfigurations.projectConfigurationsDo.UseDB(db, opts...)
	_projectConfigurations.projectConfigurationsDo.UseModel(&model.ProjectConfigurations{})

	tableName := _projectConfigurations.projectConfigurationsDo.TableName()
	_projectConfigurations.ALL = field.NewAsterisk(tableName)
	_projectConfigurations.ID = field.NewInt64(tableName, "id")
	_projectConfigurations.CreatedAt = field.NewTime(tableName, "created_at")
	_projectConfigurations.UpdatedAt = field.NewTime(tableName, "updated_at")
	_projectConfigurations.Deleted = field.NewBool(tableName, "deleted")
	_projectConfigurations.DbVersion = field.NewInt64(tableName, "db_version")
	_projectConfigurations.ProjectID = field.NewInt64(tableName, "project_id")
	_projectConfigurations.TenantID = field.NewInt64(tableName, "tenant_id")
	_projectConfigurations.ServerTimeout = field.NewInt64(tableName, "server_timeout")
	_projectConfigurations.ServerOs = field.NewString(tableName, "server_os")
	_projectConfigurations.CallbackURL = field.NewString(tableName, "callback_url")

	_projectConfigurations.fillFieldMap()

	return _projectConfigurations
}

// projectConfigurations 项目配置
type projectConfigurations struct {
	projectConfigurationsDo

	ALL           field.Asterisk
	ID            field.Int64
	CreatedAt     field.Time   // 创建时间
	UpdatedAt     field.Time   // 更新时间
	Deleted       field.Bool   // 是否已删除
	DbVersion     field.Int64  // 数据库版本用于加锁
	ProjectID     field.Int64  // 所属项目ID
	TenantID      field.Int64  // 所属租户ID
	ServerTimeout field.Int64  // 服务器超时时长
	ServerOs      field.String // 服务器操作系统
	CallbackURL   field.String // 分配完成后回调URL

	fieldMap map[string]field.Expr
}

func (p projectConfigurations) Table(newTableName string) *projectConfigurations {
	p.projectConfigurationsDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p projectConfigurations) As(alias string) *projectConfigurations {
	p.projectConfigurationsDo.DO = *(p.projectConfigurationsDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *projectConfigurations) updateTableName(table string) *projectConfigurations {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.Deleted = field.NewBool(table, "deleted")
	p.DbVersion = field.NewInt64(table, "db_version")
	p.ProjectID = field.NewInt64(table, "project_id")
	p.TenantID = field.NewInt64(table, "tenant_id")
	p.ServerTimeout = field.NewInt64(table, "server_timeout")
	p.ServerOs = field.NewString(table, "server_os")
	p.CallbackURL = field.NewString(table, "callback_url")

	p.fillFieldMap()

	return p
}

func (p *projectConfigurations) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *projectConfigurations) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 10)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted"] = p.Deleted
	p.fieldMap["db_version"] = p.DbVersion
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["tenant_id"] = p.TenantID
	p.fieldMap["server_timeout"] = p.ServerTimeout
	p.fieldMap["server_os"] = p.ServerOs
	p.fieldMap["callback_url"] = p.CallbackURL
}

func (p projectConfigurations) clone(db *gorm.DB) projectConfigurations {
	p.projectConfigurationsDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p projectConfigurations) replaceDB(db *gorm.DB) projectConfigurations {
	p.projectConfigurationsDo.ReplaceDB(db)
	return p
}

type projectConfigurationsDo struct{ gen.DO }

type IProjectConfigurationsDo interface {
	gen.SubQuery
	Debug() IProjectConfigurationsDo
	WithContext(ctx context.Context) IProjectConfigurationsDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IProjectConfigurationsDo
	WriteDB() IProjectConfigurationsDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IProjectConfigurationsDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IProjectConfigurationsDo
	Not(conds ...gen.Condition) IProjectConfigurationsDo
	Or(conds ...gen.Condition) IProjectConfigurationsDo
	Select(conds ...field.Expr) IProjectConfigurationsDo
	Where(conds ...gen.Condition) IProjectConfigurationsDo
	Order(conds ...field.Expr) IProjectConfigurationsDo
	Distinct(cols ...field.Expr) IProjectConfigurationsDo
	Omit(cols ...field.Expr) IProjectConfigurationsDo
	Join(table schema.Tabler, on ...field.Expr) IProjectConfigurationsDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IProjectConfigurationsDo
	RightJoin(table schema.Tabler, on ...field.Expr) IProjectConfigurationsDo
	Group(cols ...field.Expr) IProjectConfigurationsDo
	Having(conds ...gen.Condition) IProjectConfigurationsDo
	Limit(limit int) IProjectConfigurationsDo
	Offset(offset int) IProjectConfigurationsDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IProjectConfigurationsDo
	Unscoped() IProjectConfigurationsDo
	Create(values ...*model.ProjectConfigurations) error
	CreateInBatches(values []*model.ProjectConfigurations, batchSize int) error
	Save(values ...*model.ProjectConfigurations) error
	First() (*model.ProjectConfigurations, error)
	Take() (*model.ProjectConfigurations, error)
	Last() (*model.ProjectConfigurations, error)
	Find() ([]*model.ProjectConfigurations, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ProjectConfigurations, err error)
	FindInBatches(result *[]*model.ProjectConfigurations, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ProjectConfigurations) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IProjectConfigurationsDo
	Assign(attrs ...field.AssignExpr) IProjectConfigurationsDo
	Joins(fields ...field.RelationField) IProjectConfigurationsDo
	Preload(fields ...field.RelationField) IProjectConfigurationsDo
	FirstOrInit() (*model.ProjectConfigurations, error)
	FirstOrCreate() (*model.ProjectConfigurations, error)
	FindByPage(offset int, limit int) (result []*model.ProjectConfigurations, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IProjectConfigurationsDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p projectConfigurationsDo) Debug() IProjectConfigurationsDo {
	return p.withDO(p.DO.Debug())
}

func (p projectConfigurationsDo) WithContext(ctx context.Context) IProjectConfigurationsDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p projectConfigurationsDo) ReadDB() IProjectConfigurationsDo {
	return p.Clauses(dbresolver.Read)
}

func (p projectConfigurationsDo) WriteDB() IProjectConfigurationsDo {
	return p.Clauses(dbresolver.Write)
}

func (p projectConfigurationsDo) Session(config *gorm.Session) IProjectConfigurationsDo {
	return p.withDO(p.DO.Session(config))
}

func (p projectConfigurationsDo) Clauses(conds ...clause.Expression) IProjectConfigurationsDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p projectConfigurationsDo) Returning(value interface{}, columns ...string) IProjectConfigurationsDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p projectConfigurationsDo) Not(conds ...gen.Condition) IProjectConfigurationsDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p projectConfigurationsDo) Or(conds ...gen.Condition) IProjectConfigurationsDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p projectConfigurationsDo) Select(conds ...field.Expr) IProjectConfigurationsDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p projectConfigurationsDo) Where(conds ...gen.Condition) IProjectConfigurationsDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p projectConfigurationsDo) Order(conds ...field.Expr) IProjectConfigurationsDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p projectConfigurationsDo) Distinct(cols ...field.Expr) IProjectConfigurationsDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p projectConfigurationsDo) Omit(cols ...field.Expr) IProjectConfigurationsDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p projectConfigurationsDo) Join(table schema.Tabler, on ...field.Expr) IProjectConfigurationsDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p projectConfigurationsDo) LeftJoin(table schema.Tabler, on ...field.Expr) IProjectConfigurationsDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p projectConfigurationsDo) RightJoin(table schema.Tabler, on ...field.Expr) IProjectConfigurationsDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p projectConfigurationsDo) Group(cols ...field.Expr) IProjectConfigurationsDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p projectConfigurationsDo) Having(conds ...gen.Condition) IProjectConfigurationsDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p projectConfigurationsDo) Limit(limit int) IProjectConfigurationsDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p projectConfigurationsDo) Offset(offset int) IProjectConfigurationsDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p projectConfigurationsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IProjectConfigurationsDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p projectConfigurationsDo) Unscoped() IProjectConfigurationsDo {
	return p.withDO(p.DO.Unscoped())
}

func (p projectConfigurationsDo) Create(values ...*model.ProjectConfigurations) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p projectConfigurationsDo) CreateInBatches(values []*model.ProjectConfigurations, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p projectConfigurationsDo) Save(values ...*model.ProjectConfigurations) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p projectConfigurationsDo) First() (*model.ProjectConfigurations, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProjectConfigurations), nil
	}
}

func (p projectConfigurationsDo) Take() (*model.ProjectConfigurations, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProjectConfigurations), nil
	}
}

func (p projectConfigurationsDo) Last() (*model.ProjectConfigurations, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProjectConfigurations), nil
	}
}

func (p projectConfigurationsDo) Find() ([]*model.ProjectConfigurations, error) {
	result, err := p.DO.Find()
	return result.([]*model.ProjectConfigurations), err
}

func (p projectConfigurationsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ProjectConfigurations, err error) {
	buf := make([]*model.ProjectConfigurations, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p projectConfigurationsDo) FindInBatches(result *[]*model.ProjectConfigurations, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p projectConfigurationsDo) Attrs(attrs ...field.AssignExpr) IProjectConfigurationsDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p projectConfigurationsDo) Assign(attrs ...field.AssignExpr) IProjectConfigurationsDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p projectConfigurationsDo) Joins(fields ...field.RelationField) IProjectConfigurationsDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p projectConfigurationsDo) Preload(fields ...field.RelationField) IProjectConfigurationsDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p projectConfigurationsDo) FirstOrInit() (*model.ProjectConfigurations, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProjectConfigurations), nil
	}
}

func (p projectConfigurationsDo) FirstOrCreate() (*model.ProjectConfigurations, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ProjectConfigurations), nil
	}
}

func (p projectConfigurationsDo) FindByPage(offset int, limit int) (result []*model.ProjectConfigurations, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p projectConfigurationsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p projectConfigurationsDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p projectConfigurationsDo) Delete(models ...*model.ProjectConfigurations) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *projectConfigurationsDo) withDO(do gen.Dao) *projectConfigurationsDo {
	p.DO = *do.(*gen.DO)
	return p
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/gen/model"
)

func newCluster(db *gorm.DB, opts ...gen.DOOption) cluster {
	_cluster := cluster{}

	_cluster.clusterDo.UseDB(db, opts...)
	_cluster.clusterDo.UseModel(&model.Cluster{})

	tableName := _cluster.clusterDo.TableName()
	_cluster.ALL = field.NewAsterisk(tableName)
	_cluster.ID = field.NewInt64(tableName, "id")
	_cluster.CreatedAt = field.NewTime(tableName, "created_at")
	_cluster.UpdatedAt = field.NewTime(tableName, "updated_at")
	_cluster.Deleted = field.NewBool(tableName, "deleted")
	_cluster.DbVersion = field.NewInt64(tableName, "db_version")
	_cluster.MasterIP = field.NewString(tableName, "master_ip")
	_cluster.Port = field.NewInt64(tableName, "port")
	_cluster.PodSubnet = field.NewString(tableName, "pod_subnet")
	_cluster.ServiceSubnet = field.NewString(tableName, "service_subnet")
	_cluster.Name = field.NewString(tableName, "name")
	_cluster.ImageRepository = field.NewString(tableName, "image_repository")
	_cluster.WorkerIps = field.NewString(tableName, "worker_ips")
	_cluster.Region = field.NewString(tableName, "region")
	_cluster.Zone = field.NewString(tableName, "zone")
	_cluster.ClusterConfigName = field.NewString(tableName, "cluster_config_name")
	_cluster.ClusterConfigPath = field.NewString(tableName, "cluster_config_path")

	_cluster.fillFieldMap()

	return _cluster
}

// cluster 集群信息表
type cluster struct {
	clusterDo

	ALL               field.Asterisk
	ID                field.Int64
	CreatedAt         field.Time   // 创建时间
	UpdatedAt         field.Time   // 更新时间
	Deleted           field.Bool   // 是否已删除
	DbVersion         field.Int64  // 数据库版本用于加锁
	MasterIP          field.String // 主节点ip
	Port              field.Int64  // 端口
	PodSubnet         field.String // pod网段地址
	ServiceSubnet     field.String // service网段
	Name              field.String // 集群名称
	ImageRepository   field.String // 镜像库
	WorkerIps         field.String // 工作节点ip
	Region            field.String // 地域
	Zone              field.String // 地区
	ClusterConfigName field.String // 集群配置名称
	ClusterConfigPath field.String // 集群配置路径

	fieldMap map[string]field.Expr
}

func (c cluster) Table(newTableName string) *cluster {
	c.clusterDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c cluster) As(alias string) *cluster {
	c.clusterDo.DO = *(c.clusterDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *cluster) updateTableName(table string) *cluster {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")
	c.Deleted = field.NewBool(table, "deleted")
	c.DbVersion = field.NewInt64(table, "db_version")
	c.MasterIP = field.NewString(table, "master_ip")
	c.Port = field.NewInt64(table, "port")
	c.PodSubnet = field.NewString(table, "pod_subnet")
	c.ServiceSubnet = field.NewString(table, "service_subnet")
	c.Name = field.NewString(table, "name")
	c.ImageRepository = field.NewString(table, "image_repository")
	c.WorkerIps = field.NewString(table, "worker_ips")
	c.Region = field.NewString(table, "region")
	c.Zone = field.NewString(table, "zone")
	c.ClusterConfigName = field.NewString(table, "cluster_config_name")
	c.ClusterConfigPath = field.NewString(table, "cluster_config_path")

	c.fillFieldMap()

	return c
}

func (c *cluster) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *cluster) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 16)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted"] = c.Deleted
	c.fieldMap["db_version"] = c.DbVersion
	c.fieldMap["master_ip"] = c.MasterIP
	c.fieldMap["port"] = c.Port
	c.fieldMap["pod_subnet"] = c.PodSubnet
	c.fieldMap["service_subnet"] = c.ServiceSubnet
	c.fieldMap["name"] = c.Name
	c.fieldMap["image_repository"] = c.ImageRepository
	c.fieldMap["worker_ips"] = c.WorkerIps
	c.fieldMap["region"] = c.Region
	c.fieldMap["zone"] = c.Zone
	c.fieldMap["cluster_config_name"] = c.ClusterConfigName
	c.fieldMap["cluster_config_path"] = c.ClusterConfigPath
}

func (c cluster) clone(db *gorm.DB) cluster {
	c.clusterDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c cluster) replaceDB(db *gorm.DB) cluster {
	c.clusterDo.ReplaceDB(db)
	return c
}

type clusterDo struct{ gen.DO }

type IClusterDo interface {
	gen.SubQuery
	Debug() IClusterDo
	WithContext(ctx context.Context) IClusterDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IClusterDo
	WriteDB() IClusterDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IClusterDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IClusterDo
	Not(conds ...gen.Condition) IClusterDo
	Or(conds ...gen.Condition) IClusterDo
	Select(conds ...field.Expr) IClusterDo
	Where(conds ...gen.Condition) IClusterDo
	Order(conds ...field.Expr) IClusterDo
	Distinct(cols ...field.Expr) IClusterDo
	Omit(cols ...field.Expr) IClusterDo
	Join(table schema.Tabler, on ...field.Expr) IClusterDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IClusterDo
	RightJoin(table schema.Tabler, on ...field.Expr) IClusterDo
	Group(cols ...field.Expr) IClusterDo
	Having(conds ...gen.Condition) IClusterDo
	Limit(limit int) IClusterDo
	Offset(offset int) IClusterDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IClusterDo
	Unscoped() IClusterDo
	Create(values ...*model.Cluster) error
	CreateInBatches(values []*model.Cluster, batchSize int) error
	Save(values ...*model.Cluster) error
	First() (*model.Cluster, error)
	Take() (*model.Cluster, error)
	Last() (*model.Cluster, error)
	Find() ([]*model.Cluster, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Cluster, err error)
	FindInBatches(result *[]*model.Cluster, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Cluster) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IClusterDo
	Assign(attrs ...field.AssignExpr) IClusterDo
	Joins(fields ...field.RelationField) IClusterDo
	Preload(fields ...field.RelationField) IClusterDo
	FirstOrInit() (*model.Cluster, error)
	FirstOrCreate() (*model.Cluster, error)
	FindByPage(offset int, limit int) (result []*model.Cluster, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IClusterDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c clusterDo) Debug() IClusterDo {
	return c.withDO(c.DO.Debug())
}

func (c clusterDo) WithContext(ctx context.Context) IClusterDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c clusterDo) ReadDB() IClusterDo {
	return c.Clauses(dbresolver.Read)
}

func (c clusterDo) WriteDB() IClusterDo {
	return c.Clauses(dbresolver.Write)
}

func (c clusterDo) Session(config *gorm.Session) IClusterDo {
	return c.withDO(c.DO.Session(config))
}

func (c clusterDo) Clauses(conds ...clause.Expression) IClusterDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c clusterDo) Returning(value interface{}, columns ...string) IClusterDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c clusterDo) Not(conds ...gen.Condition) IClusterDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c clusterDo) Or(conds ...gen.Condition) IClusterDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c clusterDo) Select(conds ...field.Expr) IClusterDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c clusterDo) Where(conds ...gen.Condition) IClusterDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c clusterDo) Order(conds ...field.Expr) IClusterDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c clusterDo) Distinct(cols ...field.Expr) IClusterDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c clusterDo) Omit(cols ...field.Expr) IClusterDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c clusterDo) Join(table schema.Tabler, on ...field.Expr) IClusterDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c clusterDo) LeftJoin(table schema.Tabler, on ...field.Expr) IClusterDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c clusterDo) RightJoin(table schema.Tabler, on ...field.Expr) IClusterDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c clusterDo) Group(cols ...field.Expr) IClusterDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c clusterDo) Having(conds ...gen.Condition) IClusterDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c clusterDo) Limit(limit int) IClusterDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c clusterDo) Offset(offset int) IClusterDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c clusterDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IClusterDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c clusterDo) Unscoped() IClusterDo {
	return c.withDO(c.DO.Unscoped())
}

func (c clusterDo) Create(values ...*model.Cluster) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c clusterDo) CreateInBatches(values []*model.Cluster, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c clusterDo) Save(values ...*model.Cluster) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c clusterDo) First() (*model.Cluster, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cluster), nil
	}
}

func (c clusterDo) Take() (*model.Cluster, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cluster), nil
	}
}

func (c clusterDo) Last() (*model.Cluster, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cluster), nil
	}
}

func (c clusterDo) Find() ([]*model.Cluster, error) {
	result, err := c.DO.Find()
	return result.([]*model.Cluster), err
}

func (c clusterDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Cluster, err error) {
	buf := make([]*model.Cluster, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c clusterDo) FindInBatches(result *[]*model.Cluster, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c clusterDo) Attrs(attrs ...field.AssignExpr) IClusterDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c clusterDo) Assign(attrs ...field.AssignExpr) IClusterDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c clusterDo) Joins(fields ...field.RelationField) IClusterDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c clusterDo) Preload(fields ...field.RelationField) IClusterDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c clusterDo) FirstOrInit() (*model.Cluster, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cluster), nil
	}
}

func (c clusterDo) FirstOrCreate() (*model.Cluster, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cluster), nil
	}
}

func (c clusterDo) FindByPage(offset int, limit int) (result []*model.Cluster, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c clusterDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c clusterDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c clusterDo) Delete(models ...*model.Cluster) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *clusterDo) withDO(do gen.Dao) *clusterDo {
	c.DO = *do.(*gen.DO)
	return c
}

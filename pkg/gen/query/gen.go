// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                     = new(Query)
	BaseImageConfig       *baseImageConfig
	Cloudaccounts         *cloudaccounts
	Cluster               *cluster
	ImageInfo             *imageInfo
	ProjectConfigurations *projectConfigurations
	StartupConfigurations *startupConfigurations
	UserRegion            *userRegion
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	BaseImageConfig = &Q.BaseImageConfig
	Cloudaccounts = &Q.Cloudaccounts
	Cluster = &Q.Cluster
	ImageInfo = &Q.ImageInfo
	ProjectConfigurations = &Q.ProjectConfigurations
	StartupConfigurations = &Q.StartupConfigurations
	UserRegion = &Q.UserRegion
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                    db,
		BaseImageConfig:       newBaseImageConfig(db, opts...),
		Cloudaccounts:         newCloudaccounts(db, opts...),
		Cluster:               newCluster(db, opts...),
		ImageInfo:             newImageInfo(db, opts...),
		ProjectConfigurations: newProjectConfigurations(db, opts...),
		StartupConfigurations: newStartupConfigurations(db, opts...),
		UserRegion:            newUserRegion(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	BaseImageConfig       baseImageConfig
	Cloudaccounts         cloudaccounts
	Cluster               cluster
	ImageInfo             imageInfo
	ProjectConfigurations projectConfigurations
	StartupConfigurations startupConfigurations
	UserRegion            userRegion
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		BaseImageConfig:       q.BaseImageConfig.clone(db),
		Cloudaccounts:         q.Cloudaccounts.clone(db),
		Cluster:               q.Cluster.clone(db),
		ImageInfo:             q.ImageInfo.clone(db),
		ProjectConfigurations: q.ProjectConfigurations.clone(db),
		StartupConfigurations: q.StartupConfigurations.clone(db),
		UserRegion:            q.UserRegion.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		BaseImageConfig:       q.BaseImageConfig.replaceDB(db),
		Cloudaccounts:         q.Cloudaccounts.replaceDB(db),
		Cluster:               q.Cluster.replaceDB(db),
		ImageInfo:             q.ImageInfo.replaceDB(db),
		ProjectConfigurations: q.ProjectConfigurations.replaceDB(db),
		StartupConfigurations: q.StartupConfigurations.replaceDB(db),
		UserRegion:            q.UserRegion.replaceDB(db),
	}
}

type queryCtx struct {
	BaseImageConfig       IBaseImageConfigDo
	Cloudaccounts         ICloudaccountsDo
	Cluster               IClusterDo
	ImageInfo             IImageInfoDo
	ProjectConfigurations IProjectConfigurationsDo
	StartupConfigurations IStartupConfigurationsDo
	UserRegion            IUserRegionDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		BaseImageConfig:       q.BaseImageConfig.WithContext(ctx),
		Cloudaccounts:         q.Cloudaccounts.WithContext(ctx),
		Cluster:               q.Cluster.WithContext(ctx),
		ImageInfo:             q.ImageInfo.WithContext(ctx),
		ProjectConfigurations: q.ProjectConfigurations.WithContext(ctx),
		StartupConfigurations: q.StartupConfigurations.WithContext(ctx),
		UserRegion:            q.UserRegion.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/gen/model"
)

func newCloudaccounts(db *gorm.DB, opts ...gen.DOOption) cloudaccounts {
	_cloudaccounts := cloudaccounts{}

	_cloudaccounts.cloudaccountsDo.UseDB(db, opts...)
	_cloudaccounts.cloudaccountsDo.UseModel(&model.Cloudaccounts{})

	tableName := _cloudaccounts.cloudaccountsDo.TableName()
	_cloudaccounts.ALL = field.NewAsterisk(tableName)
	_cloudaccounts.ID = field.NewInt64(tableName, "id")
	_cloudaccounts.CreatedAt = field.NewTime(tableName, "created_at")
	_cloudaccounts.UpdatedAt = field.NewTime(tableName, "updated_at")
	_cloudaccounts.Deleted = field.NewBool(tableName, "deleted")
	_cloudaccounts.DbVersion = field.NewInt64(tableName, "db_version")
	_cloudaccounts.Name = field.NewString(tableName, "name")
	_cloudaccounts.Description = field.NewString(tableName, "description")
	_cloudaccounts.Status = field.NewString(tableName, "status")
	_cloudaccounts.Enabled = field.NewBool(tableName, "enabled")
	_cloudaccounts.ProviderID = field.NewInt64(tableName, "provider_id")
	_cloudaccounts.AccountName = field.NewString(tableName, "account_name")
	_cloudaccounts.AccountType = field.NewString(tableName, "account_type")
	_cloudaccounts.Ak = field.NewString(tableName, "ak")
	_cloudaccounts.Sk = field.NewString(tableName, "sk")
	_cloudaccounts.Endpoint = field.NewString(tableName, "endpoint")
	_cloudaccounts.LastSyncAt = field.NewTime(tableName, "last_sync_at")
	_cloudaccounts.LastSyncError = field.NewString(tableName, "last_sync_error")
	_cloudaccounts.Options = field.NewString(tableName, "options")

	_cloudaccounts.fillFieldMap()

	return _cloudaccounts
}

// cloudaccounts 云账号
type cloudaccounts struct {
	cloudaccountsDo

	ALL           field.Asterisk
	ID            field.Int64
	CreatedAt     field.Time   // 创建时间
	UpdatedAt     field.Time   // 更新时间
	Deleted       field.Bool   // 是否已删除
	DbVersion     field.Int64  // 数据库版本用于加锁
	Name          field.String // 云账号名称
	Description   field.String // 账号描述
	Status        field.String // 状态
	Enabled       field.Bool   // 是否启用
	ProviderID    field.Int64  // 云服务提供商ID
	AccountName   field.String // 云账号名称
	AccountType   field.String // 账号类型
	Ak            field.String // Access Key
	Sk            field.String // Secret Key
	Endpoint      field.String // Endpoint
	LastSyncAt    field.Time   // 最后同步时间
	LastSyncError field.String // 最后同步错误
	Options       field.String // 其他配置

	fieldMap map[string]field.Expr
}

func (c cloudaccounts) Table(newTableName string) *cloudaccounts {
	c.cloudaccountsDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c cloudaccounts) As(alias string) *cloudaccounts {
	c.cloudaccountsDo.DO = *(c.cloudaccountsDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *cloudaccounts) updateTableName(table string) *cloudaccounts {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")
	c.Deleted = field.NewBool(table, "deleted")
	c.DbVersion = field.NewInt64(table, "db_version")
	c.Name = field.NewString(table, "name")
	c.Description = field.NewString(table, "description")
	c.Status = field.NewString(table, "status")
	c.Enabled = field.NewBool(table, "enabled")
	c.ProviderID = field.NewInt64(table, "provider_id")
	c.AccountName = field.NewString(table, "account_name")
	c.AccountType = field.NewString(table, "account_type")
	c.Ak = field.NewString(table, "ak")
	c.Sk = field.NewString(table, "sk")
	c.Endpoint = field.NewString(table, "endpoint")
	c.LastSyncAt = field.NewTime(table, "last_sync_at")
	c.LastSyncError = field.NewString(table, "last_sync_error")
	c.Options = field.NewString(table, "options")

	c.fillFieldMap()

	return c
}

func (c *cloudaccounts) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *cloudaccounts) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 18)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted"] = c.Deleted
	c.fieldMap["db_version"] = c.DbVersion
	c.fieldMap["name"] = c.Name
	c.fieldMap["description"] = c.Description
	c.fieldMap["status"] = c.Status
	c.fieldMap["enabled"] = c.Enabled
	c.fieldMap["provider_id"] = c.ProviderID
	c.fieldMap["account_name"] = c.AccountName
	c.fieldMap["account_type"] = c.AccountType
	c.fieldMap["ak"] = c.Ak
	c.fieldMap["sk"] = c.Sk
	c.fieldMap["endpoint"] = c.Endpoint
	c.fieldMap["last_sync_at"] = c.LastSyncAt
	c.fieldMap["last_sync_error"] = c.LastSyncError
	c.fieldMap["options"] = c.Options
}

func (c cloudaccounts) clone(db *gorm.DB) cloudaccounts {
	c.cloudaccountsDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c cloudaccounts) replaceDB(db *gorm.DB) cloudaccounts {
	c.cloudaccountsDo.ReplaceDB(db)
	return c
}

type cloudaccountsDo struct{ gen.DO }

type ICloudaccountsDo interface {
	gen.SubQuery
	Debug() ICloudaccountsDo
	WithContext(ctx context.Context) ICloudaccountsDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICloudaccountsDo
	WriteDB() ICloudaccountsDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICloudaccountsDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICloudaccountsDo
	Not(conds ...gen.Condition) ICloudaccountsDo
	Or(conds ...gen.Condition) ICloudaccountsDo
	Select(conds ...field.Expr) ICloudaccountsDo
	Where(conds ...gen.Condition) ICloudaccountsDo
	Order(conds ...field.Expr) ICloudaccountsDo
	Distinct(cols ...field.Expr) ICloudaccountsDo
	Omit(cols ...field.Expr) ICloudaccountsDo
	Join(table schema.Tabler, on ...field.Expr) ICloudaccountsDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICloudaccountsDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICloudaccountsDo
	Group(cols ...field.Expr) ICloudaccountsDo
	Having(conds ...gen.Condition) ICloudaccountsDo
	Limit(limit int) ICloudaccountsDo
	Offset(offset int) ICloudaccountsDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICloudaccountsDo
	Unscoped() ICloudaccountsDo
	Create(values ...*model.Cloudaccounts) error
	CreateInBatches(values []*model.Cloudaccounts, batchSize int) error
	Save(values ...*model.Cloudaccounts) error
	First() (*model.Cloudaccounts, error)
	Take() (*model.Cloudaccounts, error)
	Last() (*model.Cloudaccounts, error)
	Find() ([]*model.Cloudaccounts, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Cloudaccounts, err error)
	FindInBatches(result *[]*model.Cloudaccounts, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Cloudaccounts) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICloudaccountsDo
	Assign(attrs ...field.AssignExpr) ICloudaccountsDo
	Joins(fields ...field.RelationField) ICloudaccountsDo
	Preload(fields ...field.RelationField) ICloudaccountsDo
	FirstOrInit() (*model.Cloudaccounts, error)
	FirstOrCreate() (*model.Cloudaccounts, error)
	FindByPage(offset int, limit int) (result []*model.Cloudaccounts, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICloudaccountsDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c cloudaccountsDo) Debug() ICloudaccountsDo {
	return c.withDO(c.DO.Debug())
}

func (c cloudaccountsDo) WithContext(ctx context.Context) ICloudaccountsDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c cloudaccountsDo) ReadDB() ICloudaccountsDo {
	return c.Clauses(dbresolver.Read)
}

func (c cloudaccountsDo) WriteDB() ICloudaccountsDo {
	return c.Clauses(dbresolver.Write)
}

func (c cloudaccountsDo) Session(config *gorm.Session) ICloudaccountsDo {
	return c.withDO(c.DO.Session(config))
}

func (c cloudaccountsDo) Clauses(conds ...clause.Expression) ICloudaccountsDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c cloudaccountsDo) Returning(value interface{}, columns ...string) ICloudaccountsDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c cloudaccountsDo) Not(conds ...gen.Condition) ICloudaccountsDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c cloudaccountsDo) Or(conds ...gen.Condition) ICloudaccountsDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c cloudaccountsDo) Select(conds ...field.Expr) ICloudaccountsDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c cloudaccountsDo) Where(conds ...gen.Condition) ICloudaccountsDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c cloudaccountsDo) Order(conds ...field.Expr) ICloudaccountsDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c cloudaccountsDo) Distinct(cols ...field.Expr) ICloudaccountsDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c cloudaccountsDo) Omit(cols ...field.Expr) ICloudaccountsDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c cloudaccountsDo) Join(table schema.Tabler, on ...field.Expr) ICloudaccountsDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c cloudaccountsDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICloudaccountsDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c cloudaccountsDo) RightJoin(table schema.Tabler, on ...field.Expr) ICloudaccountsDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c cloudaccountsDo) Group(cols ...field.Expr) ICloudaccountsDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c cloudaccountsDo) Having(conds ...gen.Condition) ICloudaccountsDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c cloudaccountsDo) Limit(limit int) ICloudaccountsDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c cloudaccountsDo) Offset(offset int) ICloudaccountsDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c cloudaccountsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICloudaccountsDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c cloudaccountsDo) Unscoped() ICloudaccountsDo {
	return c.withDO(c.DO.Unscoped())
}

func (c cloudaccountsDo) Create(values ...*model.Cloudaccounts) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c cloudaccountsDo) CreateInBatches(values []*model.Cloudaccounts, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c cloudaccountsDo) Save(values ...*model.Cloudaccounts) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c cloudaccountsDo) First() (*model.Cloudaccounts, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cloudaccounts), nil
	}
}

func (c cloudaccountsDo) Take() (*model.Cloudaccounts, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cloudaccounts), nil
	}
}

func (c cloudaccountsDo) Last() (*model.Cloudaccounts, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cloudaccounts), nil
	}
}

func (c cloudaccountsDo) Find() ([]*model.Cloudaccounts, error) {
	result, err := c.DO.Find()
	return result.([]*model.Cloudaccounts), err
}

func (c cloudaccountsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Cloudaccounts, err error) {
	buf := make([]*model.Cloudaccounts, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c cloudaccountsDo) FindInBatches(result *[]*model.Cloudaccounts, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c cloudaccountsDo) Attrs(attrs ...field.AssignExpr) ICloudaccountsDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c cloudaccountsDo) Assign(attrs ...field.AssignExpr) ICloudaccountsDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c cloudaccountsDo) Joins(fields ...field.RelationField) ICloudaccountsDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c cloudaccountsDo) Preload(fields ...field.RelationField) ICloudaccountsDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c cloudaccountsDo) FirstOrInit() (*model.Cloudaccounts, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cloudaccounts), nil
	}
}

func (c cloudaccountsDo) FirstOrCreate() (*model.Cloudaccounts, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Cloudaccounts), nil
	}
}

func (c cloudaccountsDo) FindByPage(offset int, limit int) (result []*model.Cloudaccounts, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c cloudaccountsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c cloudaccountsDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c cloudaccountsDo) Delete(models ...*model.Cloudaccounts) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *cloudaccountsDo) withDO(do gen.Dao) *cloudaccountsDo {
	c.DO = *do.(*gen.DO)
	return c
}

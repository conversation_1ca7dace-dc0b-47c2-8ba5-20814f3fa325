package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/context"
	"github.com/gin-gonic/gin"
)

type AuthRequest struct {
	UUID   string `json:"uuid"`
	ToUUID string `json:"toUuid"`
	Ticket string `json:"ticket"`
}

type UserInfoResponse struct {
	Status  string           `json:"status"`
	Code    string           `json:"code"`
	Message string           `json:"message"`
	Data    context.UserInfo `json:"data"`
}

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Step 1: 获取 Authorization 头和 cookies
		auth := c.<PERSON>("Authorization")
		ticket, err := c.<PERSON>("cross_web_ticket")
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Missing cross_web_ticket cookie",
			})
			return
		}

		uuid, err := c.<PERSON>("cross_web_uuid")
		if err != nil {
			c.<PERSON>bortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Missing cross_web_uuid cookie",
			})
			return
		}

		if auth == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Missing Authorization header",
			})
			return
		}

		// Step 2: 构造验证请求
		authReq := AuthRequest{
			UUID:   uuid,
			ToUUID: uuid,
			Ticket: ticket,
		}

		jsonData, err := json.Marshal(authReq)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to process authentication request",
			})
			return
		}

		// Step 3: 发送验证请求
		req, err := http.NewRequest("POST", "https://api.meta.mg.xyz/spaceapi/am/user/userInfo", bytes.NewBuffer(jsonData))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to create authentication request",
			})
			return
		}

		req.Header.Set("Authorization", auth)
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to validate authentication",
			})
			return
		}
		defer resp.Body.Close()

		// Step 4: 处理响应
		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			c.AbortWithStatusJSON(resp.StatusCode, gin.H{
				"error":   "Authentication failed",
				"details": string(body),
			})
			return
		}

		// Step 5: 解析用户信息
		var userInfoResp UserInfoResponse
		if err := json.NewDecoder(resp.Body).Decode(&userInfoResp); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to parse user information",
			})
			return
		}

		// Step 6: 将用户信息存储到上下文中
		c.Set(context.UserInfoKey, userInfoResp.Data)

		// 验证成功，继续处理请求
		c.Next()
	}
}

// GetUserInfo 从上下文中获取用户信息的辅助函数
func GetUserInfo(c *gin.Context) (context.UserInfo, bool) {
	userInfo, exists := c.Get(context.UserInfoKey)
	if !exists {
		return context.UserInfo{}, false
	}
	return userInfo.(context.UserInfo), true
}

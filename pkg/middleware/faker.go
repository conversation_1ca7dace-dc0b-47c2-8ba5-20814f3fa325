package middleware

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/context"
	"github.com/gin-gonic/gin"
)

func FakerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !config.GlobalConfig.Faker.Enable {
			c.Next()
			return
		}

		// 设置faker数据
		context.SetUserInfo(c, context.UserInfo{
			UUID:     config.GlobalConfig.Faker.UserUUID,
			ID:       config.GlobalConfig.Faker.UserID,
			NickName: config.GlobalConfig.Faker.UserName,
			Email:    config.GlobalConfig.Faker.UserEmail,
			Mobile:   config.GlobalConfig.Faker.UserPhone,
		})
		context.SetTenantID(c, config.GlobalConfig.Faker.TenantID)
		context.SetProjectID(c, config.GlobalConfig.Faker.ProjectID)
		c.Next()
	}
}

package middleware

import (
	"runtime"
	"strconv"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/metrics"

	"github.com/gin-gonic/gin"
)

// Metrics 返回一个 Gin 中间件，用于收集 HTTP 请求指标
func Metrics() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path

		// 处理请求
		c.Next()

		// 计算请求处理时间
		duration := time.Since(start).Seconds()

		// 记录请求总数
		metrics.HTTPRequestsTotal.WithLabelValues(
			c.Request.Method,
			path,
			strconv.Itoa(c.Writer.Status()),
		).Inc()

		// 记录请求处理时间
		metrics.HTTPRequestDuration.WithLabelValues(
			c.Request.Method,
			path,
		).Observe(duration)

		// 更新系统指标
		updateSystemMetrics()
	}
}

// updateSystemMetrics 更新系统相关指标
func updateSystemMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 更新内存使用情况
	metrics.SystemMemoryUsage.WithLabelValues("alloc").Set(float64(m.Alloc))
	metrics.SystemMemoryUsage.WithLabelValues("total").Set(float64(m.TotalAlloc))
	metrics.SystemMemoryUsage.WithLabelValues("sys").Set(float64(m.Sys))
	metrics.SystemMemoryUsage.WithLabelValues("heap").Set(float64(m.HeapAlloc))

	// 更新 goroutine 数量
	metrics.SystemGoroutines.Set(float64(runtime.NumGoroutine()))
}

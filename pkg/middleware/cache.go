package middleware

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/cache"

	"github.com/gin-gonic/gin"
)

// CacheConfig 缓存配置
type CacheConfig struct {
	// 缓存时间
	Expiration time.Duration
	// 是否跳过缓存
	SkipCache func(c *gin.Context) bool
	// 缓存前缀
	Prefix string
}

// Cache 缓存中间件
func Cache(config CacheConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果设置了跳过缓存，则直接处理请求
		if config.SkipCache != nil && config.SkipCache(c) {
			c.Next()
			return
		}

		// 只缓存API请求
		if !strings.HasPrefix(c.Request.URL.Path, "/api/") {
			c.Next()
			return
		}

		// 生成缓存key
		key := generateCacheKey(c, config.Prefix)

		// 尝试从缓存获取数据
		ctx := context.Background()
		cachedData, err := cache.Client.Get(ctx, key).Result()
		if err == nil {
			// 缓存命中，直接返回
			var response map[string]interface{}
			if err := json.Unmarshal([]byte(cachedData), &response); err == nil {
				c.JSON(200, response)
				c.Abort()
				return
			}
		}

		// 创建一个自定义的ResponseWriter来捕获响应
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:           make([]byte, 0),
		}
		c.Writer = writer

		// 处理请求
		c.Next()

		// 如果状态码不是200，不缓存
		if c.Writer.Status() != 200 {
			return
		}

		// 将响应存入缓存
		if len(writer.body) > 0 {
			cache.Client.Set(ctx, key, string(writer.body), config.Expiration)
		}
	}
}

// responseWriter 自定义ResponseWriter
type responseWriter struct {
	gin.ResponseWriter
	body []byte
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body = append(w.body, b...)
	return w.ResponseWriter.Write(b)
}

// generateCacheKey 生成缓存key
func generateCacheKey(c *gin.Context, prefix string) string {
	// 组合请求信息
	data := fmt.Sprintf("%s:%s:%s", c.Request.Method, c.Request.URL.Path, c.Request.URL.RawQuery)

	// 计算MD5
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%s:%s", prefix, hex.EncodeToString(hash[:]))
}

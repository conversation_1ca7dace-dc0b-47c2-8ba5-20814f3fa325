package middleware

import (
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/logger"
	"github.com/google/uuid"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RequestIDKey is the key used to store request ID in gin context
const (
	RequestIDKey    = "request_id"
	RequestIDHeader = "X-Request-ID"
)

// Logger 返回一个 Gin 日志中间件
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 优先使用请求头中的 RequestID，如果没有则生成新的
		requestID := c.GetHeader(RequestIDHeader)
		if requestID == "" {
			requestID = uuid.New().String()
		}

		c.Set(RequestIDKey, requestID)
		c.Header(RequestIDHeader, requestID)

		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 请求处理完成后记录日志
		cost := time.Since(start)
		logger.Info("HTTP请求",
			zap.Int("status", c.Writer.Status()),
			zap.String("method", c.Request.Method),
			zap.String("path", path),
			zap.String("query", query),
			zap.String("ip", c.ClientIP()),
			zap.String("user-agent", c.Request.UserAgent()),
			zap.String("errors", c.Errors.ByType(gin.ErrorTypePrivate).String()),
			zap.Duration("cost", cost),
			zap.String("request_id", requestID),
		)
	}
}

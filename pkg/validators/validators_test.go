package validators

import (
	"reflect"
	"strings"
	"testing"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/validators/mocks"
	"github.com/golang/mock/gomock"
)

func TestValidateName(t *testing.T) {
	tests := []struct {
		name      string
		input     string
		minLength int
		maxLength int
		want      bool
	}{
		{
			name:      "有效名称_字母开头",
			input:     "abc123",
			minLength: 1,
			maxLength: 64,
			want:      true,
		},
		{
			name:      "有效名称_下划线开头",
			input:     "_test123",
			minLength: 1,
			maxLength: 64,
			want:      true,
		},
		{
			name:      "有效名称_包含连字符",
			input:     "test-123",
			minLength: 1,
			maxLength: 64,
			want:      true,
		},
		{
			name:      "无效名称_数字开头",
			input:     "123test",
			minLength: 1,
			maxLength: 64,
			want:      false,
		},
		{
			name:      "无效名称_特殊字符",
			input:     "test@123",
			minLength: 1,
			maxLength: 64,
			want:      false,
		},
		{
			name:      "无效名称_空字符串",
			input:     "",
			minLength: 1,
			maxLength: 64,
			want:      false,
		},
		{
			name:      "无效名称_长度超过最大值",
			input:     "a" + strings.Repeat("b", 64),
			minLength: 1,
			maxLength: 64,
			want:      false,
		},
		{
			name:      "有效名称_边界值_最小长度",
			input:     "a",
			minLength: 1,
			maxLength: 64,
			want:      true,
		},
		{
			name:      "有效名称_边界值_最大长度",
			input:     "a" + strings.Repeat("b", 62),
			minLength: 1,
			maxLength: 64,
			want:      true,
		},
		{
			name:      "无效效名称_有中文",
			input:     "a中",
			minLength: 1,
			maxLength: 64,
			want:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建 mock 控制器
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// 创建 mock FieldLevel
			mockFL := mocks.NewMockFieldLevel(ctrl)

			// 设置 Field() 方法的预期行为
			mockFL.EXPECT().
				Field().
				Return(reflect.ValueOf(tt.input)).
				Times(1) // 期望只调用一次

			// 执行测试
			got := validateName(mockFL, tt.minLength, tt.maxLength)
			if got != tt.want {
				t.Errorf("validateName() = %v, want %v, input: %v", got, tt.want, tt.input)
			}
		})
	}
}

func TestValidateUnique(t *testing.T) {
	tests := []struct {
		name  string
		input interface{}
		want  bool
	}{
		{
			name:  "字符串切片_无重复",
			input: []string{"a", "b", "c"},
			want:  true,
		},
		{
			name:  "字符串切片_有重复",
			input: []string{"a", "b", "a"},
			want:  false,
		},
		{
			name:  "整数切片_无重复",
			input: []int{1, 2, 3},
			want:  true,
		},
		{
			name:  "整数切片_有重复",
			input: []int{1, 2, 2},
			want:  false,
		},
		{
			name:  "空切片",
			input: []string{},
			want:  true,
		},
		{
			name:  "非切片类型",
			input: "not a slice",
			want:  false,
		},
		{
			name:  "接口切片_无重复",
			input: []interface{}{1, "a", true},
			want:  true,
		},
		{
			name:  "接口切片_有重复",
			input: []interface{}{1, "a", 1},
			want:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建 mock 控制器
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// 创建 mock FieldLevel
			mockFL := mocks.NewMockFieldLevel(ctrl)

			// 设置 Field() 方法的预期行为
			mockFL.EXPECT().
				Field().
				Return(reflect.ValueOf(tt.input)).
				Times(1)

			// 执行测试
			got := validateUnique(mockFL)
			if got != tt.want {
				t.Errorf("validateUnique() = %v, want %v, input: %v", got, tt.want, tt.input)
			}
		})
	}
}

package validators

import (
	"fmt"
	"os"
	"reflect"
	"regexp"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

// 验证器说明
// name_1_64 验证名称：以字母或下划线开头，可含数字、字母、连字符，长度大于1小于64个字符
// name_1_128 验证名称：以字母或下划线开头，可含数字、字母、连字符，长度大于1小于128个字符
// unique 验证切片中的元素是否唯一

// init 初始化自定义验证器
func init() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		// 注册资源命名验证器 长度大于1小于64个字符
		err := v.RegisterValidation("name_1_64", func(fl validator.FieldLevel) bool {
			return validateName(fl, 1, 64)
		})
		if err != nil {
			fmt.Println("注册资源命名验证器失败", err)
			os.Exit(1)
		}
		// 注册资源命名验证器 长度大于1小于128个字符
		err = v.RegisterValidation("name_1_128", func(fl validator.FieldLevel) bool {
			return validateName(fl, 1, 128)
		})
		if err != nil {
			fmt.Println("注册资源命名验证器失败", err)
			os.Exit(1)
		}
		// 添加唯一性验证器
		err = v.RegisterValidation("unique", validateUnique)
		if err != nil {
			fmt.Println("注册唯一性验证器失败", err)
			os.Exit(1)
		}
	}
}

// validateName 验证名称：以字母或下划线开头，可含数字、字母、连字符，长度小于64个字符
func validateName(fl validator.FieldLevel, minLength int, maxLength int) bool {
	name := fl.Field().String()

	// 检查长度
	if len(name) < minLength || len(name) >= maxLength {
		return false
	}

	// 使用正则表达式验证
	pattern := `^[a-zA-Z_][a-zA-Z0-9_-]*$`
	matched, _ := regexp.MatchString(pattern, name)
	return matched
}

// validateUnique 验证切片中的元素是否唯一
func validateUnique(fl validator.FieldLevel) bool {
	field := fl.Field()

	// 检查是否为切片类型
	if field.Kind() != reflect.Slice {
		return false
	}

	seen := make(map[any]struct{})
	for i := range field.Len() {
		value := field.Index(i).Interface()
		if _, ok := seen[value]; ok {
			return false // 发现重复值
		}
		seen[value] = struct{}{}
	}

	return true
}

// ValidateStruct 验证结构体并返回友好的错误信息
func ValidateStruct(s interface{}) error {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		if err := v.Struct(s); err != nil {
			return FormatValidationError(err)
		}
	}
	return nil
}

// FormatValidationError 格式化验证错误信息
func FormatValidationError(err error) error {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, validationError := range validationErrors {
			return formatSingleValidationError(validationError)
		}
	}
	return fmt.Errorf("验证失败: %w", err)
}

// formatSingleValidationError 格式化单个验证错误
func formatSingleValidationError(validationError validator.FieldError) error {
	namespace := validationError.Namespace()
	// field := validationError.StructField()
	tag := validationError.Tag()
	param := validationError.Param()
	value := validationError.Value()

	switch tag {
	case "required":
		return fmt.Errorf("字段 %s 是必填的", namespace)
	case "oneof":
		return fmt.Errorf("字段 %s 的值 '%v' 无效，支持的值: %s", namespace, value, param)
	case "min":
		if validationError.Kind() == reflect.Slice {
			return fmt.Errorf("字段 %s 至少需要 %s 个元素", namespace, param)
		}
		return fmt.Errorf("字段 %s 的最小值为 %s", namespace, param)
	case "max":
		if validationError.Kind() == reflect.Slice {
			return fmt.Errorf("字段 %s 最多允许 %s 个元素", namespace, param)
		}
		return fmt.Errorf("字段 %s 的最大值为 %s", namespace, param)
	case "email":
		return fmt.Errorf("字段 %s 必须是有效的邮箱地址", namespace)
	case "url":
		return fmt.Errorf("字段 %s 必须是有效的URL", namespace)
	case "unique":
		return fmt.Errorf("字段 %s 中不能有重复值", namespace)
	case "dive":
		return fmt.Errorf("字段 %s 中的元素验证失败", namespace)
	case "name_1_64":
		return fmt.Errorf("字段 %s 必须是1-64个字符的有效名称（字母或下划线开头，可含数字、字母、连字符）", namespace)
	case "name_1_128":
		return fmt.Errorf("字段 %s 必须是1-128个字符的有效名称（字母或下划线开头，可含数字、字母、连字符）", namespace)
	case "json":
		return fmt.Errorf("字段 %s 必须是有效的JSON格式", namespace)
	default:
		return fmt.Errorf("字段 %s 验证失败: %s", namespace, tag)
	}
}

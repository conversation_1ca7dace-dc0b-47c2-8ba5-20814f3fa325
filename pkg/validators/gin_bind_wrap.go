package validators

import "github.com/gin-gonic/gin"

// ShouldBindJSONWrap 包装 ShouldBindJSON，提供友好的验证错误信息
func ShouldBindJSONWrap(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindJSON(obj); err != nil {
		return FormatValidationError(err)
	}
	return nil
}

// ShouldBindWrap 包装 ShouldBind，提供友好的验证错误信息
func ShouldBindWrap(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBind(obj); err != nil {
		return FormatValidationError(err)
	}
	return nil
}

// ShouldBindQueryWrap 包装 ShouldBindQuery，提供友好的验证错误信息
func ShouldBindQueryWrap(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindQuery(obj); err != nil {
		return FormatValidationError(err)
	}
	return nil
}

// ShouldBindUriWrap 包装 ShouldBindUri，提供友好的验证错误信息
func ShouldBindUriWrap(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindUri(obj); err != nil {
		return FormatValidationError(err)
	}
	return nil
}

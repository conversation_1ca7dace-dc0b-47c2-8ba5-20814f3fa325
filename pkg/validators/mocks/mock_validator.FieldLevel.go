// Code generated by MockGen. DO NOT EDIT.
// Source: vendor/github.com/go-playground/validator/v10/field_level.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockFieldLevel is a mock of FieldLevel interface.
type MockFieldLevel struct {
	ctrl     *gomock.Controller
	recorder *MockFieldLevelMockRecorder
}

// MockFieldLevelMockRecorder is the mock recorder for MockFieldLevel.
type MockFieldLevelMockRecorder struct {
	mock *MockFieldLevel
}

// NewMockFieldLevel creates a new mock instance.
func NewMockFieldLevel(ctrl *gomock.Controller) *MockFieldLevel {
	mock := &MockFieldLevel{ctrl: ctrl}
	mock.recorder = &MockFieldLevelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFieldLevel) EXPECT() *MockFieldLevelMockRecorder {
	return m.recorder
}

// ExtractType mocks base method.
func (m *MockFieldLevel) ExtractType(field reflect.Value) (reflect.Value, reflect.Kind, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtractType", field)
	ret0, _ := ret[0].(reflect.Value)
	ret1, _ := ret[1].(reflect.Kind)
	ret2, _ := ret[2].(bool)
	return ret0, ret1, ret2
}

// ExtractType indicates an expected call of ExtractType.
func (mr *MockFieldLevelMockRecorder) ExtractType(field interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractType", reflect.TypeOf((*MockFieldLevel)(nil).ExtractType), field)
}

// Field mocks base method.
func (m *MockFieldLevel) Field() reflect.Value {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Field")
	ret0, _ := ret[0].(reflect.Value)
	return ret0
}

// Field indicates an expected call of Field.
func (mr *MockFieldLevelMockRecorder) Field() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Field", reflect.TypeOf((*MockFieldLevel)(nil).Field))
}

// FieldName mocks base method.
func (m *MockFieldLevel) FieldName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FieldName")
	ret0, _ := ret[0].(string)
	return ret0
}

// FieldName indicates an expected call of FieldName.
func (mr *MockFieldLevelMockRecorder) FieldName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FieldName", reflect.TypeOf((*MockFieldLevel)(nil).FieldName))
}

// GetStructFieldOK mocks base method.
func (m *MockFieldLevel) GetStructFieldOK() (reflect.Value, reflect.Kind, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStructFieldOK")
	ret0, _ := ret[0].(reflect.Value)
	ret1, _ := ret[1].(reflect.Kind)
	ret2, _ := ret[2].(bool)
	return ret0, ret1, ret2
}

// GetStructFieldOK indicates an expected call of GetStructFieldOK.
func (mr *MockFieldLevelMockRecorder) GetStructFieldOK() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStructFieldOK", reflect.TypeOf((*MockFieldLevel)(nil).GetStructFieldOK))
}

// GetStructFieldOK2 mocks base method.
func (m *MockFieldLevel) GetStructFieldOK2() (reflect.Value, reflect.Kind, bool, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStructFieldOK2")
	ret0, _ := ret[0].(reflect.Value)
	ret1, _ := ret[1].(reflect.Kind)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(bool)
	return ret0, ret1, ret2, ret3
}

// GetStructFieldOK2 indicates an expected call of GetStructFieldOK2.
func (mr *MockFieldLevelMockRecorder) GetStructFieldOK2() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStructFieldOK2", reflect.TypeOf((*MockFieldLevel)(nil).GetStructFieldOK2))
}

// GetStructFieldOKAdvanced mocks base method.
func (m *MockFieldLevel) GetStructFieldOKAdvanced(val reflect.Value, namespace string) (reflect.Value, reflect.Kind, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStructFieldOKAdvanced", val, namespace)
	ret0, _ := ret[0].(reflect.Value)
	ret1, _ := ret[1].(reflect.Kind)
	ret2, _ := ret[2].(bool)
	return ret0, ret1, ret2
}

// GetStructFieldOKAdvanced indicates an expected call of GetStructFieldOKAdvanced.
func (mr *MockFieldLevelMockRecorder) GetStructFieldOKAdvanced(val, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStructFieldOKAdvanced", reflect.TypeOf((*MockFieldLevel)(nil).GetStructFieldOKAdvanced), val, namespace)
}

// GetStructFieldOKAdvanced2 mocks base method.
func (m *MockFieldLevel) GetStructFieldOKAdvanced2(val reflect.Value, namespace string) (reflect.Value, reflect.Kind, bool, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStructFieldOKAdvanced2", val, namespace)
	ret0, _ := ret[0].(reflect.Value)
	ret1, _ := ret[1].(reflect.Kind)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(bool)
	return ret0, ret1, ret2, ret3
}

// GetStructFieldOKAdvanced2 indicates an expected call of GetStructFieldOKAdvanced2.
func (mr *MockFieldLevelMockRecorder) GetStructFieldOKAdvanced2(val, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStructFieldOKAdvanced2", reflect.TypeOf((*MockFieldLevel)(nil).GetStructFieldOKAdvanced2), val, namespace)
}

// GetTag mocks base method.
func (m *MockFieldLevel) GetTag() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTag")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTag indicates an expected call of GetTag.
func (mr *MockFieldLevelMockRecorder) GetTag() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTag", reflect.TypeOf((*MockFieldLevel)(nil).GetTag))
}

// Param mocks base method.
func (m *MockFieldLevel) Param() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Param")
	ret0, _ := ret[0].(string)
	return ret0
}

// Param indicates an expected call of Param.
func (mr *MockFieldLevelMockRecorder) Param() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Param", reflect.TypeOf((*MockFieldLevel)(nil).Param))
}

// Parent mocks base method.
func (m *MockFieldLevel) Parent() reflect.Value {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Parent")
	ret0, _ := ret[0].(reflect.Value)
	return ret0
}

// Parent indicates an expected call of Parent.
func (mr *MockFieldLevelMockRecorder) Parent() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Parent", reflect.TypeOf((*MockFieldLevel)(nil).Parent))
}

// StructFieldName mocks base method.
func (m *MockFieldLevel) StructFieldName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StructFieldName")
	ret0, _ := ret[0].(string)
	return ret0
}

// StructFieldName indicates an expected call of StructFieldName.
func (mr *MockFieldLevelMockRecorder) StructFieldName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StructFieldName", reflect.TypeOf((*MockFieldLevel)(nil).StructFieldName))
}

// Top mocks base method.
func (m *MockFieldLevel) Top() reflect.Value {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Top")
	ret0, _ := ret[0].(reflect.Value)
	return ret0
}

// Top indicates an expected call of Top.
func (mr *MockFieldLevelMockRecorder) Top() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Top", reflect.TypeOf((*MockFieldLevel)(nil).Top))
}

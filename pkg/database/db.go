package database

import (
	"fmt"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/logger"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	// DB 全局数据库连接
	DB *gorm.DB
)

// InitDB 初始化数据库连接
func InitDB() error {
	// 从配置中获取数据库连接信息
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.GlobalConfig.Database.Username,
		config.GlobalConfig.Database.Password,
		config.GlobalConfig.Database.Host,
		config.GlobalConfig.Database.Port,
		config.GlobalConfig.Database.Name,
	)

	// 配置 GORM
	gormConfig := &gorm.Config{
		Logger: logger.NewGormLogger(),
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	// 注册全局软删除插件
	db.Callback().Query().Before("gorm:query").Register("soft_delete_filter", func(db *gorm.DB) {
		if db.Statement.Schema != nil {
			if _, ok := db.Statement.Schema.FieldsByName["Deleted"]; ok {
				db.Statement.AddClause(clause.Where{Exprs: []clause.Expression{
					clause.Eq{Column: "deleted", Value: false},
				}})
			}
		}
	})

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %v", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// query.SetDefault(DB)

	// 自动迁移数据库表结构并添加表注释
	model.AutoMigrateWithComments(db,
		&model.CloudAccount{},
		&model.UserRegion{},
		&model.Cluster{},

		// TODO 按照需要添加其他模型
		&model.ProjectConfiguration{},
		&model.StartupConfiguration{},
		&model.ImageInfo{},
		&model.ImageConfig{},
		&model.BaseImageConfig{},
		&model.ServicesAllocation{},
	)

	// 设置全局数据库连接
	DB = db

	logger.Info("数据库连接成功")
	return nil
}

package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"

	redisv9 "github.com/redis/go-redis/v9"
)

var (
	// Client Redis客户端实例
	Client *redisv9.Client

	// 互斥锁map，用于防止缓存击穿
	mutexMap = make(map[string]*sync.Mutex)
	// 互斥锁map的互斥锁
	mutexMapLock sync.Mutex
)

const (
	// DefaultExpiration 默认缓存过期时间
	DefaultExpiration = 30 * time.Minute
	// EmptyValueExpiration 空值缓存过期时间
	EmptyValueExpiration = 5 * time.Minute
	// MaxRandomExpiration 最大随机过期时间（秒）
	MaxRandomExpiration = 300
	// Table name constants for cache keys
	TableCloudAccounts           = "cloudaccounts"
	TableCloudAccountTenants     = "cloudaccount_tenants"
	TableCloudProviders          = "cloud_providers"
	TableCloudRegions            = "cloudregions"
	TableFuncCustomDomains       = "func_custom_domains"
	TableFuncDNS                 = "func_dns"
	TableFuncEnvVars             = "func_env_vars"
	TableFuncInstTags            = "func_inst_tags"
	TableFuncLayerRelations      = "func_layer_relations"
	TableFuncLifecycles          = "func_lifecycles"
	TableFuncLogs                = "func_logs"
	TableFuncNetworks            = "func_networks"
	TableFuncReservedInsts       = "func_reserved_insts"
	TableFuncStorages            = "func_storages"
	TableFuncTimeScaleConfigs    = "func_time_scale_configs"
	TableFuncTriggers            = "func_triggers"
	TableFuncVersions            = "func_versions"
	TableFuncVPCAccess           = "func_vpc_access"
	TableFuncWaterLevelScaleCfgs = "func_water_level_scale_configs"
	TableFuncWebIDE              = "func_webide"
	TableFuncInsts               = "func_insts"
	TableFuncAliases             = "func_aliases"
	TableFuncTemplates           = "func_templates"
	TableLayerVersions           = "layer_versions"
	TableLayers                  = "layers"
	TableLogServers              = "log_servers"
	TableLogstores               = "logstores"
	TableMqConsumerKafkas        = "mq_consumer_kafkas"
	TableMqConsumerRockets       = "mq_consumer_rockets"
	TableMqInstKafkas            = "mq_inst_kafkas"
	TableMqInstRockets           = "mq_inst_rockets"
	TableMqTenantRelations       = "mq_tenant_relations"
	TableMqTopicKafkas           = "mq_topic_kafkas"
	TableMqTopicRockets          = "mq_topic_rockets"
	TableOfficialLayers          = "official_layers"
	TableOSSBuckets              = "oss_buckets"
	TablePermissions             = "permissions"
	TableResourcePackages        = "resource_packages"
	TableRoles                   = "roles"
	TableRuntimes                = "runtimes"
	TableSecurityGroups          = "security_groups"
	TableTags                    = "tags"
	TableTenantLogs              = "tenant_logs"
	TableTenantOSSRelations      = "tenant_oss_relations"
	TableTenantQuotas            = "tenant_quotas"
	TableTenantRoles             = "tenant_roles"
	TableTenantSecurityGroups    = "tenant_security_groups"
	TableTenantVPCs              = "tenant_vpcs"
	TableTenantVSwitches         = "tenant_vswitches"
	TableVPCs                    = "vpcs"
	TableVSwitches               = "vswitches"
	TableFuncAsyncs              = "func_asyncs"
)

// InitRedis 初始化Redis连接
func InitRedis() error {
	Client = redisv9.NewClient(&redisv9.Options{
		Addr:     fmt.Sprintf("%s:%d", config.GlobalConfig.Redis.Host, config.GlobalConfig.Redis.Port),
		Password: config.GlobalConfig.Redis.Password,
		DB:       config.GlobalConfig.Redis.DB,
	})

	// 测试连接
	ctx := context.Background()
	if err := Client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("连接Redis失败: %v", err)
	}

	// logger.Info("Redis连接成功",
	// 	zap.String("host", config.GlobalConfig.Redis.Host),
	// 	zap.Int("port", config.GlobalConfig.Redis.Port),
	// 	zap.Int("db", config.GlobalConfig.Redis.DB),
	// 	zap.Bool("enable_api_cache", config.GlobalConfig.Redis.EnableApiCache),
	// )

	return nil
}

// Close 关闭Redis连接
func Close() error {
	if Client != nil {
		return Client.Close()
	}
	return nil
}

// getMutex 获取或创建互斥锁
func getMutex(key string) *sync.Mutex {
	mutexMapLock.Lock()
	defer mutexMapLock.Unlock()

	if mutex, exists := mutexMap[key]; exists {
		return mutex
	}

	mutex := &sync.Mutex{}
	mutexMap[key] = mutex
	return mutex
}

// getRandomExpiration 获取随机过期时间
func getRandomExpiration(baseExpiration time.Duration) time.Duration {
	randomSeconds := rand.Intn(MaxRandomExpiration)
	return baseExpiration + time.Duration(randomSeconds)*time.Second
}

// GenerateModelCacheKey 生成模型缓存key
func GenerateModelCacheKey(modelName string, pk interface{}) string {
	return fmt.Sprintf("model:%s:%v", modelName, pk)
}

// GenerateListCacheKey 生成列表缓存key
func GenerateListCacheKey(modelName string, queryHash string) string {
	return fmt.Sprintf("model:%s:list:%s", modelName, queryHash)
}

// SetModelCache 设置模型缓存
func SetModelCache(ctx context.Context, modelName string, pk interface{}, data interface{}) error {
	if !config.GlobalConfig.Redis.EnableApiCache {
		return nil
	}

	key := GenerateModelCacheKey(modelName, pk)
	bytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	// 使用随机过期时间，防止缓存雪崩
	// expiration := getRandomExpiration(DefaultExpiration)
	return Client.Set(ctx, key, bytes, -1).Err()
}

// GetModelCache 获取模型缓存
func GetModelCache(ctx context.Context, modelName string, pk interface{}, data interface{}) error {
	if !config.GlobalConfig.Redis.EnableApiCache {
		return redisv9.Nil
	}

	key := GenerateModelCacheKey(modelName, pk)

	// 获取互斥锁，防止缓存击穿
	mutex := getMutex(key)
	mutex.Lock()
	defer mutex.Unlock()

	bytes, err := Client.Get(ctx, key).Bytes()
	if err != nil {
		if err == redisv9.Nil {
			// 缓存未命中，设置空值防止缓存穿透
			emptyBytes, _ := json.Marshal(nil)
			Client.Set(ctx, key, emptyBytes, EmptyValueExpiration)
		}
		return err
	}

	// 如果缓存的是空值，返回未找到错误
	if string(bytes) == "null" {
		return redisv9.Nil
	}

	return json.Unmarshal(bytes, data)
}

// DeleteModelCache 删除模型缓存
func DeleteModelCache(ctx context.Context, modelName string, pk interface{}) error {
	if !config.GlobalConfig.Redis.EnableApiCache {
		return nil
	}

	key := GenerateModelCacheKey(modelName, pk)
	return Client.Del(ctx, key).Err()
}

// SetListCache 设置列表缓存
func SetListCache(ctx context.Context, modelName string, queryHash string, data interface{}) error {
	if !config.GlobalConfig.Redis.EnableApiCache {
		return nil
	}

	key := GenerateListCacheKey(modelName, queryHash)
	bytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	// 使用随机过期时间，防止缓存雪崩
	expiration := getRandomExpiration(DefaultExpiration)
	return Client.Set(ctx, key, bytes, expiration).Err()
}

// GetListCache 获取列表缓存
func GetListCache(ctx context.Context, modelName string, queryHash string, data interface{}) error {
	if !config.GlobalConfig.Redis.EnableApiCache {
		return redisv9.Nil
	}

	key := GenerateListCacheKey(modelName, queryHash)

	// 获取互斥锁，防止缓存击穿
	mutex := getMutex(key)
	mutex.Lock()
	defer mutex.Unlock()

	bytes, err := Client.Get(ctx, key).Bytes()
	if err != nil {
		if err == redisv9.Nil {
			// 缓存未命中，设置空值防止缓存穿透
			emptyBytes, _ := json.Marshal(nil)
			Client.Set(ctx, key, emptyBytes, EmptyValueExpiration)
		}
		return err
	}

	// 如果缓存的是空值，返回未找到错误
	if string(bytes) == "null" {
		return redisv9.Nil
	}

	return json.Unmarshal(bytes, data)
}

// DeleteListCache 删除列表缓存
func DeleteListCache(ctx context.Context, modelName string, queryHash string) error {
	if !config.GlobalConfig.Redis.EnableApiCache {
		return nil
	}

	if queryHash == "*" {
		// 使用通配符删除所有列表缓存
		pattern := fmt.Sprintf("model:%s:list:*", modelName)
		iter := Client.Scan(ctx, 0, pattern, 0).Iterator()
		for iter.Next(ctx) {
			if err := Client.Del(ctx, iter.Val()).Err(); err != nil {
				return err
			}
		}
		return iter.Err()
	}
	key := GenerateListCacheKey(modelName, queryHash)
	return Client.Del(ctx, key).Err()
}

// BatchDeleteModelCache 批量删除模型缓存
func BatchDeleteModelCache(ctx context.Context, modelName string, pks []interface{}) error {
	if !config.GlobalConfig.Redis.EnableApiCache {
		return nil
	}

	if len(pks) == 0 {
		return nil
	}
	keys := make([]string, len(pks))
	for i, id := range pks {
		keys[i] = GenerateModelCacheKey(modelName, id)
	}
	return Client.Del(ctx, keys...).Err()
}

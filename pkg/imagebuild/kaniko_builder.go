package imagebuild

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

type KanikoBuilder struct {
	clientset *kubernetes.Clientset
	namespace string
}

// NewKanikoBuilder 创建Kaniko构建器
func NewKanikoBuilder(namespace string) (*KanikoBuilder, error) {
	var config *rest.Config
	var err error

	// 尝试使用集群内配置
	config, err = rest.InClusterConfig()
	if err != nil {
		// 如果不在集群内，使用kubeconfig
		kubeconfig := filepath.Join(os.Getenv("HOME"), ".kube", "config")
		config, err = clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			return nil, fmt.Errorf("无法创建Kubernetes配置: %v", err)
		}
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("无法创建Kubernetes客户端: %v", err)
	}

	return &KanikoBuilder{
		clientset: clientset,
		namespace: namespace,
	}, nil
}

// BuildImage 使用Kaniko构建镜像
func (kb *KanikoBuilder) BuildImage(buildID, projectID, baseImage, projectName, entryPoint, workingDir string) error {
	projectDir := filepath.Join("./uploads", projectID)

	// 生成Dockerfile
	if err := GenerateDockerfile(projectDir, baseImage, entryPoint, workingDir); err != nil {
		return fmt.Errorf("生成Dockerfile失败: %v", err)
	}

	// 创建构建上下文（使用OSS存储方案）
	extractDir := filepath.Join(projectDir, "extracted")

	// 上传构建上下文到OSS
	ossKey := fmt.Sprintf("build-contexts/%s.tar.gz", buildID)
	if err := kb.uploadBuildContextToOSS(extractDir, ossKey); err != nil {
		return fmt.Errorf("上传构建上下文到OSS失败: %v", err)
	}

	// 创建Kaniko构建Job（使用OSS下载）
	// 使用阿里云ACR作为镜像仓库
	cfg := config.GlobalConfig
	registryHost := fmt.Sprintf("registry.%s.aliyuncs.com", cfg.S3.Region)
	imageName := fmt.Sprintf("%s/player-apps/%s:%s", registryHost, projectName, buildID[:8])
	jobName := fmt.Sprintf("kaniko-build-%s", buildID)

	// 创建ACR认证Secret
	if err := kb.createACRSecret(); err != nil {
		return fmt.Errorf("创建ACR认证失败: %v", err)
	}

	if err := kb.createKanikoJobWithOSS(jobName, ossKey, imageName, buildID, baseImage, entryPoint); err != nil {
		return fmt.Errorf("创建构建任务失败: %v", err)
	}

	// 启动监控协程
	go kb.monitorBuildJob(jobName, buildID)

	return nil
}

// uploadBuildContextToOSS 将构建上下文打包并上传到OSS
func (kb *KanikoBuilder) uploadBuildContextToOSS(sourceDir, ossKey string) error {
	// 1. 创建临时tar.gz文件
	tempFile, err := os.CreateTemp("", "build-context-*.tar.gz")
	if err != nil {
		return fmt.Errorf("创建临时文件失败: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	// 2. 打包目录为tar.gz
	if err := kb.createTarGz(sourceDir, tempFile); err != nil {
		return fmt.Errorf("打包目录失败: %v", err)
	}

	// 3. 上传到OSS
	if err := kb.uploadToOSS(tempFile.Name(), ossKey); err != nil {
		return fmt.Errorf("上传到OSS失败: %v", err)
	}

	return nil
}

// createTarGz 将目录打包为tar.gz文件
func (kb *KanikoBuilder) createTarGz(sourceDir string, targetFile *os.File) error {
	// 简化实现：使用系统命令
	cmd := exec.Command("tar", "-czf", targetFile.Name(), "-C", sourceDir, ".")
	return cmd.Run()
}

// uploadToOSS 上传文件到真实的阿里云OSS
func (kb *KanikoBuilder) uploadToOSS(filePath, ossKey string) error {
	// 从配置中获取OSS配置
	cfg := config.GlobalConfig

	// 创建OSS客户端
	client, err := oss.New(cfg.S3.Endpoint, cfg.S3.AccessKeyId, cfg.S3.AccessKeySecret)
	if err != nil {
		return fmt.Errorf("创建OSS客户端失败: %v", err)
	}

	// 获取存储桶
	bucket, err := client.Bucket(cfg.S3.Bucket)
	if err != nil {
		return fmt.Errorf("获取存储桶失败: %v", err)
	}

	// 上传文件
	err = bucket.PutObjectFromFile(ossKey, filePath)
	if err != nil {
		return fmt.Errorf("上传文件到OSS失败: %v", err)
	}

	log.Printf("文件成功上传到OSS: %s", ossKey)
	return nil
}

// getOSSDownloadURL 获取OSS下载URL（生成签名URL）
func (kb *KanikoBuilder) getOSSDownloadURL(ossKey string) string {
	// 从配置中获取OSS配置
	cfg := config.GlobalConfig

	// 创建OSS客户端
	client, err := oss.New(cfg.S3.Endpoint, cfg.S3.AccessKeyId, cfg.S3.AccessKeySecret)
	if err != nil {
		log.Printf("创建OSS客户端失败: %v", err)
		return ""
	}

	// 获取存储桶
	bucket, err := client.Bucket(cfg.S3.Bucket)
	if err != nil {
		log.Printf("获取存储桶失败: %v", err)
		return ""
	}

	// 生成签名URL（有效期1小时）
	signedURL, err := bucket.SignURL(ossKey, oss.HTTPGet, 3600)
	if err != nil {
		log.Printf("生成签名URL失败: %v", err)
		return ""
	}

	return signedURL
}

// createACRSecret 创建阿里云ACR认证Secret
func (kb *KanikoBuilder) createACRSecret() error {
	cfg := config.GlobalConfig

	// 构建Docker配置JSON
	dockerConfig := map[string]interface{}{
		"auths": map[string]interface{}{
			fmt.Sprintf("registry.%s.aliyuncs.com", cfg.S3.Region): map[string]interface{}{
				"username": cfg.S3.AccessKeyId,
				"password": cfg.S3.AccessKeySecret,
			},
		},
	}

	// 将配置转换为JSON字符串
	configJSON, err := json.Marshal(dockerConfig)
	if err != nil {
		return fmt.Errorf("序列化Docker配置失败: %v", err)
	}

	// 创建Secret
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "acr-credentials",
			Namespace: kb.namespace,
		},
		Type: corev1.SecretTypeOpaque,
		Data: map[string][]byte{
			"config.json": configJSON,
		},
	}

	// 检查Secret是否已存在
	_, err = kb.clientset.CoreV1().Secrets(kb.namespace).Get(context.TODO(), "acr-credentials", metav1.GetOptions{})
	if err == nil {
		// Secret已存在，更新它
		_, err = kb.clientset.CoreV1().Secrets(kb.namespace).Update(context.TODO(), secret, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("更新ACR认证Secret失败: %v", err)
		}
	} else {
		// Secret不存在，创建它
		_, err = kb.clientset.CoreV1().Secrets(kb.namespace).Create(context.TODO(), secret, metav1.CreateOptions{})
		if err != nil {
			return fmt.Errorf("创建ACR认证Secret失败: %v", err)
		}
	}

	log.Printf("ACR认证Secret创建/更新成功")
	return nil
}

// createKanikoJobWithOSS 创建使用OSS下载的Kaniko构建Job
func (kb *KanikoBuilder) createKanikoJobWithOSS(jobName, ossKey, imageName, buildID, baseImage, entryPoint string) error {
	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: kb.namespace,
			Labels: map[string]string{
				"app":      "kaniko-builder",
				"build-id": buildID,
			},
		},
		Spec: batchv1.JobSpec{
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":      "kaniko-builder",
						"build-id": buildID,
					},
				},
				Spec: corev1.PodSpec{
					InitContainers: []corev1.Container{
						{
							Name:    "download-context",
							Image:   "alpine/curl:latest",
							Command: []string{"sh", "-c"},
							Args: []string{
								fmt.Sprintf(`echo "从HTTP服务下载构建上下文..."
								 curl -o /tmp/context.tar.gz "%s"
								 cd /workspace
								 tar -xzf /tmp/context.tar.gz
								 ls -la /workspace/
								 echo "构建上下文下载完成"`, kb.getOSSDownloadURL(ossKey)),
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "workspace",
									MountPath: "/workspace",
								},
							},
						},
					},
					Containers: []corev1.Container{
						{
							Name:  "kaniko",
							Image: "gcr.io/kaniko-project/executor:latest",
							Args: []string{
								"--dockerfile=/workspace/Dockerfile",
								"--context=/workspace",
								fmt.Sprintf("--destination=%s", imageName),
								"--verbosity=info",
								fmt.Sprintf("--label=build.id=%s", buildID),
								fmt.Sprintf("--label=build.timestamp=%s", time.Now().Format(time.RFC3339)),
								fmt.Sprintf("--label=build.baseImage=%s", baseImage),
								fmt.Sprintf("--label=build.entryPoint=%s", entryPoint),
							},
							Env: []corev1.EnvVar{
								{
									Name:  "DOCKER_CONFIG",
									Value: "/kaniko/.docker",
								},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "workspace",
									MountPath: "/workspace",
								},
								{
									Name:      "docker-config",
									MountPath: "/kaniko/.docker",
								},
							},
							Resources: corev1.ResourceRequirements{
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse("500m"),
									corev1.ResourceMemory: resource.MustParse("512Mi"),
								},
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse("1000m"),
									corev1.ResourceMemory: resource.MustParse("1Gi"),
								},
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "workspace",
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
						{
							Name: "docker-config",
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									SecretName: "acr-credentials",
									Items: []corev1.KeyToPath{
										{
											Key:  "config.json",
											Path: "config.json",
										},
									},
								},
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyNever,
				},
			},
			BackoffLimit: func() *int32 { i := int32(1); return &i }(),
		},
	}

	_, err := kb.clientset.BatchV1().Jobs(kb.namespace).Create(
		context.TODO(), job, metav1.CreateOptions{})

	return err
}

type BuildStatus struct {
	ID          string     `json:"id"`
	ProjectID   string     `json:"projectId"`
	Status      string     `json:"status"` // pending, building, success, failed
	BaseImage   string     `json:"baseImage"`
	EntryPoint  string     `json:"entryPoint"`
	CreatedAt   time.Time  `json:"createdAt"`
	CompletedAt *time.Time `json:"completedAt,omitempty"`
	ImageName   string     `json:"imageName,omitempty"`
	Logs        []string   `json:"logs,omitempty"`
	Error       string     `json:"error,omitempty"`
}

// 内存存储（生产环境应使用数据库）
var BuildStatuses = make(map[string]*BuildStatus)

// monitorBuildJob 监控构建Job状态
func (kb *KanikoBuilder) monitorBuildJob(jobName, buildID string) {
	buildStatus := BuildStatuses[buildID]
	if buildStatus == nil {
		log.Printf("构建状态不存在: %s", buildID)
		return
	}

	buildStatus.Status = "building"
	buildStatus.Logs = append(buildStatus.Logs, "Kaniko构建任务已创建")

	// 轮询Job状态
	for {
		time.Sleep(5 * time.Second)

		job, err := kb.clientset.BatchV1().Jobs(kb.namespace).Get(
			context.TODO(), jobName, metav1.GetOptions{})
		if err != nil {
			log.Printf("获取Job状态失败: %v", err)
			continue
		}

		// 获取Pod日志
		pods, err := kb.clientset.CoreV1().Pods(kb.namespace).List(
			context.TODO(), metav1.ListOptions{
				LabelSelector: fmt.Sprintf("build-id=%s", buildID),
			})
		if err == nil && len(pods.Items) > 0 {
			pod := pods.Items[0]
			if pod.Status.Phase == corev1.PodRunning || pod.Status.Phase == corev1.PodSucceeded {
				logs := kb.getPodLogs(pod.Name, "kaniko")
				if logs != "" {
					logLines := strings.Split(logs, "\n")
					buildStatus.Logs = append(buildStatus.Logs[:1], logLines...)
				}
			}
		}

		// 检查Job完成状态
		if job.Status.Succeeded > 0 {
			now := time.Now()
			buildStatus.Status = "success"
			buildStatus.CompletedAt = &now
			buildStatus.ImageName = fmt.Sprintf("player-apps/%s:%s", "demo", buildID[:8])
			buildStatus.Logs = append(buildStatus.Logs, "构建成功完成！")

			// 清理资源
			kb.cleanup(jobName, fmt.Sprintf("build-contexts/%s.tar.gz", buildID))
			break
		} else if job.Status.Failed > 0 {
			now := time.Now()
			buildStatus.Status = "failed"
			buildStatus.CompletedAt = &now
			buildStatus.Error = "构建失败，请检查代码和依赖"
			buildStatus.Logs = append(buildStatus.Logs, "构建失败")

			// 清理资源
			kb.cleanup(jobName, fmt.Sprintf("build-contexts/%s.tar.gz", buildID))
			break
		}
	}
}

// getPodLogs 获取Pod日志
func (kb *KanikoBuilder) getPodLogs(podName, containerName string) string {
	req := kb.clientset.CoreV1().Pods(kb.namespace).GetLogs(podName, &corev1.PodLogOptions{
		Container: containerName,
		TailLines: func() *int64 { i := int64(50); return &i }(),
	})

	logs, err := req.Stream(context.TODO())
	if err != nil {
		return ""
	}
	defer logs.Close()

	buf, err := io.ReadAll(logs)
	if err != nil {
		return ""
	}

	return string(buf)
}

// cleanup 清理构建资源
func (kb *KanikoBuilder) cleanup(jobName, ossKey string) {
	// 删除Job
	kb.clientset.BatchV1().Jobs(kb.namespace).Delete(
		context.TODO(), jobName, metav1.DeleteOptions{})

	// 删除OSS文件
	kb.deleteFromOSS(ossKey)
}

// deleteFromOSS 从真实的阿里云OSS删除文件
func (kb *KanikoBuilder) deleteFromOSS(ossKey string) error {
	// 从配置中获取OSS配置
	cfg := config.GlobalConfig

	// 创建OSS客户端
	client, err := oss.New(cfg.S3.Endpoint, cfg.S3.AccessKeyId, cfg.S3.AccessKeySecret)
	if err != nil {
		return fmt.Errorf("创建OSS客户端失败: %v", err)
	}

	// 获取存储桶
	bucket, err := client.Bucket(cfg.S3.Bucket)
	if err != nil {
		return fmt.Errorf("获取存储桶失败: %v", err)
	}

	// 删除文件
	err = bucket.DeleteObject(ossKey)
	if err != nil {
		return fmt.Errorf("从OSS删除文件失败: %v", err)
	}

	log.Printf("文件成功从OSS删除: %s", ossKey)
	return nil
}

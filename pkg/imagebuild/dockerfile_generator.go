package imagebuild

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// DockerfileTemplate 定义简化的Dockerfile模板
type DockerfileTemplate struct {
	BaseImage   string
	WorkDir     string
	ExposePort  int
	EntryPoint  string
	Environment map[string]string
}

// GenerateDockerfile 根据基础镜像和配置生成Dockerfile
func GenerateDockerfile(projectDir, baseImage, entryPoint, workingDir string) error {
	// 设置默认工作目录
	if workingDir == "" {
		workingDir = "/app"
	}

	template := &DockerfileTemplate{
		BaseImage:   baseImage,
		WorkDir:     workingDir,
		ExposePort:  8080,
		EntryPoint:  entryPoint,
		Environment: make(map[string]string),
	}

	// 生成简化的Dockerfile内容
	dockerfileContent := generateSimpleDockerfile(template)

	// 写入Dockerfile
	dockerfilePath := filepath.Join(projectDir, "extracted", "Dockerfile")
	return os.WriteFile(dockerfilePath, []byte(dockerfileContent), 0644)
}

// generateSimpleDockerfile 生成简化的Dockerfile内容
func generateSimpleDockerfile(template *DockerfileTemplate) string {
	var content strings.Builder

	// 基础镜像
	content.WriteString(fmt.Sprintf("FROM %s\n\n", template.BaseImage))

	// 设置工作目录
	content.WriteString(fmt.Sprintf("WORKDIR %s\n\n", template.WorkDir))

	// 设置环境变量
	for key, value := range template.Environment {
		content.WriteString(fmt.Sprintf("ENV %s=%s\n", key, value))
	}
	if len(template.Environment) > 0 {
		content.WriteString("\n")
	}

	// 复制所有文件
	content.WriteString("COPY . .\n\n")

	// 设置可执行权限（如果需要）
	if strings.Contains(template.EntryPoint, "./") || strings.HasSuffix(template.EntryPoint, ".sh") {
		content.WriteString("RUN chmod +x " + template.EntryPoint + "\n\n")
	}

	// 暴露端口
	content.WriteString(fmt.Sprintf("EXPOSE %d\n\n", template.ExposePort))

	// 设置入口点
	if strings.Contains(template.EntryPoint, " ") {
		// 如果入口点包含参数，使用shell形式
		content.WriteString(fmt.Sprintf("CMD %s\n", template.EntryPoint))
	} else {
		// 否则使用exec形式
		content.WriteString(fmt.Sprintf("CMD [\"%s\"]\n", template.EntryPoint))
	}

	return content.String()
}

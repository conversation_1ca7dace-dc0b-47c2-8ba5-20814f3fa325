package imagebuild

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// ZipResult ZIP处理结果
type ZipResult struct {
	TotalFiles    int      `json:"totalFiles"`
	ExtractedSize int64    `json:"extractedSize"`
	FileTypes     []string `json:"fileTypes"`
	Executables   []string `json:"executables"`
}

// ZipHandler ZIP文件处理器
type ZipHandler struct{}

// NewZipHandler 创建ZIP处理器
func NewZipHandler() *ZipHandler {
	return &ZipHandler{}
}

// ProcessZipFile 处理ZIP文件
func (zh *ZipHandler) ProcessZipFile(zipPath, extractDir string) (*ZipResult, error) {
	// 创建解压目录
	if err := os.MkdirAll(extractDir, 0755); err != nil {
		return nil, fmt.Errorf("创建解压目录失败: %v", err)
	}

	// 打开ZIP文件
	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return nil, fmt.Errorf("打开ZIP文件失败: %v", err)
	}
	defer reader.Close()

	result := &ZipResult{
		FileTypes:   []string{},
		Executables: []string{},
	}

	fileTypes := make(map[string]bool)

	// 解压文件
	for _, file := range reader.File {
		if file.FileInfo().IsDir() {
			continue
		}

		result.TotalFiles++

		// 获取文件扩展名
		ext := strings.ToLower(filepath.Ext(file.Name))
		if ext != "" {
			fileTypes[ext] = true
		}

		// 解压文件
		if err := zh.extractFile(file, extractDir); err != nil {
			return nil, fmt.Errorf("解压文件 %s 失败: %v", file.Name, err)
		}

		// 检查是否为可执行文件
		if zh.isExecutable(file.Name) {
			result.Executables = append(result.Executables, file.Name)
		}

		result.ExtractedSize += file.FileInfo().Size()
	}

	// 转换文件类型为切片
	for fileType := range fileTypes {
		result.FileTypes = append(result.FileTypes, fileType)
	}

	return result, nil
}

// extractFile 解压单个文件
func (zh *ZipHandler) extractFile(file *zip.File, extractDir string) error {
	// 创建目标文件路径
	targetPath := filepath.Join(extractDir, file.Name)

	// 创建目录
	if err := os.MkdirAll(filepath.Dir(targetPath), 0755); err != nil {
		return err
	}

	// 打开源文件
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	// 创建目标文件
	dst, err := os.Create(targetPath)
	if err != nil {
		return err
	}
	defer dst.Close()

	// 复制文件内容
	_, err = io.Copy(dst, src)
	return err
}

// isExecutable 检查文件是否为可执行文件
func (zh *ZipHandler) isExecutable(fileName string) bool {
	ext := strings.ToLower(filepath.Ext(fileName))
	executableExts := []string{
		".exe", ".bat", ".cmd", ".com", ".msi",
		".sh", ".bash", ".zsh", ".fish",
		".py", ".pl", ".rb", ".js", ".php",
		".jar", ".war", ".ear",
	}

	for _, execExt := range executableExts {
		if ext == execExt {
			return true
		}
	}

	// 检查无扩展名的文件（Unix可执行文件）
	if ext == "" && !strings.Contains(fileName, ".") {
		return true
	}

	return false
}

// FindExecutableFiles 查找可执行文件
func (zh *ZipHandler) FindExecutableFiles(dir string) ([]string, error) {
	var executables []string

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			relPath, err := filepath.Rel(dir, path)
			if err != nil {
				return err
			}

			if zh.isExecutable(relPath) {
				executables = append(executables, relPath)
			}
		}

		return nil
	})

	return executables, err
}

// GetSummary 获取处理结果摘要
func (zh *ZipHandler) GetSummary(result *ZipResult) string {
	return fmt.Sprintf("解压了 %d 个文件，总大小 %d 字节，发现 %d 个可执行文件",
		result.TotalFiles, result.ExtractedSize, len(result.Executables))
}

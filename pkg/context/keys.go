package context

// Context 键常量定义
// 所有在 gin.Context 中使用的键都应该在这里定义，避免魔法字符串
const (
	// 用户相关
	UserInfoKey = "userInfo"
	UserIDKey   = "userID"
	UserUUIDKey = "userUUID"

	// 请求相关
	RequestIDKey    = "requestID"
	RequestIDHeader = "X-Request-ID"

	// 业务相关
	ProjectIDKey = "projectID"
	TenantIDKey  = "tenantID"

	// 审计相关
	OperationTypeKey = "operationType"
	ResourceTypeKey  = "resourceType"

	// 认证相关
	AuthTokenKey = "authToken"
	AuthUserKey  = "authUser"
)

package context

type AuthRequest struct {
	UUID   string `json:"uuid"`
	ToUUID string `json:"toUuid"`
	Ticket string `json:"ticket"`
}

// UserInfo 用户信息结构体
type UserInfo struct {
	UUID         string   `json:"uuid"`
	ID           int64    `json:"id"`
	NickName     string   `json:"nickName"`
	Mobile       string   `json:"mobile"`
	Email        string   `json:"email"`
	Avatar       string   `json:"avatar"`
	Introduction string   `json:"introduction"`
	CreateTime   string   `json:"createTime"`
	Uname        string   `json:"uname"`
	Badges       []string `json:"badges"`
	GroupID      int64    `json:"groupId"`
	MainAccount  bool     `json:"mainAccount"`
}

// RequestContext 请求上下文信息
type RequestContext struct {
	RequestID     string   `json:"requestID"`
	UserInfo      UserInfo `json:"userInfo"`
	ProjectID     int64    `json:"projectID"`
	TenantID      int64    `json:"tenantID"`
	OperationType string   `json:"operationType"`
	ResourceType  string   `json:"resourceType"`
}

// ContextValue 上下文值的通用接口
type ContextValue interface {
	GetKey() string
	GetValue() interface{}
}

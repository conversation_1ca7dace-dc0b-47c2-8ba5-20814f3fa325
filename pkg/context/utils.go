package context

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// ==================== 用户信息相关 ====================

// SetUserInfo 设置用户信息到上下文
func SetUserInfo(c *gin.Context, userInfo UserInfo) {
	c.Set(UserInfoKey, userInfo)
}

// GetUserInfo 从上下文获取用户信息
func GetUserInfo(c *gin.Context) (UserInfo, bool) {
	userInfo, exists := c.Get(UserInfoKey)
	if !exists {
		return UserInfo{}, false
	}
	return userInfo.(UserInfo), true
}

// GetUserID 从上下文获取用户ID
func GetUserID(c *gin.Context) (int64, bool) {
	userInfo, exists := GetUserInfo(c)
	if !exists {
		return 0, true
	}
	return userInfo.ID, true
}

// GetUserUUID 从上下文获取用户UUID
func GetUserUUID(c *gin.Context) (string, bool) {
	userInfo, exists := GetUserInfo(c)
	if !exists {
		return "", false
	}
	return userInfo.UUID, true
}

// MustGetUserInfo 强制获取用户信息，如果不存在则panic
func MustGetUserInfo(c *gin.Context) UserInfo {
	userInfo, exists := GetUserInfo(c)
	if !exists {
		panic("用户信息不存在于上下文中")
	}
	return userInfo
}

// ==================== 请求ID相关 ====================

// SetRequestID 设置请求ID到上下文
func SetRequestID(c *gin.Context, requestID string) {
	c.Set(RequestIDKey, requestID)
	c.Header(RequestIDHeader, requestID)
}

// GetRequestID 从上下文获取请求ID
func GetRequestID(c *gin.Context) (string, bool) {
	requestID, exists := c.Get(RequestIDKey)
	if !exists {
		return "", false
	}
	return requestID.(string), true
}

// MustGetRequestID 强制获取请求ID
func MustGetRequestID(c *gin.Context) string {
	requestID, exists := GetRequestID(c)
	if !exists {
		panic("请求ID不存在于上下文中")
	}
	return requestID
}

// ==================== 业务参数相关 ====================

// SetProjectID 设置项目ID到上下文
func SetProjectID(c *gin.Context, projectID int64) {
	c.Set(ProjectIDKey, projectID)
}

// GetProjectID 从上下文获取项目ID
func GetProjectID(c *gin.Context) (int64, bool) {
	projectID, exists := c.Get(ProjectIDKey)
	if !exists {
		return 0, false
	}
	return projectID.(int64), true
}

// GetProjectIDFromParam 从URL参数获取项目ID并设置到上下文
func GetProjectIDFromParam(c *gin.Context) (int64, error) {
	projectIDStr := c.Param("projectId")
	projectID, err := strconv.ParseInt(projectIDStr, 10, 64)
	if err != nil {
		return 0, err
	}
	SetProjectID(c, projectID)
	return projectID, nil
}

// SetTenantID 设置租户ID到上下文
func SetTenantID(c *gin.Context, tenantID int64) {
	c.Set(TenantIDKey, tenantID)
}

// GetTenantID 从上下文获取租户ID
func GetTenantID(c *gin.Context) (int64, bool) {
	tenantID, exists := c.Get(TenantIDKey)
	if !exists {
		return 0, false
	}
	return tenantID.(int64), true
}

// ==================== 审计相关 ====================

// SetOperationType 设置操作类型到上下文
func SetOperationType(c *gin.Context, operationType string) {
	c.Set(OperationTypeKey, operationType)
}

// GetOperationType 从上下文获取操作类型
func GetOperationType(c *gin.Context) (string, bool) {
	operationType, exists := c.Get(OperationTypeKey)
	if !exists {
		return "", false
	}
	return operationType.(string), true
}

// SetResourceType 设置资源类型到上下文
func SetResourceType(c *gin.Context, resourceType string) {
	c.Set(ResourceTypeKey, resourceType)
}

// GetResourceType 从上下文获取资源类型
func GetResourceType(c *gin.Context) (string, bool) {
	resourceType, exists := c.Get(ResourceTypeKey)
	if !exists {
		return "", false
	}
	return resourceType.(string), true
}

// ==================== 通用工具函数 ====================

// GetString 从上下文获取字符串值
func GetString(c *gin.Context, key string) (string, bool) {
	value, exists := c.Get(key)
	if !exists {
		return "", false
	}
	return value.(string), true
}

// GetInt 从上下文获取整数值
func GetInt(c *gin.Context, key string) (int, bool) {
	value, exists := c.Get(key)
	if !exists {
		return 0, false
	}
	return value.(int), true
}

// GetInt64 从上下文获取int64值
func GetInt64(c *gin.Context, key string) (int64, bool) {
	value, exists := c.Get(key)
	if !exists {
		return 0, false
	}
	return value.(int64), true
}

// GetBool 从上下文获取布尔值
func GetBool(c *gin.Context, key string) (bool, bool) {
	value, exists := c.Get(key)
	if !exists {
		return false, false
	}
	return value.(bool), true
}

// GetInterface 从上下文获取任意类型值
func GetInterface(c *gin.Context, key string) (interface{}, bool) {
	return c.Get(key)
}

// ==================== 上下文信息聚合 ====================

// GetRequestContext 获取完整的请求上下文信息
func GetRequestContext(c *gin.Context) RequestContext {
	requestID, _ := GetRequestID(c)
	userInfo, _ := GetUserInfo(c)
	projectID, _ := GetProjectID(c)
	tenantID, _ := GetTenantID(c)
	operationType, _ := GetOperationType(c)
	resourceType, _ := GetResourceType(c)

	return RequestContext{
		RequestID:     requestID,
		UserInfo:      userInfo,
		ProjectID:     projectID,
		TenantID:      tenantID,
		OperationType: operationType,
		ResourceType:  resourceType,
	}
}

// IsAuthenticated 检查用户是否已认证
func IsAuthenticated(c *gin.Context) bool {
	_, exists := GetUserInfo(c)
	return exists
}

// IsProjectContext 检查是否有项目上下文
func IsProjectContext(c *gin.Context) bool {
	_, exists := GetProjectID(c)
	return exists
}

package k8smgr

import (
	"fmt"
	"sync"
	"time"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/klog/v2"
)

// ClientPool 客户端连接池
type ClientPool struct {
	mu      sync.RWMutex
	clients map[string]*ClientWrapper
	config  *MultiClusterConfig
}

// ClientWrapper 客户端包装器
type ClientWrapper struct {
	client     kubernetes.Interface
	restConfig *rest.Config
	config     *ClusterConfig

	// 连接状态
	connected bool
	lastUsed  time.Time
	createdAt time.Time

	// 统计信息
	requestCount int64
	errorCount   int64

	mu sync.RWMutex
}

// NewClientPool 创建新的客户端池
func NewClientPool(config *MultiClusterConfig) *ClientPool {
	if config == nil {
		config = DefaultMultiClusterConfig()
	}

	return &ClientPool{
		clients: make(map[string]*ClientWrapper),
		config:  config,
	}
}

// GetClient 获取指定集群的客户端
func (p *ClientPool) GetClient(clusterName string) (kubernetes.Interface, error) {
	p.mu.RLock()
	wrapper, exists := p.clients[clusterName]
	p.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("cluster %s not found", clusterName)
	}

	wrapper.mu.Lock()
	defer wrapper.mu.Unlock()

	// 更新最后使用时间
	wrapper.lastUsed = time.Now()
	wrapper.requestCount++

	if !wrapper.connected {
		return nil, fmt.Errorf("cluster %s is not connected", clusterName)
	}

	return wrapper.client, nil
}

// GetRestConfig 获取指定集群的rest配置
func (p *ClientPool) GetRestConfig(clusterName string) (*rest.Config, error) {
	p.mu.RLock()
	wrapper, exists := p.clients[clusterName]
	p.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("cluster %s not found", clusterName)
	}

	wrapper.mu.RLock()
	defer wrapper.mu.RUnlock()

	if !wrapper.connected {
		return nil, fmt.Errorf("cluster %s is not connected", clusterName)
	}

	return wrapper.restConfig, nil
}

// AddCluster 添加集群到连接池
func (p *ClientPool) AddCluster(config *ClusterConfig) error {
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid cluster config: %w", err)
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	// 使用唯一标识符检查是否已存在
	uniqueID := config.GetUniqueID()
	if _, exists := p.clients[uniqueID]; exists {
		return fmt.Errorf("cluster %s already exists", config.GetDisplayName())
	}

	// 创建客户端包装器
	wrapper, err := p.createClientWrapper(config)
	if err != nil {
		return fmt.Errorf("failed to create client for cluster %s: %w", config.GetDisplayName(), err)
	}

	p.clients[uniqueID] = wrapper

	klog.Infof("Added cluster %s to client pool", config.GetDisplayName())
	return nil
}

// RemoveCluster 从连接池移除集群
func (p *ClientPool) RemoveCluster(clusterName string) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	wrapper, exists := p.clients[clusterName]
	if !exists {
		return fmt.Errorf("cluster %s not found", clusterName)
	}

	// 标记为未连接
	wrapper.mu.Lock()
	wrapper.connected = false
	wrapper.mu.Unlock()

	delete(p.clients, clusterName)

	klog.Infof("Removed cluster %s from client pool", clusterName)
	return nil
}

// UpdateCluster 更新集群配置
func (p *ClientPool) UpdateCluster(clusterName string, config *ClusterConfig) error {
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid cluster config: %w", err)
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	oldWrapper, exists := p.clients[clusterName]
	if !exists {
		return fmt.Errorf("cluster %s not found", clusterName)
	}

	// 创建新的客户端包装器
	newWrapper, err := p.createClientWrapper(config)
	if err != nil {
		return fmt.Errorf("failed to create new client for cluster %s: %w", config.GetDisplayName(), err)
	}

	// 保留统计信息
	newWrapper.mu.Lock()
	oldWrapper.mu.RLock()
	newWrapper.requestCount = oldWrapper.requestCount
	newWrapper.errorCount = oldWrapper.errorCount
	oldWrapper.mu.RUnlock()
	newWrapper.mu.Unlock()

	// 标记旧客户端为未连接
	oldWrapper.mu.Lock()
	oldWrapper.connected = false
	oldWrapper.mu.Unlock()

	// 检查新配置的唯一ID是否与旧的不同
	newUniqueID := config.GetUniqueID()
	if newUniqueID != clusterName {
		// 如果唯一ID改变了，需要删除旧的并添加新的
		delete(p.clients, clusterName)
		p.clients[newUniqueID] = newWrapper
		klog.Infof("Updated cluster %s (ID changed from %s to %s) in client pool", config.GetDisplayName(), clusterName, newUniqueID)
	} else {
		// 如果唯一ID没变，直接更新
		p.clients[clusterName] = newWrapper
		klog.Infof("Updated cluster %s in client pool", config.GetDisplayName())
	}

	return nil
}

// ListClusters 列出所有集群
func (p *ClientPool) ListClusters() []string {
	p.mu.RLock()
	defer p.mu.RUnlock()

	clusters := make([]string, 0, len(p.clients))
	for name := range p.clients {
		clusters = append(clusters, name)
	}

	return clusters
}

// GetClusterInfo 获取集群信息
func (p *ClientPool) GetClusterInfo(clusterName string) (*ClientWrapper, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	wrapper, exists := p.clients[clusterName]
	if !exists {
		return nil, fmt.Errorf("cluster %s not found", clusterName)
	}

	return wrapper, nil
}

// IsConnected 检查集群是否连接
func (p *ClientPool) IsConnected(clusterName string) bool {
	p.mu.RLock()
	wrapper, exists := p.clients[clusterName]
	p.mu.RUnlock()

	if !exists {
		return false
	}

	wrapper.mu.RLock()
	defer wrapper.mu.RUnlock()

	return wrapper.connected
}

// Reconnect 重新连接集群
func (p *ClientPool) Reconnect(clusterName string) error {
	p.mu.RLock()
	wrapper, exists := p.clients[clusterName]
	p.mu.RUnlock()

	if !exists {
		return fmt.Errorf("cluster %s not found", clusterName)
	}

	// 重新创建客户端
	newWrapper, err := p.createClientWrapper(wrapper.config)
	if err != nil {
		wrapper.mu.Lock()
		wrapper.errorCount++
		wrapper.mu.Unlock()
		return fmt.Errorf("failed to reconnect cluster %s: %w", clusterName, err)
	}

	// 保留统计信息
	wrapper.mu.Lock()
	newWrapper.requestCount = wrapper.requestCount
	newWrapper.errorCount = wrapper.errorCount
	wrapper.connected = false
	wrapper.mu.Unlock()

	p.mu.Lock()
	p.clients[clusterName] = newWrapper
	p.mu.Unlock()

	klog.Infof("Reconnected cluster %s", clusterName)
	return nil
}

// createClientWrapper 创建客户端包装器
func (p *ClientPool) createClientWrapper(config *ClusterConfig) (*ClientWrapper, error) {
	// 构建rest配置
	restConfig, err := config.BuildRestConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to build rest config: %w", err)
	}

	// 创建kubernetes客户端
	client, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create kubernetes client: %w", err)
	}

	// 测试连接
	_, err = client.Discovery().ServerVersion()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to cluster: %w", err)
	}

	wrapper := &ClientWrapper{
		client:     client,
		restConfig: restConfig,
		config:     config.Clone(),
		connected:  true,
		lastUsed:   time.Now(),
		createdAt:  time.Now(),
	}

	return wrapper, nil
}

// Cleanup 清理过期连接
func (p *ClientPool) Cleanup() {
	p.mu.Lock()
	defer p.mu.Unlock()

	now := time.Now()
	maxLifetime := p.config.ConnMaxLifetime

	for name, wrapper := range p.clients {
		wrapper.mu.RLock()
		shouldCleanup := now.Sub(wrapper.createdAt) > maxLifetime ||
			(!wrapper.connected && now.Sub(wrapper.lastUsed) > time.Hour)
		wrapper.mu.RUnlock()

		if shouldCleanup {
			wrapper.mu.Lock()
			wrapper.connected = false
			wrapper.mu.Unlock()

			delete(p.clients, name)
			klog.Infof("Cleaned up expired client for cluster %s", name)
		}
	}
}

// GetStats 获取连接池统计信息
func (p *ClientPool) GetStats() map[string]interface{} {
	p.mu.RLock()
	defer p.mu.RUnlock()

	stats := map[string]interface{}{
		"total_clusters":     len(p.clients),
		"connected_clusters": 0,
		"clusters":           make(map[string]interface{}),
	}

	connectedCount := 0
	clusterStats := make(map[string]interface{})

	for name, wrapper := range p.clients {
		wrapper.mu.RLock()
		if wrapper.connected {
			connectedCount++
		}

		clusterStats[name] = map[string]interface{}{
			"connected":     wrapper.connected,
			"last_used":     wrapper.lastUsed,
			"created_at":    wrapper.createdAt,
			"request_count": wrapper.requestCount,
			"error_count":   wrapper.errorCount,
		}
		wrapper.mu.RUnlock()
	}

	stats["connected_clusters"] = connectedCount
	stats["clusters"] = clusterStats

	return stats
}

// Close 关闭连接池
func (p *ClientPool) Close() {
	p.mu.Lock()
	defer p.mu.Unlock()

	for name, wrapper := range p.clients {
		wrapper.mu.Lock()
		wrapper.connected = false
		wrapper.mu.Unlock()
		klog.Infof("Closed client for cluster %s", name)
	}

	p.clients = make(map[string]*ClientWrapper)
}

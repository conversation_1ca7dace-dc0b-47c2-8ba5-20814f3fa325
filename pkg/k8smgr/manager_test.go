package k8smgr

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewManager(t *testing.T) {
	// 测试使用默认配置创建管理器
	manager, err := NewManager(nil)
	require.NoError(t, err)
	require.NotNil(t, manager)

	assert.NotNil(t, manager.config)
	assert.NotNil(t, manager.pool)
	assert.NotNil(t, manager.checker)
	assert.NotNil(t, manager.ops)
}

func TestManagerWithConfig(t *testing.T) {
	config := &MultiClusterConfig{
		Clusters: []*ClusterConfig{
			{
				Name:        "test-cluster",
				Description: "Test cluster",
				Environment: "test",
				AuthType:    AuthTypeInCluster, // 使用集群内认证避免需要真实的kubeconfig
				Timeout:     30 * time.Second,
				QPS:         100,
				Burst:       100,
				HealthCheck: HealthCheckConfig{
					Enabled:          true,
					Interval:         30 * time.Second,
					Timeout:          10 * time.Second,
					FailureThreshold: 3,
					SuccessThreshold: 1,
				},
				Enabled: true,
			},
		},
		DefaultCluster:      "test-cluster",
		LoadBalanceStrategy: LoadBalanceRoundRobin,
		GlobalTimeout:       60 * time.Second,
	}

	manager, err := NewManager(config)
	require.NoError(t, err)
	require.NotNil(t, manager)

	// 验证配置
	assert.Equal(t, config.DefaultCluster, manager.config.DefaultCluster)
	assert.Equal(t, config.LoadBalanceStrategy, manager.config.LoadBalanceStrategy)
}

func TestClusterConfig_Validate(t *testing.T) {
	tests := []struct {
		name    string
		config  *ClusterConfig
		wantErr bool
	}{
		{
			name: "valid kubeconfig config",
			config: &ClusterConfig{
				Name:      "test",
				APIServer: "https://k8s.example.com",
				AuthType:  AuthTypeKubeconfig,
				Auth: AuthConfig{
					KubeconfigPath: "/path/to/kubeconfig",
				},
			},
			wantErr: false,
		},
		{
			name: "valid token config",
			config: &ClusterConfig{
				Name:      "test",
				APIServer: "https://k8s.example.com",
				AuthType:  AuthTypeToken,
				Auth: AuthConfig{
					Token: "test-token",
				},
			},
			wantErr: false,
		},
		{
			name: "valid certificate config",
			config: &ClusterConfig{
				Name:      "test",
				APIServer: "https://k8s.example.com",
				AuthType:  AuthTypeCertificate,
				Auth: AuthConfig{
					CertData: "cert-data",
					KeyData:  "key-data",
				},
			},
			wantErr: false,
		},
		{
			name: "valid in-cluster config",
			config: &ClusterConfig{
				Name:     "test",
				AuthType: AuthTypeInCluster,
			},
			wantErr: false,
		},
		{
			name: "empty name",
			config: &ClusterConfig{
				Name:      "",
				APIServer: "https://k8s.example.com",
				AuthType:  AuthTypeKubeconfig,
			},
			wantErr: true,
		},
		{
			name: "missing api server for kubeconfig",
			config: &ClusterConfig{
				Name:     "test",
				AuthType: AuthTypeKubeconfig,
			},
			wantErr: true,
		},
		{
			name: "missing kubeconfig path and data",
			config: &ClusterConfig{
				Name:      "test",
				APIServer: "https://k8s.example.com",
				AuthType:  AuthTypeKubeconfig,
				Auth:      AuthConfig{},
			},
			wantErr: true,
		},
		{
			name: "missing token",
			config: &ClusterConfig{
				Name:      "test",
				APIServer: "https://k8s.example.com",
				AuthType:  AuthTypeToken,
				Auth:      AuthConfig{},
			},
			wantErr: true,
		},
		{
			name: "missing certificate data",
			config: &ClusterConfig{
				Name:      "test",
				APIServer: "https://k8s.example.com",
				AuthType:  AuthTypeCertificate,
				Auth: AuthConfig{
					CertData: "cert-data",
					// KeyData missing
				},
			},
			wantErr: true,
		},
		{
			name: "unsupported auth type",
			config: &ClusterConfig{
				Name:      "test",
				APIServer: "https://k8s.example.com",
				AuthType:  "unsupported",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestClusterConfig_Clone(t *testing.T) {
	original := &ClusterConfig{
		Name:        "test",
		Description: "Test cluster",
		Environment: "test",
		AuthType:    AuthTypeKubeconfig,
		Labels: map[string]string{
			"env": "test",
		},
		Annotations: map[string]string{
			"note": "test cluster",
		},
	}

	clone := original.Clone()

	// 验证克隆的内容相同
	assert.Equal(t, original.Name, clone.Name)
	assert.Equal(t, original.Description, clone.Description)
	assert.Equal(t, original.Environment, clone.Environment)
	assert.Equal(t, original.AuthType, clone.AuthType)

	// 验证map是深拷贝
	assert.Equal(t, original.Labels, clone.Labels)
	assert.Equal(t, original.Annotations, clone.Annotations)

	// 修改克隆不应该影响原始对象
	clone.Labels["env"] = "modified"
	assert.NotEqual(t, original.Labels["env"], clone.Labels["env"])
}

func TestClusterConfig_Merge(t *testing.T) {
	base := &ClusterConfig{
		Name:        "test",
		Description: "Base cluster",
		Environment: "test",
		AuthType:    AuthTypeKubeconfig,
		Labels: map[string]string{
			"env":  "test",
			"base": "true",
		},
		Priority: 1,
		Weight:   1,
	}

	other := &ClusterConfig{
		Description: "Updated cluster",
		Environment: "prod",
		Labels: map[string]string{
			"env":     "prod",
			"updated": "true",
		},
		Priority: 2,
		Weight:   3,
	}

	base.Merge(other)

	// 验证合并结果
	assert.Equal(t, "test", base.Name)                   // 名称不变
	assert.Equal(t, "Updated cluster", base.Description) // 描述被更新
	assert.Equal(t, "prod", base.Environment)            // 环境被更新
	assert.Equal(t, int(2), base.Priority)               // 优先级被更新
	assert.Equal(t, int(3), base.Weight)                 // 权重被更新

	// 验证标签合并
	assert.Equal(t, "prod", base.Labels["env"])     // 被覆盖
	assert.Equal(t, "true", base.Labels["base"])    // 保留
	assert.Equal(t, "true", base.Labels["updated"]) // 新增
}

func TestDefaultConfigs(t *testing.T) {
	// 测试默认集群配置
	clusterConfig := DefaultClusterConfig()
	assert.NotNil(t, clusterConfig)
	assert.Equal(t, "default", clusterConfig.Name)
	assert.Equal(t, AuthTypeKubeconfig, clusterConfig.AuthType)
	assert.True(t, clusterConfig.Enabled)
	assert.True(t, clusterConfig.HealthCheck.Enabled)

	// 测试默认多集群配置
	multiConfig := DefaultMultiClusterConfig()
	assert.NotNil(t, multiConfig)
	assert.Equal(t, LoadBalanceRoundRobin, multiConfig.LoadBalanceStrategy)
	assert.NotZero(t, multiConfig.GlobalTimeout)
	assert.NotZero(t, multiConfig.HealthCheckPeriod)
}

func TestClientPool(t *testing.T) {
	config := DefaultMultiClusterConfig()
	pool := NewClientPool(config)
	require.NotNil(t, pool)

	// 测试空池
	clusters := pool.ListClusters()
	assert.Empty(t, clusters)

	// 测试获取不存在的集群
	_, err := pool.GetClient("nonexistent")
	assert.Error(t, err)

	// 测试连接状态
	connected := pool.IsConnected("nonexistent")
	assert.False(t, connected)
}

func TestHealthChecker(t *testing.T) {
	config := DefaultMultiClusterConfig()
	pool := NewClientPool(config)
	checker := NewHealthChecker(pool)
	require.NotNil(t, checker)

	// 测试获取不存在集群的健康状态
	_, err := checker.GetClusterHealth("nonexistent")
	assert.Error(t, err)

	// 测试健康集群列表
	healthyClusters := checker.GetHealthyClusters()
	assert.Empty(t, healthyClusters)

	// 测试不健康集群列表
	unhealthyClusters := checker.GetUnhealthyClusters()
	assert.Empty(t, unhealthyClusters)

	// 测试健康状态摘要
	summary := checker.GetHealthSummary()
	assert.NotNil(t, summary)
	assert.Equal(t, 0, summary["total_clusters"])
}

func TestOperations(t *testing.T) {
	config := DefaultMultiClusterConfig()
	pool := NewClientPool(config)
	checker := NewHealthChecker(pool)
	ops := NewOperations(pool, checker)
	require.NotNil(t, ops)

	ctx := context.Background()

	// 测试从不存在的集群列出Pod
	_, err := ops.ListPods(ctx, "nonexistent", "default")
	assert.Error(t, err)

	// 测试从所有集群列出Pod（没有集群）
	_, err = ops.ListPodsFromAllClusters(ctx, "default")
	assert.Error(t, err)

	// 测试集群选择器
	selector := &ClusterSelector{
		Names: []string{"nonexistent"},
	}
	clusters, err := ops.GetClustersBySelector(selector)
	assert.NoError(t, err)
	assert.Empty(t, clusters)
}

func TestLoadBalanceStrategies(t *testing.T) {
	config := &MultiClusterConfig{
		LoadBalanceStrategy: LoadBalanceRoundRobin,
	}
	manager, err := NewManager(config)
	require.NoError(t, err)

	// 测试轮询选择（没有集群）
	clusters := []string{}
	_, err = manager.selectRoundRobin(clusters)
	assert.Error(t, err)

	// 测试加权选择（没有集群）
	_, err = manager.selectWeighted(clusters)
	assert.Error(t, err)

	// 测试随机选择（没有集群）
	_, err = manager.selectRandom(clusters)
	assert.Error(t, err)

	// 测试最少连接选择（没有集群）
	_, err = manager.selectLeastConnections(clusters)
	assert.Error(t, err)

	// 测试有集群的情况
	clusters = []string{"cluster1", "cluster2", "cluster3"}

	// 轮询选择
	cluster1, err := manager.selectRoundRobin(clusters)
	assert.NoError(t, err)
	assert.Contains(t, clusters, cluster1)

	cluster2, err := manager.selectRoundRobin(clusters)
	assert.NoError(t, err)
	assert.Contains(t, clusters, cluster2)

	// 随机选择
	randomCluster, err := manager.selectRandom(clusters)
	assert.NoError(t, err)
	assert.Contains(t, clusters, randomCluster)
}

func TestManagerStats(t *testing.T) {
	manager, err := NewManager(nil)
	require.NoError(t, err)

	stats := manager.GetStats()
	assert.NotNil(t, stats)
	assert.Contains(t, stats, "pool")
	assert.Contains(t, stats, "health")
	assert.Contains(t, stats, "config")

	poolStats := stats["pool"].(map[string]interface{})
	assert.Contains(t, poolStats, "total_clusters")
	assert.Contains(t, poolStats, "connected_clusters")

	healthStats := stats["health"].(map[string]interface{})
	assert.Contains(t, healthStats, "total_clusters")
	assert.Contains(t, healthStats, "healthy_clusters")

	configStats := stats["config"].(map[string]interface{})
	assert.Contains(t, configStats, "load_balance_strategy")
	assert.Contains(t, configStats, "global_timeout")
}

func TestManager_GetHealthyClusters(t *testing.T) {
	manager, err := NewManager(nil)
	require.NoError(t, err)
	defer manager.Stop()

	// 初始状态下应该没有健康的集群
	healthyClusters := manager.GetHealthyClusters()
	assert.Empty(t, healthyClusters)

	// 注意：由于我们没有真实的集群连接，这个测试主要验证方法调用不会出错
	// 在实际环境中，健康检查器会定期更新集群状态
}

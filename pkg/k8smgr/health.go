package k8smgr

import (
	"context"
	"fmt"
	"sync"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
)

// HealthChecker 健康检查器
type HealthChecker struct {
	pool   *ClientPool
	stopCh chan struct{}
	wg     sync.WaitGroup
	mu     sync.RWMutex

	// 健康状态
	healthStatus map[string]*HealthStatus
}

// HealthStatus 健康状态
type HealthStatus struct {
	ClusterName      string        `json:"cluster_name"`
	Status           ClusterStatus `json:"status"`
	LastCheck        time.Time     `json:"last_check"`
	LastError        string        `json:"last_error"`
	CheckCount       int64         `json:"check_count"`
	FailureCount     int64         `json:"failure_count"`
	ConsecutiveFails int           `json:"consecutive_fails"`
	ServerVersion    string        `json:"server_version"`
	NodeCount        int32         `json:"node_count"`
	ResponseTime     time.Duration `json:"response_time"`
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(pool *ClientPool) *HealthChecker {
	return &HealthChecker{
		pool:         pool,
		stopCh:       make(chan struct{}),
		healthStatus: make(map[string]*HealthStatus),
	}
}

// Start 启动健康检查
func (h *HealthChecker) Start(ctx context.Context) {
	h.wg.Add(1)
	go h.healthCheckLoop(ctx)
}

// Stop 停止健康检查
func (h *HealthChecker) Stop() {
	close(h.stopCh)
	h.wg.Wait()
}

// healthCheckLoop 健康检查循环
func (h *HealthChecker) healthCheckLoop(ctx context.Context) {
	defer h.wg.Done()

	ticker := time.NewTicker(30 * time.Second) // 默认30秒检查一次
	defer ticker.Stop()

	// 立即执行一次健康检查
	h.checkAllClusters()

	for {
		select {
		case <-ctx.Done():
			return
		case <-h.stopCh:
			return
		case <-ticker.C:
			h.checkAllClusters()
		}
	}
}

// checkAllClusters 检查所有集群健康状态
func (h *HealthChecker) checkAllClusters() {
	clusters := h.pool.ListClusters()

	for _, clusterName := range clusters {
		h.wg.Add(1)
		go func(name string) {
			defer h.wg.Done()
			h.checkClusterHealth(name)
		}(clusterName)
	}
}

// checkClusterHealth 检查单个集群健康状态
func (h *HealthChecker) checkClusterHealth(clusterName string) {
	startTime := time.Now()

	h.mu.Lock()
	status, exists := h.healthStatus[clusterName]
	if !exists {
		status = &HealthStatus{
			ClusterName: clusterName,
			Status:      ClusterStatusConnecting,
		}
		h.healthStatus[clusterName] = status
	}
	h.mu.Unlock()

	// 更新检查计数
	status.CheckCount++
	status.LastCheck = startTime

	// 获取客户端
	client, err := h.pool.GetClient(clusterName)
	if err != nil {
		h.handleHealthCheckFailure(status, fmt.Sprintf("failed to get client: %v", err))
		return
	}

	// 执行健康检查
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 检查API服务器版本
	version, err := client.Discovery().ServerVersion()
	if err != nil {
		h.handleHealthCheckFailure(status, fmt.Sprintf("failed to get server version: %v", err))
		return
	}

	// 检查节点数量
	nodes, err := client.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		h.handleHealthCheckFailure(status, fmt.Sprintf("failed to list nodes: %v", err))
		return
	}

	// 更新健康状态
	status.Status = ClusterStatusHealthy
	status.LastError = ""
	status.ConsecutiveFails = 0
	status.ServerVersion = version.String()
	status.NodeCount = int32(len(nodes.Items))
	status.ResponseTime = time.Since(startTime)

	klog.V(4).Infof("Health check passed for cluster %s, response time: %v", clusterName, status.ResponseTime)
}

// handleHealthCheckFailure 处理健康检查失败
func (h *HealthChecker) handleHealthCheckFailure(status *HealthStatus, errorMsg string) {
	status.FailureCount++
	status.ConsecutiveFails++
	status.LastError = errorMsg
	status.Status = ClusterStatusUnhealthy

	klog.Warningf("Health check failed for cluster %s (consecutive fails: %d): %s",
		status.ClusterName, status.ConsecutiveFails, errorMsg)

	// 获取集群配置
	wrapper, err := h.pool.GetClusterInfo(status.ClusterName)
	if err != nil {
		klog.Errorf("Failed to get cluster info for %s: %v", status.ClusterName, err)
		return
	}

	// 检查是否需要重连
	if status.ConsecutiveFails >= wrapper.config.HealthCheck.FailureThreshold {
		klog.Infof("Attempting to reconnect cluster %s after %d consecutive failures",
			status.ClusterName, status.ConsecutiveFails)

		if err := h.pool.Reconnect(status.ClusterName); err != nil {
			klog.Errorf("Failed to reconnect cluster %s: %v", status.ClusterName, err)
			status.Status = ClusterStatusDisconnected
		} else {
			klog.Infof("Successfully reconnected cluster %s", status.ClusterName)
			status.ConsecutiveFails = 0
			status.Status = ClusterStatusConnecting
		}
	}
}

// GetClusterHealth 获取指定集群的健康状态
func (h *HealthChecker) GetClusterHealth(clusterName string) (*HealthStatus, error) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	status, exists := h.healthStatus[clusterName]
	if !exists {
		return nil, fmt.Errorf("health status not found for cluster %s", clusterName)
	}

	// 返回副本以避免并发修改
	statusCopy := *status
	return &statusCopy, nil
}

// GetAllHealth 获取所有集群的健康状态
func (h *HealthChecker) GetAllHealth() map[string]*HealthStatus {
	h.mu.RLock()
	defer h.mu.RUnlock()

	result := make(map[string]*HealthStatus)
	for name, status := range h.healthStatus {
		statusCopy := *status
		result[name] = &statusCopy
	}

	return result
}

// IsClusterHealthy 检查集群是否健康
func (h *HealthChecker) IsClusterHealthy(clusterName string) bool {
	status, err := h.GetClusterHealth(clusterName)
	if err != nil {
		return false
	}

	return status.Status == ClusterStatusHealthy
}

// GetHealthyCluster 获取健康的集群列表
func (h *HealthChecker) GetHealthyClusters() []string {
	h.mu.RLock()
	defer h.mu.RUnlock()

	var healthyClusters []string
	for name, status := range h.healthStatus {
		if status.Status == ClusterStatusHealthy {
			healthyClusters = append(healthyClusters, name)
		}
	}

	return healthyClusters
}

// GetUnhealthyClusters 获取不健康的集群列表
func (h *HealthChecker) GetUnhealthyClusters() []string {
	h.mu.RLock()
	defer h.mu.RUnlock()

	var unhealthyClusters []string
	for name, status := range h.healthStatus {
		if status.Status != ClusterStatusHealthy {
			unhealthyClusters = append(unhealthyClusters, name)
		}
	}

	return unhealthyClusters
}

// ForceHealthCheck 强制执行健康检查
func (h *HealthChecker) ForceHealthCheck(clusterName string) error {
	clusters := h.pool.ListClusters()
	found := false
	for _, name := range clusters {
		if name == clusterName {
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("cluster %s not found", clusterName)
	}

	go h.checkClusterHealth(clusterName)
	return nil
}

// GetHealthSummary 获取健康状态摘要
func (h *HealthChecker) GetHealthSummary() map[string]interface{} {
	h.mu.RLock()
	defer h.mu.RUnlock()

	summary := map[string]interface{}{
		"total_clusters":        len(h.healthStatus),
		"healthy_clusters":      0,
		"unhealthy_clusters":    0,
		"connecting_clusters":   0,
		"disconnected_clusters": 0,
		"last_check_time":       time.Time{},
	}

	var lastCheckTime time.Time
	for _, status := range h.healthStatus {
		switch status.Status {
		case ClusterStatusHealthy:
			summary["healthy_clusters"] = summary["healthy_clusters"].(int) + 1
		case ClusterStatusUnhealthy:
			summary["unhealthy_clusters"] = summary["unhealthy_clusters"].(int) + 1
		case ClusterStatusConnecting:
			summary["connecting_clusters"] = summary["connecting_clusters"].(int) + 1
		case ClusterStatusDisconnected:
			summary["disconnected_clusters"] = summary["disconnected_clusters"].(int) + 1
		}

		if status.LastCheck.After(lastCheckTime) {
			lastCheckTime = status.LastCheck
		}
	}

	summary["last_check_time"] = lastCheckTime
	return summary
}

// RemoveClusterHealth 移除集群健康状态
func (h *HealthChecker) RemoveClusterHealth(clusterName string) {
	h.mu.Lock()
	defer h.mu.Unlock()

	delete(h.healthStatus, clusterName)
	klog.Infof("Removed health status for cluster %s", clusterName)
}

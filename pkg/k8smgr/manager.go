package k8smgr

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/klog/v2"
)

// Manager k8s集群管理器实现
type Manager struct {
	config     *MultiClusterConfig
	pool       *ClientPool
	checker    *HealthChecker
	ops        *Operations
	kruiseGame KruiseGameOperations

	// 负载均衡
	roundRobinIndex int
	mu              sync.RWMutex

	// 生命周期
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewManager 创建新的集群管理器
func NewManager(config *MultiClusterConfig) (*Manager, error) {
	if config == nil {
		config = DefaultMultiClusterConfig()
	}

	// 创建客户端池
	pool := NewClientPool(config)

	// 创建健康检查器
	checker := NewHealthChecker(pool)

	// 创建操作接口
	ops := NewOperations(pool, checker)

	ctx, cancel := context.WithCancel(context.Background())

	manager := &Manager{
		config:  config,
		pool:    pool,
		checker: checker,
		ops:     ops,
		ctx:     ctx,
		cancel:  cancel,
	}

	// 创建 OpenKruise Game 操作接口
	manager.kruiseGame = NewKruiseGameOperations(manager)

	// 添加配置中的集群
	for _, clusterConfig := range config.Clusters {
		if err := manager.AddCluster(clusterConfig); err != nil {
			klog.Warningf("Failed to add cluster %s: %v", clusterConfig.GetDisplayName(), err)
		}
	}

	return manager, nil
}

// Start 启动集群管理器
func (m *Manager) Start(ctx context.Context) error {
	klog.Info("Starting k8s cluster manager")

	// 启动健康检查
	m.checker.Start(ctx)

	// 启动清理协程
	m.wg.Add(1)
	go m.cleanupLoop()

	klog.Info("K8s cluster manager started successfully")
	return nil
}

// Stop 停止集群管理器
func (m *Manager) Stop() error {
	klog.Info("Stopping k8s cluster manager")

	// 取消上下文
	m.cancel()

	// 停止健康检查
	m.checker.Stop()

	// 等待协程结束
	m.wg.Wait()

	// 关闭连接池
	m.pool.Close()

	klog.Info("K8s cluster manager stopped")
	return nil
}

// AddCluster 添加集群
func (m *Manager) AddCluster(config *ClusterConfig) error {
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid cluster config: %w", err)
	}

	if err := m.pool.AddCluster(config); err != nil {
		return fmt.Errorf("failed to add cluster to pool: %w", err)
	}

	klog.Infof("Added cluster %s to manager", config.GetDisplayName())
	return nil
}

// RemoveCluster 移除集群
func (m *Manager) RemoveCluster(name string) error {
	if err := m.pool.RemoveCluster(name); err != nil {
		return fmt.Errorf("failed to remove cluster from pool: %w", err)
	}

	// 移除健康状态
	m.checker.RemoveClusterHealth(name)

	klog.Infof("Removed cluster %s from manager", name)
	return nil
}

// GetCluster 获取集群信息
func (m *Manager) GetCluster(name string) (*ClusterInfo, error) {
	wrapper, err := m.pool.GetClusterInfo(name)
	if err != nil {
		return nil, err
	}

	health, err := m.checker.GetClusterHealth(name)
	if err != nil {
		// 如果没有健康状态，创建默认状态
		health = &HealthStatus{
			ClusterName: name,
			Status:      ClusterStatusConnecting,
		}
	}

	wrapper.mu.RLock()
	info := &ClusterInfo{
		Config:              wrapper.config,
		Status:              health.Status,
		Client:              wrapper.client,
		RestConfig:          wrapper.restConfig,
		LastHealthCheck:     health.LastCheck,
		LastErrorMsg:        health.LastError,
		ConnectedAt:         wrapper.createdAt,
		HealthCheckCount:    health.CheckCount,
		HealthCheckFailures: health.FailureCount,
		RequestCount:        wrapper.requestCount,
		RequestFailures:     wrapper.errorCount,
		ServerVersion:       health.ServerVersion,
		NodeCount:           health.NodeCount,
	}
	wrapper.mu.RUnlock()

	return info, nil
}

// ListClusters 列出所有集群信息
func (m *Manager) ListClusters() []*ClusterInfo {
	clusterNames := m.pool.ListClusters()
	var clusters []*ClusterInfo

	for _, name := range clusterNames {
		if info, err := m.GetCluster(name); err == nil {
			clusters = append(clusters, info)
		}
	}

	return clusters
}

// UpdateCluster 更新集群配置
func (m *Manager) UpdateCluster(name string, config *ClusterConfig) error {
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid cluster config: %w", err)
	}

	if err := m.pool.UpdateCluster(name, config); err != nil {
		return fmt.Errorf("failed to update cluster in pool: %w", err)
	}

	klog.Infof("Updated cluster %s configuration", name)
	return nil
}

// GetClient 获取指定集群的客户端
func (m *Manager) GetClient(clusterName string) (kubernetes.Interface, error) {
	return m.pool.GetClient(clusterName)
}

// GetRestConfig 获取指定集群的rest配置
func (m *Manager) GetRestConfig(clusterName string) (*rest.Config, error) {
	return m.pool.GetRestConfig(clusterName)
}

// GetDefaultClient 获取默认集群的客户端
func (m *Manager) GetDefaultClient() (kubernetes.Interface, error) {
	if m.config.DefaultCluster != "" {
		return m.GetClient(m.config.DefaultCluster)
	}

	// 如果没有设置默认集群，使用负载均衡选择
	clusterName, err := m.selectClusterByLoadBalance()
	if err != nil {
		return nil, err
	}

	return m.GetClient(clusterName)
}

// CheckHealth 检查指定集群健康状态
func (m *Manager) CheckHealth(clusterName string) error {
	return m.checker.ForceHealthCheck(clusterName)
}

// CheckAllHealth 检查所有集群健康状态
func (m *Manager) CheckAllHealth() map[string]error {
	healthStatus := m.checker.GetAllHealth()
	result := make(map[string]error)

	for name, status := range healthStatus {
		if status.Status != ClusterStatusHealthy {
			result[name] = fmt.Errorf("cluster %s is %s: %s", name, status.Status, status.LastError)
		} else {
			result[name] = nil
		}
	}

	return result
}

// GetClusterStatus 获取集群状态
func (m *Manager) GetClusterStatus(clusterName string) ClusterStatus {
	health, err := m.checker.GetClusterHealth(clusterName)
	if err != nil {
		return ClusterStatusDisconnected
	}

	return health.Status
}

// GetHealthyClusters 获取所有健康的集群
func (m *Manager) GetHealthyClusters() []string {
	return m.checker.GetHealthyClusters()
}

// IsClusterHealthy 检查集群是否健康
func (m *Manager) IsClusterHealthy(clusterName string) bool {
	return m.checker.IsClusterHealthy(clusterName)
}

// GetOperations 获取操作接口
func (m *Manager) GetOperations() *Operations {
	return m.ops
}

// selectClusterByLoadBalance 根据负载均衡策略选择集群
func (m *Manager) selectClusterByLoadBalance() (string, error) {
	healthyClusters := m.checker.GetHealthyClusters()
	if len(healthyClusters) == 0 {
		return "", fmt.Errorf("no healthy clusters available")
	}

	switch m.config.LoadBalanceStrategy {
	case LoadBalanceRoundRobin:
		return m.selectRoundRobin(healthyClusters)
	case LoadBalanceWeighted:
		return m.selectWeighted(healthyClusters)
	case LoadBalanceRandom:
		return m.selectRandom(healthyClusters)
	case LoadBalanceLeastConnections:
		return m.selectLeastConnections(healthyClusters)
	default:
		return m.selectRoundRobin(healthyClusters)
	}
}

// selectRoundRobin 轮询选择
func (m *Manager) selectRoundRobin(clusters []string) (string, error) {
	if len(clusters) == 0 {
		return "", fmt.Errorf("no clusters available")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	cluster := clusters[m.roundRobinIndex%len(clusters)]
	m.roundRobinIndex++

	return cluster, nil
}

// selectWeighted 加权选择
func (m *Manager) selectWeighted(clusters []string) (string, error) {
	if len(clusters) == 0 {
		return "", fmt.Errorf("no clusters available")
	}

	// 计算总权重
	totalWeight := 0
	clusterWeights := make(map[string]int)

	for _, clusterName := range clusters {
		wrapper, err := m.pool.GetClusterInfo(clusterName)
		if err != nil {
			continue
		}
		weight := wrapper.config.Weight
		if weight <= 0 {
			weight = 1
		}
		clusterWeights[clusterName] = weight
		totalWeight += weight
	}

	if totalWeight == 0 {
		return clusters[0], nil
	}

	// 随机选择
	random := rand.Intn(totalWeight)
	currentWeight := 0

	for clusterName, weight := range clusterWeights {
		currentWeight += weight
		if random < currentWeight {
			return clusterName, nil
		}
	}

	return clusters[0], nil
}

// selectRandom 随机选择
func (m *Manager) selectRandom(clusters []string) (string, error) {
	if len(clusters) == 0 {
		return "", fmt.Errorf("no clusters available")
	}

	return clusters[rand.Intn(len(clusters))], nil
}

// selectLeastConnections 最少连接选择
func (m *Manager) selectLeastConnections(clusters []string) (string, error) {
	if len(clusters) == 0 {
		return "", fmt.Errorf("no clusters available")
	}

	var selectedCluster string
	minConnections := int64(-1)

	for _, clusterName := range clusters {
		wrapper, err := m.pool.GetClusterInfo(clusterName)
		if err != nil {
			continue
		}

		wrapper.mu.RLock()
		connections := wrapper.requestCount
		wrapper.mu.RUnlock()

		if minConnections == -1 || connections < minConnections {
			minConnections = connections
			selectedCluster = clusterName
		}
	}

	if selectedCluster == "" {
		return clusters[0], nil
	}

	return selectedCluster, nil
}

// cleanupLoop 清理循环
func (m *Manager) cleanupLoop() {
	defer m.wg.Done()

	ticker := time.NewTicker(time.Hour) // 每小时清理一次
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.pool.Cleanup()
		}
	}
}

// GetStats 获取管理器统计信息
func (m *Manager) GetStats() map[string]interface{} {
	poolStats := m.pool.GetStats()
	healthSummary := m.checker.GetHealthSummary()

	return map[string]interface{}{
		"pool":   poolStats,
		"health": healthSummary,
		"config": map[string]interface{}{
			"default_cluster":       m.config.DefaultCluster,
			"load_balance_strategy": m.config.LoadBalanceStrategy,
			"global_timeout":        m.config.GlobalTimeout,
			"health_check_period":   m.config.HealthCheckPeriod,
			"max_idle_conns":        m.config.MaxIdleConns,
			"max_active_conns":      m.config.MaxActiveConns,
			"conn_max_lifetime":     m.config.ConnMaxLifetime,
		},
	}
}

// KruiseGame 获取 OpenKruise Game 操作接口
func (m *Manager) KruiseGame() KruiseGameOperations {
	return m.kruiseGame
}

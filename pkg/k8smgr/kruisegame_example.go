package k8smgr

import (
	"context"
	"fmt"
	"time"

	"k8s.io/klog/v2"
)

// KruiseGameExample OpenKruise Game 使用示例
func KruiseGameExample() {
	// 创建多集群配置
	config := &MultiClusterConfig{
		Clusters: []*ClusterConfig{
			{
				Name:      "game-cluster-1",
				Scope:     "production",
				APIServer: "https://your-k8s-api-server:6443",
				AuthType:  AuthTypeKubeconfig,
				Auth: AuthConfig{
					KubeconfigPath: "~/.kube/config",
					Context:        "game-cluster-1",
				},
			},
		},
	}

	// 创建管理器
	manager, err := NewManager(config)
	if err != nil {
		klog.Fatalf("Failed to create manager: %v", err)
	}

	// 启动管理器
	ctx := context.Background()
	if err := manager.Start(ctx); err != nil {
		klog.Fatalf("Failed to start manager: %v", err)
	}
	defer manager.Stop()

	// 获取 OpenKruise Game 操作接口
	kruiseGame := manager.KruiseGame()

	// 示例1: 创建 GameServerSet
	fmt.Println("=== 创建 GameServerSet ===")
	gssReq := &GameServerSetCreateRequest{
		Name:      "my-game-servers",
		Namespace: "default",
		Replicas:  3,
		Image:     "nginx:latest", // 这里应该是你的游戏服务器镜像
		Ports: []GameServerPort{
			{
				Name:     "game-port",
				Port:     7777,
				Protocol: "TCP",
			},
			{
				Name:     "query-port",
				Port:     7778,
				Protocol: "UDP",
			},
		},
		Env: map[string]string{
			"GAME_MODE":     "PVP",
			"MAX_PLAYERS":   "10",
			"SERVER_REGION": "us-west",
		},
		Labels: map[string]string{
			"game":        "my-awesome-game",
			"environment": "production",
		},
		NetworkType: "Kubernetes-HostPort",
		Resources: ResourceRequirements{
			Requests: ResourceList{
				CPU:    "500m",
				Memory: "1Gi",
			},
			Limits: ResourceList{
				CPU:    "1000m",
				Memory: "2Gi",
			},
		},
	}

	gssInfo, err := kruiseGame.CreateGameServerSet(ctx, "production:game-cluster-1", gssReq)
	if err != nil {
		klog.Errorf("Failed to create GameServerSet: %v", err)
	} else {
		fmt.Printf("Created GameServerSet: %s/%s with %d replicas\n", 
			gssInfo.Namespace, gssInfo.Name, gssInfo.Replicas)
	}

	// 等待一段时间让 GameServer 启动
	time.Sleep(10 * time.Second)

	// 示例2: 列出 GameServer
	fmt.Println("\n=== 列出 GameServer ===")
	gameServers, err := kruiseGame.ListGameServers(ctx, "production:game-cluster-1", &GameServerFilter{
		Namespace: "default",
		State:     GameServerStateReady,
	})
	if err != nil {
		klog.Errorf("Failed to list GameServers: %v", err)
	} else {
		fmt.Printf("Found %d ready GameServers:\n", len(gameServers))
		for _, gs := range gameServers {
			fmt.Printf("  - %s: %s:%s (State: %s, OpsState: %s)\n", 
				gs.Name, gs.InternalAddress, formatPorts(gs.Ports), gs.State, gs.OpsState)
		}
	}

	// 示例3: 伸缩 GameServerSet
	fmt.Println("\n=== 伸缩 GameServerSet ===")
	err = kruiseGame.ScaleGameServerSet(ctx, "production:game-cluster-1", "default", "my-game-servers", 5)
	if err != nil {
		klog.Errorf("Failed to scale GameServerSet: %v", err)
	} else {
		fmt.Println("Successfully scaled GameServerSet to 5 replicas")
	}

	// 示例4: 批量更新 GameServer
	fmt.Println("\n=== 批量更新 GameServer ===")
	updatePriority := 1
	updatedNames, err := kruiseGame.BatchUpdateGameServers(ctx, "production:game-cluster-1", 
		&GameServerFilter{
			Namespace: "default",
			OpsState:  GameServerOpsStateNone,
		}, 
		&GameServerUpdateRequest{
			UpdatePriority: &updatePriority,
			Labels: map[string]string{
				"updated": "true",
			},
		})
	if err != nil {
		klog.Errorf("Failed to batch update GameServers: %v", err)
	} else {
		fmt.Printf("Updated %d GameServers: %v\n", len(updatedNames), updatedNames)
	}

	// 示例5: 获取统计信息
	fmt.Println("\n=== 获取统计信息 ===")
	stats, err := kruiseGame.GetGameServerStats(ctx, "production:game-cluster-1", "default")
	if err != nil {
		klog.Errorf("Failed to get GameServer stats: %v", err)
	} else {
		fmt.Printf("GameServer Stats - Total: %d, Ready: %d, NotReady: %d, Crash: %d\n",
			stats.Total, stats.Ready, stats.NotReady, stats.Crash)
	}

	// 示例6: 跨集群操作
	fmt.Println("\n=== 跨集群统计 ===")
	allStats, err := kruiseGame.GetGameServerStatsFromAllClusters(ctx)
	if err != nil {
		klog.Errorf("Failed to get stats from all clusters: %v", err)
	} else {
		for clusterName, clusterStats := range allStats {
			fmt.Printf("Cluster %s - Total: %d, Ready: %d\n", 
				clusterName, clusterStats.Total, clusterStats.Ready)
		}
	}

	// 示例7: 热更新游戏服务器镜像
	fmt.Println("\n=== 热更新镜像 ===")
	_, err = kruiseGame.UpdateGameServerSet(ctx, "production:game-cluster-1", &GameServerSetUpdateRequest{
		Name:      "my-game-servers",
		Namespace: "default",
		Image:     "nginx:1.21", // 新的镜像版本
		Annotations: map[string]string{
			"update.time": time.Now().Format(time.RFC3339),
			"update.reason": "security-patch",
		},
	})
	if err != nil {
		klog.Errorf("Failed to update GameServerSet image: %v", err)
	} else {
		fmt.Println("Successfully updated GameServerSet image")
	}

	// 示例8: 优雅关闭游戏服务器
	fmt.Println("\n=== 优雅关闭游戏服务器 ===")
	if len(gameServers) > 0 {
		killState := GameServerOpsStateKill
		_, err = kruiseGame.UpdateGameServer(ctx, "production:game-cluster-1", &GameServerUpdateRequest{
			Name:      gameServers[0].Name,
			Namespace: gameServers[0].Namespace,
			OpsState:  &killState,
			Annotations: map[string]string{
				"shutdown.reason": "maintenance",
				"shutdown.time":   time.Now().Format(time.RFC3339),
			},
		})
		if err != nil {
			klog.Errorf("Failed to shutdown GameServer: %v", err)
		} else {
			fmt.Printf("Successfully marked GameServer %s for shutdown\n", gameServers[0].Name)
		}
	}

	fmt.Println("\n=== OpenKruise Game 示例完成 ===")
}

// formatPorts 格式化端口信息
func formatPorts(ports []GameServerPort) string {
	if len(ports) == 0 {
		return "no-ports"
	}
	
	result := ""
	for i, port := range ports {
		if i > 0 {
			result += ","
		}
		result += fmt.Sprintf("%d/%s", port.Port, port.Protocol)
	}
	return result
}

// GameServerAllocationExample 游戏服务器分配示例
func GameServerAllocationExample() {
	// 这个示例展示如何为玩家分配游戏服务器
	config := &MultiClusterConfig{
		Clusters: []*ClusterConfig{
			{
				Name:      "game-cluster-us",
				Scope:     "production",
				APIServer: "https://us-k8s-api:6443",
				AuthType:  AuthTypeKubeconfig,
			},
			{
				Name:      "game-cluster-eu",
				Scope:     "production", 
				APIServer: "https://eu-k8s-api:6443",
				AuthType:  AuthTypeKubeconfig,
			},
		},
		LoadBalanceStrategy: LoadBalanceRoundRobin,
	}

	manager, err := NewManager(config)
	if err != nil {
		klog.Fatalf("Failed to create manager: %v", err)
	}

	ctx := context.Background()
	if err := manager.Start(ctx); err != nil {
		klog.Fatalf("Failed to start manager: %v", err)
	}
	defer manager.Stop()

	kruiseGame := manager.KruiseGame()

	// 为玩家分配游戏服务器
	playerRegion := "us" // 玩家所在区域
	
	// 1. 查找可用的游戏服务器
	availableServers, err := kruiseGame.ListGameServersFromAllClusters(ctx, &GameServerFilter{
		State:    GameServerStateReady,
		OpsState: GameServerOpsStateNone,
		Labels: map[string]string{
			"region": playerRegion,
			"mode":   "pvp",
		},
	})
	if err != nil {
		klog.Errorf("Failed to find available servers: %v", err)
		return
	}

	// 2. 选择最佳服务器（这里简单选择第一个）
	var selectedServer *GameServerInfo
	for clusterName, servers := range availableServers {
		if len(servers) > 0 {
			selectedServer = servers[0]
			fmt.Printf("Selected server %s from cluster %s\n", 
				selectedServer.Name, clusterName)
			break
		}
	}

	if selectedServer == nil {
		fmt.Println("No available game servers found")
		return
	}

	// 3. 标记服务器为已分配
	_, err = kruiseGame.UpdateGameServer(ctx, selectedServer.ClusterName, &GameServerUpdateRequest{
		Name:      selectedServer.Name,
		Namespace: selectedServer.Namespace,
		Labels: map[string]string{
			"allocated":   "true",
			"player-id":   "player-12345",
			"session-id":  "session-67890",
		},
		Annotations: map[string]string{
			"allocation.time": time.Now().Format(time.RFC3339),
		},
	})
	if err != nil {
		klog.Errorf("Failed to allocate server: %v", err)
		return
	}

	fmt.Printf("Successfully allocated server %s:%d for player\n", 
		selectedServer.ExternalAddress, selectedServer.Ports[0].Port)
}

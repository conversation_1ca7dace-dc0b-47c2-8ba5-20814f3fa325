package k8smgr

import (
	"time"
)

// GameServerState 游戏服务器状态
type GameServerState string

const (
	GameServerStateReady    GameServerState = "Ready"    // 游戏服务器就绪
	GameServerStateNotReady GameServerState = "NotReady" // 游戏服务器未就绪
	GameServerStateCrash    GameServerState = "Crash"    // 游戏服务器崩溃
	GameServerStateUpdating GameServerState = "Updating" // 游戏服务器更新中
	GameServerStateUnknown  GameServerState = "Unknown"  // 游戏服务器未知状态
)

// GameServerOpsState 游戏服务器运维状态
type GameServerOpsState string

const (
	GameServerOpsStateNone            GameServerOpsState = "None"
	GameServerOpsStateKill            GameServerOpsState = "Kill"
	GameServerOpsStateWaitToBeDeleted GameServerOpsState = "WaitToBeDeleted"
)

// GameServerInfo 游戏服务器信息
type GameServerInfo struct {
	Name             string             `json:"name"`
	Namespace        string             `json:"namespace"`
	ClusterName      string             `json:"clusterName"`
	State            GameServerState    `json:"state"`
	OpsState         GameServerOpsState `json:"opsState"`
	UpdatePriority   int                `json:"updatePriority"`
	DeletionPriority int                `json:"deletionPriority"`

	// 网络信息
	InternalAddress string           `json:"internalAddress,omitempty"`
	ExternalAddress string           `json:"externalAddress,omitempty"`
	Ports           []GameServerPort `json:"ports,omitempty"`

	// 容器信息
	Images []string `json:"images,omitempty"`

	// 时间信息
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`

	// 标签和注解
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

// GameServerPort 游戏服务器端口信息
type GameServerPort struct {
	Name     string `json:"name"`
	Port     int32  `json:"port"`
	Protocol string `json:"protocol"`
}

// GameServerSetInfo 游戏服务器集合信息
type GameServerSetInfo struct {
	Name        string `json:"name"`
	Namespace   string `json:"namespace"`
	ClusterName string `json:"clusterName"`

	// 副本信息
	Replicas          int32 `json:"replicas"`
	ReadyReplicas     int32 `json:"readyReplicas"`
	AvailableReplicas int32 `json:"availableReplicas"`
	UpdatedReplicas   int32 `json:"updatedReplicas"`

	// 时间信息
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`

	// 标签和注解
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`

	// 游戏服务器列表
	GameServers []GameServerInfo `json:"gameServers,omitempty"`
}

// GameServerFilter 游戏服务器过滤器
type GameServerFilter struct {
	Namespace   string             `json:"namespace,omitempty"`
	State       GameServerState    `json:"state,omitempty"`
	OpsState    GameServerOpsState `json:"opsState,omitempty"`
	Labels      map[string]string  `json:"labels,omitempty"`
	Annotations map[string]string  `json:"annotations,omitempty"`
}

// GameServerSetFilter 游戏服务器集合过滤器
type GameServerSetFilter struct {
	Namespace   string            `json:"namespace,omitempty"`
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

// GameServerUpdateRequest 游戏服务器更新请求
type GameServerUpdateRequest struct {
	Name             string              `json:"name"`
	Namespace        string              `json:"namespace"`
	OpsState         *GameServerOpsState `json:"opsState,omitempty"`
	UpdatePriority   *int                `json:"updatePriority,omitempty"`
	DeletionPriority *int                `json:"deletionPriority,omitempty"`
	Labels           map[string]string   `json:"labels,omitempty"`
	Annotations      map[string]string   `json:"annotations,omitempty"`
}

// GameServerSetUpdateRequest 游戏服务器集合更新请求
type GameServerSetUpdateRequest struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Replicas    *int32            `json:"replicas,omitempty"`
	Image       string            `json:"image,omitempty"`
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

// GameServerSetCreateRequest 游戏服务器集合创建请求
type GameServerSetCreateRequest struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Replicas    int32             `json:"replicas"`
	Image       string            `json:"image"`
	Ports       []GameServerPort  `json:"ports,omitempty"`
	Env         map[string]string `json:"env,omitempty"`
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`

	// 网络配置
	NetworkType string `json:"networkType,omitempty"` // Kubernetes-HostPort, Kubernetes-Ingress 等

	// 资源配置
	Resources ResourceRequirements `json:"resources,omitempty"`
}

// ResourceRequirements 资源需求
type ResourceRequirements struct {
	Requests ResourceList `json:"requests,omitempty"`
	Limits   ResourceList `json:"limits,omitempty"`
}

// ResourceList 资源列表
type ResourceList struct {
	CPU    string `json:"cpu,omitempty" binding:"required,min=0.1,max=4"`
	Memory string `json:"memory,omitempty" binding:"required,min=1024,max=16384"`
}

// GameServerStats 游戏服务器统计信息
type GameServerStats struct {
	Total    int `json:"total"`
	Ready    int `json:"ready"`
	NotReady int `json:"notReady"`
	Crash    int `json:"crash"`
	Updating int `json:"updating"`
	Unknown  int `json:"unknown"`
}

// ClusterGameServerStats 集群游戏服务器统计信息
type ClusterGameServerStats struct {
	ClusterName string          `json:"clusterName"`
	Stats       GameServerStats `json:"stats"`
}

package k8smgr

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/klog/v2"
)

// OpenKruise Game 资源的 GVR (GroupVersionResource)
var (
	GameServerSetGVR = schema.GroupVersionResource{
		Group:    "game.kruise.io",
		Version:  "v1alpha1",
		Resource: "gameserversets",
	}
	GameServerGVR = schema.GroupVersionResource{
		Group:    "game.kruise.io",
		Version:  "v1alpha1",
		Resource: "gameservers",
	}
)

// KruiseGameOperations OpenKruise Game 操作接口
type KruiseGameOperations interface {
	// GameServerSet 操作
	CreateGameServerSet(ctx context.Context, clusterName string, req *GameServerSetCreateRequest) (*GameServerSetInfo, error)
	GetGameServerSet(ctx context.Context, clusterName, namespace, name string) (*GameServerSetInfo, error)
	ListGameServerSets(ctx context.Context, clusterName string, filter *GameServerSetFilter) ([]*GameServerSetInfo, error)
	UpdateGameServerSet(ctx context.Context, clusterName string, req *GameServerSetUpdateRequest) (*GameServerSetInfo, error)
	DeleteGameServerSet(ctx context.Context, clusterName, namespace, name string) error
	ScaleGameServerSet(ctx context.Context, clusterName, namespace, name string, replicas int32) error

	// GameServer 操作
	GetGameServer(ctx context.Context, clusterName, namespace, name string) (*GameServerInfo, error)
	ListGameServers(ctx context.Context, clusterName string, filter *GameServerFilter) ([]*GameServerInfo, error)
	UpdateGameServer(ctx context.Context, clusterName string, req *GameServerUpdateRequest) (*GameServerInfo, error)
	DeleteGameServer(ctx context.Context, clusterName, namespace, name string) error

	// 批量操作
	BatchUpdateGameServers(ctx context.Context, clusterName string, filter *GameServerFilter, updates *GameServerUpdateRequest) ([]string, error)
	BatchDeleteGameServers(ctx context.Context, clusterName string, filter *GameServerFilter) ([]string, error)

	// 统计信息
	GetGameServerStats(ctx context.Context, clusterName, namespace string) (*GameServerStats, error)
	GetAllClustersGameServerStats(ctx context.Context) ([]*ClusterGameServerStats, error)

	// 跨集群操作
	ListGameServersFromAllClusters(ctx context.Context, filter *GameServerFilter) (map[string][]*GameServerInfo, error)
	GetGameServerStatsFromAllClusters(ctx context.Context) (map[string]*GameServerStats, error)
}

// kruiseGameOperations OpenKruise Game 操作实现
type kruiseGameOperations struct {
	manager *Manager
}

// NewKruiseGameOperations 创建 OpenKruise Game 操作实例
func NewKruiseGameOperations(manager *Manager) KruiseGameOperations {
	return &kruiseGameOperations{
		manager: manager,
	}
}

// getDynamicClient 获取动态客户端
func (k *kruiseGameOperations) getDynamicClient(clusterName string) (dynamic.Interface, error) {
	// 获取集群的 rest config
	restConfig, err := k.manager.pool.GetRestConfig(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get rest config for cluster %s: %w", clusterName, err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create dynamic client for cluster %s: %w", clusterName, err)
	}

	return dynamicClient, nil
}

// CreateGameServerSet 创建游戏服务器集合
func (k *kruiseGameOperations) CreateGameServerSet(ctx context.Context, clusterName string, req *GameServerSetCreateRequest) (*GameServerSetInfo, error) {
	client, err := k.getDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 构建 GameServerSet 对象
	gss := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "game.kruise.io/v1alpha1",
			"kind":       "GameServerSet",
			"metadata": map[string]interface{}{
				"name":      req.Name,
				"namespace": req.Namespace,
			},
			"spec": map[string]interface{}{
				"replicas": req.Replicas,
				"gameServerTemplate": map[string]interface{}{
					"spec": map[string]interface{}{
						"containers": []interface{}{
							map[string]interface{}{
								"name":      "game-server",
								"image":     req.Image,
								"ports":     convertPortsToUnstructured(req.Ports),
								"env":       convertEnvToUnstructured(req.Env),
								"resources": convertResourcesToUnstructured(req.Resources),
							},
						},
					},
				},
			},
		},
	}

	// 设置标签和注解
	if req.Labels != nil {
		gss.SetLabels(req.Labels)
	}
	if req.Annotations != nil {
		gss.SetAnnotations(req.Annotations)
	}

	// 输出gss yaml
	gssJson, err := gss.MarshalJSON()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal GameServerSet %s/%s in cluster %s: %w", req.Namespace, req.Name, clusterName, err)
	}
	fmt.Println(string(gssJson))

	// 创建 GameServerSet
	createdGSS, err := client.Resource(GameServerSetGVR).Namespace(req.Namespace).Create(ctx, gss, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to create GameServerSet %s/%s in cluster %s: %w", req.Namespace, req.Name, clusterName, err)
	}

	klog.Infof("Created GameServerSet %s/%s in cluster %s", req.Namespace, req.Name, clusterName)
	return convertUnstructuredToGameServerSetInfo(createdGSS, clusterName), nil
}

// GetGameServerSet 获取游戏服务器集合
func (k *kruiseGameOperations) GetGameServerSet(ctx context.Context, clusterName, namespace, name string) (*GameServerSetInfo, error) {
	client, err := k.getDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	gss, err := client.Resource(GameServerSetGVR).Namespace(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get GameServerSet %s/%s from cluster %s: %w", namespace, name, clusterName, err)
	}

	info := convertUnstructuredToGameServerSetInfo(gss, clusterName)

	// 获取关联的 GameServer 列表
	labelSelector := labels.SelectorFromSet(map[string]string{
		"game.kruise.io/owner-gss": name,
	}).String()

	gameServers, err := client.Resource(GameServerGVR).Namespace(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		klog.Warningf("Failed to list GameServers for GameServerSet %s/%s in cluster %s: %v", namespace, name, clusterName, err)
	} else {
		for _, gs := range gameServers.Items {
			info.GameServers = append(info.GameServers, *convertUnstructuredToGameServerInfo(&gs, clusterName))
		}
	}

	return info, nil
}

// ListGameServerSets 列出游戏服务器集合
func (k *kruiseGameOperations) ListGameServerSets(ctx context.Context, clusterName string, filter *GameServerSetFilter) ([]*GameServerSetInfo, error) {
	client, err := k.getDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	listOptions := metav1.ListOptions{}
	if filter != nil && len(filter.Labels) > 0 {
		listOptions.LabelSelector = labels.SelectorFromSet(filter.Labels).String()
	}

	namespace := metav1.NamespaceAll
	if filter != nil && filter.Namespace != "" {
		namespace = filter.Namespace
	}

	gssList, err := client.Resource(GameServerSetGVR).Namespace(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to list GameServerSets from cluster %s: %w", clusterName, err)
	}

	var result []*GameServerSetInfo
	for _, gss := range gssList.Items {
		info := convertUnstructuredToGameServerSetInfo(&gss, clusterName)
		result = append(result, info)
	}

	return result, nil
}

// UpdateGameServerSet 更新游戏服务器集合
func (k *kruiseGameOperations) UpdateGameServerSet(ctx context.Context, clusterName string, req *GameServerSetUpdateRequest) (*GameServerSetInfo, error) {
	client, err := k.getDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 获取现有的 GameServerSet
	gss, err := client.Resource(GameServerSetGVR).Namespace(req.Namespace).Get(ctx, req.Name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get GameServerSet %s/%s from cluster %s: %w", req.Namespace, req.Name, clusterName, err)
	}

	// 更新字段
	if req.Replicas != nil {
		if err := unstructured.SetNestedField(gss.Object, int64(*req.Replicas), "spec", "replicas"); err != nil {
			return nil, fmt.Errorf("failed to set replicas: %w", err)
		}
	}
	if req.Image != "" {
		if err := unstructured.SetNestedField(gss.Object, req.Image, "spec", "gameServerTemplate", "spec", "template", "spec", "containers", "0", "image"); err != nil {
			return nil, fmt.Errorf("failed to set image: %w", err)
		}
	}
	if req.Labels != nil {
		labels := gss.GetLabels()
		if labels == nil {
			labels = make(map[string]string)
		}
		for k, v := range req.Labels {
			labels[k] = v
		}
		gss.SetLabels(labels)
	}
	if req.Annotations != nil {
		annotations := gss.GetAnnotations()
		if annotations == nil {
			annotations = make(map[string]string)
		}
		for k, v := range req.Annotations {
			annotations[k] = v
		}
		gss.SetAnnotations(annotations)
	}

	// 更新 GameServerSet
	updatedGSS, err := client.Resource(GameServerSetGVR).Namespace(req.Namespace).Update(ctx, gss, metav1.UpdateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to update GameServerSet %s/%s in cluster %s: %w", req.Namespace, req.Name, clusterName, err)
	}

	klog.Infof("Updated GameServerSet %s/%s in cluster %s", req.Namespace, req.Name, clusterName)
	return convertUnstructuredToGameServerSetInfo(updatedGSS, clusterName), nil
}

// DeleteGameServerSet 删除游戏服务器集合
func (k *kruiseGameOperations) DeleteGameServerSet(ctx context.Context, clusterName, namespace, name string) error {
	client, err := k.getDynamicClient(clusterName)
	if err != nil {
		return err
	}

	err = client.Resource(GameServerSetGVR).Namespace(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete GameServerSet %s/%s from cluster %s: %w", namespace, name, clusterName, err)
	}

	klog.Infof("Deleted GameServerSet %s/%s from cluster %s", namespace, name, clusterName)
	return nil
}

// ScaleGameServerSet 伸缩游戏服务器集合
func (k *kruiseGameOperations) ScaleGameServerSet(ctx context.Context, clusterName, namespace, name string, replicas int32) error {
	_, err := k.UpdateGameServerSet(ctx, clusterName, &GameServerSetUpdateRequest{
		Name:      name,
		Namespace: namespace,
		Replicas:  &replicas,
	})
	return err
}

// GetGameServer 获取游戏服务器
func (k *kruiseGameOperations) GetGameServer(ctx context.Context, clusterName, namespace, name string) (*GameServerInfo, error) {
	client, err := k.getDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	gs, err := client.Resource(GameServerGVR).Namespace(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get GameServer %s/%s from cluster %s: %w", namespace, name, clusterName, err)
	}

	return convertUnstructuredToGameServerInfo(gs, clusterName), nil
}

// ListGameServers 列出游戏服务器
func (k *kruiseGameOperations) ListGameServers(ctx context.Context, clusterName string, filter *GameServerFilter) ([]*GameServerInfo, error) {
	client, err := k.getDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	listOptions := metav1.ListOptions{}
	if filter != nil && len(filter.Labels) > 0 {
		listOptions.LabelSelector = labels.SelectorFromSet(filter.Labels).String()
	}

	namespace := metav1.NamespaceAll
	if filter != nil && filter.Namespace != "" {
		namespace = filter.Namespace
	}

	gsList, err := client.Resource(GameServerGVR).Namespace(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to list GameServers from cluster %s: %w", clusterName, err)
	}

	var result []*GameServerInfo
	for _, gs := range gsList.Items {
		info := convertUnstructuredToGameServerInfo(&gs, clusterName)

		// 应用过滤器
		if filter != nil {
			if filter.State != "" && info.State != filter.State {
				continue
			}
			if filter.OpsState != "" && info.OpsState != filter.OpsState {
				continue
			}
		}

		result = append(result, info)
	}

	return result, nil
}

// UpdateGameServer 更新游戏服务器
func (k *kruiseGameOperations) UpdateGameServer(ctx context.Context, clusterName string, req *GameServerUpdateRequest) (*GameServerInfo, error) {
	client, err := k.getDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 获取现有的 GameServer
	gs, err := client.Resource(GameServerGVR).Namespace(req.Namespace).Get(ctx, req.Name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get GameServer %s/%s from cluster %s: %w", req.Namespace, req.Name, clusterName, err)
	}

	// 更新字段
	if req.OpsState != nil {
		if err := unstructured.SetNestedField(gs.Object, string(*req.OpsState), "spec", "opsState"); err != nil {
			return nil, fmt.Errorf("failed to set opsState: %w", err)
		}
	}
	if req.UpdatePriority != nil {
		if err := unstructured.SetNestedField(gs.Object, int64(*req.UpdatePriority), "spec", "updatePriority"); err != nil {
			return nil, fmt.Errorf("failed to set updatePriority: %w", err)
		}
	}
	if req.DeletionPriority != nil {
		if err := unstructured.SetNestedField(gs.Object, int64(*req.DeletionPriority), "spec", "deletionPriority"); err != nil {
			return nil, fmt.Errorf("failed to set deletionPriority: %w", err)
		}
	}
	if req.Labels != nil {
		labels := gs.GetLabels()
		if labels == nil {
			labels = make(map[string]string)
		}
		for k, v := range req.Labels {
			labels[k] = v
		}
		gs.SetLabels(labels)
	}
	if req.Annotations != nil {
		annotations := gs.GetAnnotations()
		if annotations == nil {
			annotations = make(map[string]string)
		}
		for k, v := range req.Annotations {
			annotations[k] = v
		}
		gs.SetAnnotations(annotations)
	}

	// 更新 GameServer
	updatedGS, err := client.Resource(GameServerGVR).Namespace(req.Namespace).Update(ctx, gs, metav1.UpdateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to update GameServer %s/%s in cluster %s: %w", req.Namespace, req.Name, clusterName, err)
	}

	klog.Infof("Updated GameServer %s/%s in cluster %s", req.Namespace, req.Name, clusterName)
	return convertUnstructuredToGameServerInfo(updatedGS, clusterName), nil
}

// DeleteGameServer 删除游戏服务器
func (k *kruiseGameOperations) DeleteGameServer(ctx context.Context, clusterName, namespace, name string) error {
	client, err := k.getDynamicClient(clusterName)
	if err != nil {
		return err
	}

	err = client.Resource(GameServerGVR).Namespace(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete GameServer %s/%s from cluster %s: %w", namespace, name, clusterName, err)
	}

	klog.Infof("Deleted GameServer %s/%s from cluster %s", namespace, name, clusterName)
	return nil
}

// BatchUpdateGameServers 批量更新游戏服务器
func (k *kruiseGameOperations) BatchUpdateGameServers(ctx context.Context, clusterName string, filter *GameServerFilter, updates *GameServerUpdateRequest) ([]string, error) {
	// 先获取符合条件的 GameServer 列表
	gameServers, err := k.ListGameServers(ctx, clusterName, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to list GameServers for batch update: %w", err)
	}

	var updatedNames []string
	for _, gs := range gameServers {
		updateReq := &GameServerUpdateRequest{
			Name:             gs.Name,
			Namespace:        gs.Namespace,
			OpsState:         updates.OpsState,
			UpdatePriority:   updates.UpdatePriority,
			DeletionPriority: updates.DeletionPriority,
			Labels:           updates.Labels,
			Annotations:      updates.Annotations,
		}

		_, err := k.UpdateGameServer(ctx, clusterName, updateReq)
		if err != nil {
			klog.Errorf("Failed to update GameServer %s/%s: %v", gs.Namespace, gs.Name, err)
			continue
		}
		updatedNames = append(updatedNames, gs.Name)
	}

	return updatedNames, nil
}

// BatchDeleteGameServers 批量删除游戏服务器
func (k *kruiseGameOperations) BatchDeleteGameServers(ctx context.Context, clusterName string, filter *GameServerFilter) ([]string, error) {
	// 先获取符合条件的 GameServer 列表
	gameServers, err := k.ListGameServers(ctx, clusterName, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to list GameServers for batch delete: %w", err)
	}

	var deletedNames []string
	for _, gs := range gameServers {
		err := k.DeleteGameServer(ctx, clusterName, gs.Namespace, gs.Name)
		if err != nil {
			klog.Errorf("Failed to delete GameServer %s/%s: %v", gs.Namespace, gs.Name, err)
			continue
		}
		deletedNames = append(deletedNames, gs.Name)
	}

	return deletedNames, nil
}

// GetGameServerStats 获取游戏服务器统计信息
func (k *kruiseGameOperations) GetGameServerStats(ctx context.Context, clusterName, namespace string) (*GameServerStats, error) {
	filter := &GameServerFilter{
		Namespace: namespace,
	}

	gameServers, err := k.ListGameServers(ctx, clusterName, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to list GameServers for stats: %w", err)
	}

	stats := &GameServerStats{}
	for _, gs := range gameServers {
		stats.Total++
		switch gs.State {
		case GameServerStateReady:
			stats.Ready++
		case GameServerStateNotReady:
			stats.NotReady++
		case GameServerStateCrash:
			stats.Crash++
		case GameServerStateUpdating:
			stats.Updating++
		default:
			stats.Unknown++
		}
	}

	return stats, nil
}

// GetAllClustersGameServerStats 获取所有集群的游戏服务器统计信息
func (k *kruiseGameOperations) GetAllClustersGameServerStats(ctx context.Context) ([]*ClusterGameServerStats, error) {
	clusters := k.manager.GetHealthyClusters()
	var result []*ClusterGameServerStats

	for _, clusterName := range clusters {
		stats, err := k.GetGameServerStats(ctx, clusterName, metav1.NamespaceAll)
		if err != nil {
			klog.Errorf("Failed to get GameServer stats for cluster %s: %v", clusterName, err)
			continue
		}

		result = append(result, &ClusterGameServerStats{
			ClusterName: clusterName,
			Stats:       *stats,
		})
	}

	return result, nil
}

// ListGameServersFromAllClusters 从所有集群列出游戏服务器
func (k *kruiseGameOperations) ListGameServersFromAllClusters(ctx context.Context, filter *GameServerFilter) (map[string][]*GameServerInfo, error) {
	clusters := k.manager.GetHealthyClusters()
	result := make(map[string][]*GameServerInfo)

	for _, clusterName := range clusters {
		gameServers, err := k.ListGameServers(ctx, clusterName, filter)
		if err != nil {
			klog.Errorf("Failed to list GameServers from cluster %s: %v", clusterName, err)
			continue
		}
		result[clusterName] = gameServers
	}

	return result, nil
}

// GetGameServerStatsFromAllClusters 从所有集群获取游戏服务器统计信息
func (k *kruiseGameOperations) GetGameServerStatsFromAllClusters(ctx context.Context) (map[string]*GameServerStats, error) {
	clusters := k.manager.GetHealthyClusters()
	result := make(map[string]*GameServerStats)

	for _, clusterName := range clusters {
		stats, err := k.GetGameServerStats(ctx, clusterName, metav1.NamespaceAll)
		if err != nil {
			klog.Errorf("Failed to get GameServer stats from cluster %s: %v", clusterName, err)
			continue
		}
		result[clusterName] = stats
	}

	return result, nil
}

// 辅助函数：转换端口到 unstructured 格式
func convertPortsToUnstructured(ports []GameServerPort) []interface{} {
	var result []interface{}
	for _, port := range ports {
		result = append(result, map[string]interface{}{
			"name":          port.Name,
			"containerPort": port.Port,
			"protocol":      port.Protocol,
		})
	}
	return result
}

// 辅助函数：转换环境变量到 unstructured 格式
func convertEnvToUnstructured(env map[string]string) []interface{} {
	var result []interface{}
	for key, value := range env {
		result = append(result, map[string]interface{}{
			"name":  key,
			"value": value,
		})
	}
	return result
}

// 辅助函数：转换资源配置到 unstructured 格式
func convertResourcesToUnstructured(resources ResourceRequirements) map[string]interface{} {
	result := make(map[string]interface{})

	if resources.Requests.CPU != "" || resources.Requests.Memory != "" {
		requests := make(map[string]interface{})
		if resources.Requests.CPU != "" {
			requests["cpu"] = resources.Requests.CPU
		}
		if resources.Requests.Memory != "" {
			requests["memory"] = resources.Requests.Memory
		}
		result["requests"] = requests
	}

	if resources.Limits.CPU != "" || resources.Limits.Memory != "" {
		limits := make(map[string]interface{})
		if resources.Limits.CPU != "" {
			limits["cpu"] = resources.Limits.CPU
		}
		if resources.Limits.Memory != "" {
			limits["memory"] = resources.Limits.Memory
		}
		result["limits"] = limits
	}

	return result
}

// 辅助函数：转换 unstructured 到 GameServerSetInfo
func convertUnstructuredToGameServerSetInfo(obj *unstructured.Unstructured, clusterName string) *GameServerSetInfo {
	info := &GameServerSetInfo{
		Name:        obj.GetName(),
		Namespace:   obj.GetNamespace(),
		ClusterName: clusterName,
		CreatedAt:   obj.GetCreationTimestamp().Time,
		Labels:      obj.GetLabels(),
		Annotations: obj.GetAnnotations(),
	}

	// 提取 spec 信息
	if replicas, found, err := unstructured.NestedInt64(obj.Object, "spec", "replicas"); found && err == nil {
		info.Replicas = int32(replicas)
	}

	// 提取 status 信息
	if readyReplicas, found, err := unstructured.NestedInt64(obj.Object, "status", "readyReplicas"); found && err == nil {
		info.ReadyReplicas = int32(readyReplicas)
	}
	if availableReplicas, found, err := unstructured.NestedInt64(obj.Object, "status", "availableReplicas"); found && err == nil {
		info.AvailableReplicas = int32(availableReplicas)
	}
	if updatedReplicas, found, err := unstructured.NestedInt64(obj.Object, "status", "updatedReplicas"); found && err == nil {
		info.UpdatedReplicas = int32(updatedReplicas)
	}

	return info
}

// 辅助函数：转换 unstructured 到 GameServerInfo
func convertUnstructuredToGameServerInfo(obj *unstructured.Unstructured, clusterName string) *GameServerInfo {
	info := &GameServerInfo{
		Name:        obj.GetName(),
		Namespace:   obj.GetNamespace(),
		ClusterName: clusterName,
		CreatedAt:   obj.GetCreationTimestamp().Time,
		Labels:      obj.GetLabels(),
		Annotations: obj.GetAnnotations(),
	}

	// 提取 spec 信息
	if opsState, found, err := unstructured.NestedString(obj.Object, "spec", "opsState"); found && err == nil {
		info.OpsState = GameServerOpsState(opsState)
	}
	if updatePriority, found, err := unstructured.NestedInt64(obj.Object, "spec", "updatePriority"); found && err == nil {
		info.UpdatePriority = int(updatePriority)
	}
	if deletionPriority, found, err := unstructured.NestedInt64(obj.Object, "spec", "deletionPriority"); found && err == nil {
		info.DeletionPriority = int(deletionPriority)
	}

	// 提取 status 信息
	if currentState, found, err := unstructured.NestedString(obj.Object, "status", "currentState"); found && err == nil {
		info.State = GameServerState(currentState)
	}

	// 提取网络信息
	if networkStatus, found, err := unstructured.NestedMap(obj.Object, "status", "networkStatus"); found && err == nil {
		if internalAddresses, found, err := unstructured.NestedStringSlice(networkStatus, "internalAddresses"); found && err == nil && len(internalAddresses) > 0 {
			info.InternalAddress = internalAddresses[0]
		}
		if externalAddresses, found, err := unstructured.NestedStringSlice(networkStatus, "externalAddresses"); found && err == nil && len(externalAddresses) > 0 {
			info.ExternalAddress = externalAddresses[0]
		}
		if ports, found, err := unstructured.NestedSlice(networkStatus, "ports"); found && err == nil {
			for _, portInterface := range ports {
				if portMap, ok := portInterface.(map[string]interface{}); ok {
					port := GameServerPort{}
					if name, found, err := unstructured.NestedString(portMap, "name"); found && err == nil {
						port.Name = name
					}
					if portNum, found, err := unstructured.NestedInt64(portMap, "port"); found && err == nil {
						port.Port = int32(portNum)
					}
					if protocol, found, err := unstructured.NestedString(portMap, "protocol"); found && err == nil {
						port.Protocol = protocol
					}
					info.Ports = append(info.Ports, port)
				}
			}
		}
	}

	// 提取镜像信息
	if containers, found, err := unstructured.NestedSlice(obj.Object, "spec", "template", "spec", "containers"); found && err == nil {
		for _, containerInterface := range containers {
			if containerMap, ok := containerInterface.(map[string]interface{}); ok {
				if image, found, err := unstructured.NestedString(containerMap, "image"); found && err == nil {
					info.Images = append(info.Images, image)
				}
			}
		}
	}

	return info
}

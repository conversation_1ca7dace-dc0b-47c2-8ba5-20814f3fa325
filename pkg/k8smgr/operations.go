package k8smgr

import (
	"context"
	"fmt"
	"sync"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"
)

// Operations k8s资源操作实现
type Operations struct {
	pool    *ClientPool
	checker *HealthChecker
}

// NewOperations 创建操作实例
func NewOperations(pool *ClientPool, checker *HealthChecker) *Operations {
	return &Operations{
		pool:    pool,
		checker: checker,
	}
}

// Pod操作

// ListPods 列出指定集群和命名空间的Pod
func (o *Operations) ListPods(ctx context.Context, clusterName, namespace string) (*corev1.PodList, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	pods, err := client.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods in cluster %s namespace %s: %w", clusterName, namespace, err)
	}

	return pods, nil
}

// GetPod 获取指定Pod
func (o *Operations) GetPod(ctx context.Context, clusterName, namespace, name string) (*corev1.Pod, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	pod, err := client.CoreV1().Pods(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get pod %s in cluster %s namespace %s: %w", name, clusterName, namespace, err)
	}

	return pod, nil
}

// CreatePod 创建Pod
func (o *Operations) CreatePod(ctx context.Context, clusterName string, pod *corev1.Pod) (*corev1.Pod, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	createdPod, err := client.CoreV1().Pods(pod.Namespace).Create(ctx, pod, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to create pod %s in cluster %s: %w", pod.Name, clusterName, err)
	}

	klog.Infof("Created pod %s in cluster %s namespace %s", pod.Name, clusterName, pod.Namespace)
	return createdPod, nil
}

// DeletePod 删除Pod
func (o *Operations) DeletePod(ctx context.Context, clusterName, namespace, name string) error {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	err = client.CoreV1().Pods(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete pod %s in cluster %s namespace %s: %w", name, clusterName, namespace, err)
	}

	klog.Infof("Deleted pod %s in cluster %s namespace %s", name, clusterName, namespace)
	return nil
}

// Deployment操作

// ListDeployments 列出Deployment
func (o *Operations) ListDeployments(ctx context.Context, clusterName, namespace string) (*appsv1.DeploymentList, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	deployments, err := client.AppsV1().Deployments(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list deployments in cluster %s namespace %s: %w", clusterName, namespace, err)
	}

	return deployments, nil
}

// GetDeployment 获取指定Deployment
func (o *Operations) GetDeployment(ctx context.Context, clusterName, namespace, name string) (*appsv1.Deployment, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	deployment, err := client.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get deployment %s in cluster %s namespace %s: %w", name, clusterName, namespace, err)
	}

	return deployment, nil
}

// CreateDeployment 创建Deployment
func (o *Operations) CreateDeployment(ctx context.Context, clusterName string, deployment *appsv1.Deployment) (*appsv1.Deployment, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	createdDeployment, err := client.AppsV1().Deployments(deployment.Namespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to create deployment %s in cluster %s: %w", deployment.Name, clusterName, err)
	}

	klog.Infof("Created deployment %s in cluster %s namespace %s", deployment.Name, clusterName, deployment.Namespace)
	return createdDeployment, nil
}

// UpdateDeployment 更新Deployment
func (o *Operations) UpdateDeployment(ctx context.Context, clusterName string, deployment *appsv1.Deployment) (*appsv1.Deployment, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	updatedDeployment, err := client.AppsV1().Deployments(deployment.Namespace).Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to update deployment %s in cluster %s: %w", deployment.Name, clusterName, err)
	}

	klog.Infof("Updated deployment %s in cluster %s namespace %s", deployment.Name, clusterName, deployment.Namespace)
	return updatedDeployment, nil
}

// DeleteDeployment 删除Deployment
func (o *Operations) DeleteDeployment(ctx context.Context, clusterName, namespace, name string) error {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	err = client.AppsV1().Deployments(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete deployment %s in cluster %s namespace %s: %w", name, clusterName, namespace, err)
	}

	klog.Infof("Deleted deployment %s in cluster %s namespace %s", name, clusterName, namespace)
	return nil
}

// Service操作

// ListServices 列出Service
func (o *Operations) ListServices(ctx context.Context, clusterName, namespace string) (*corev1.ServiceList, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	services, err := client.CoreV1().Services(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list services in cluster %s namespace %s: %w", clusterName, namespace, err)
	}

	return services, nil
}

// GetService 获取指定Service
func (o *Operations) GetService(ctx context.Context, clusterName, namespace, name string) (*corev1.Service, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	service, err := client.CoreV1().Services(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get service %s in cluster %s namespace %s: %w", name, clusterName, namespace, err)
	}

	return service, nil
}

// CreateService 创建Service
func (o *Operations) CreateService(ctx context.Context, clusterName string, service *corev1.Service) (*corev1.Service, error) {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	createdService, err := client.CoreV1().Services(service.Namespace).Create(ctx, service, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to create service %s in cluster %s: %w", service.Name, clusterName, err)
	}

	klog.Infof("Created service %s in cluster %s namespace %s", service.Name, clusterName, service.Namespace)
	return createdService, nil
}

// DeleteService 删除Service
func (o *Operations) DeleteService(ctx context.Context, clusterName, namespace, name string) error {
	client, err := o.pool.GetClient(clusterName)
	if err != nil {
		return fmt.Errorf("failed to get client for cluster %s: %w", clusterName, err)
	}

	err = client.CoreV1().Services(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete service %s in cluster %s namespace %s: %w", name, clusterName, namespace, err)
	}

	klog.Infof("Deleted service %s in cluster %s namespace %s", name, clusterName, namespace)
	return nil
}

// 跨集群操作

// ListPodsFromAllClusters 从所有健康集群列出Pod
func (o *Operations) ListPodsFromAllClusters(ctx context.Context, namespace string) (map[string]*corev1.PodList, error) {
	healthyClusters := o.checker.GetHealthyClusters()
	if len(healthyClusters) == 0 {
		return nil, fmt.Errorf("no healthy clusters available")
	}

	result := make(map[string]*corev1.PodList)
	var mu sync.Mutex
	var wg sync.WaitGroup
	var errors []error

	for _, clusterName := range healthyClusters {
		wg.Add(1)
		go func(cluster string) {
			defer wg.Done()

			pods, err := o.ListPods(ctx, cluster, namespace)

			mu.Lock()
			defer mu.Unlock()

			if err != nil {
				errors = append(errors, fmt.Errorf("cluster %s: %w", cluster, err))
			} else {
				result[cluster] = pods
			}
		}(clusterName)
	}

	wg.Wait()

	if len(errors) > 0 && len(result) == 0 {
		return nil, fmt.Errorf("failed to list pods from all clusters: %v", errors)
	}

	return result, nil
}

// DeployToMultipleClusters 部署到多个集群
func (o *Operations) DeployToMultipleClusters(ctx context.Context, clusterNames []string, deployment *appsv1.Deployment) (map[string]error, error) {
	if len(clusterNames) == 0 {
		return nil, fmt.Errorf("no clusters specified")
	}

	result := make(map[string]error)
	var mu sync.Mutex
	var wg sync.WaitGroup

	for _, clusterName := range clusterNames {
		// 检查集群是否健康
		if !o.checker.IsClusterHealthy(clusterName) {
			result[clusterName] = fmt.Errorf("cluster %s is not healthy", clusterName)
			continue
		}

		wg.Add(1)
		go func(cluster string) {
			defer wg.Done()

			// 克隆deployment以避免并发修改
			deploymentCopy := deployment.DeepCopy()

			_, err := o.CreateDeployment(ctx, cluster, deploymentCopy)

			mu.Lock()
			result[cluster] = err
			mu.Unlock()
		}(clusterName)
	}

	wg.Wait()

	// 检查是否有成功的部署
	successCount := 0
	for _, err := range result {
		if err == nil {
			successCount++
		}
	}

	if successCount == 0 {
		return result, fmt.Errorf("failed to deploy to all clusters")
	}

	return result, nil
}

// GetClustersBySelector 根据选择器获取集群列表
func (o *Operations) GetClustersBySelector(selector *ClusterSelector) ([]string, error) {
	allClusters := o.pool.ListClusters()
	var selectedClusters []string

	for _, clusterName := range allClusters {
		wrapper, err := o.pool.GetClusterInfo(clusterName)
		if err != nil {
			continue
		}

		config := wrapper.config

		// 检查名称匹配
		if len(selector.Names) > 0 {
			found := false
			for _, name := range selector.Names {
				if name == clusterName {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		// 检查环境匹配
		if selector.Environment != "" && config.Environment != selector.Environment {
			continue
		}

		// 检查区域匹配
		if selector.Region != "" && config.Region != selector.Region {
			continue
		}

		// 检查标签匹配
		if len(selector.Labels) > 0 {
			labelSelector := labels.Set(selector.Labels).AsSelector()
			if !labelSelector.Matches(labels.Set(config.Labels)) {
				continue
			}
		}

		// 检查状态匹配
		if selector.Status != "" {
			health, err := o.checker.GetClusterHealth(clusterName)
			if err != nil || health.Status != selector.Status {
				continue
			}
		}

		selectedClusters = append(selectedClusters, clusterName)
	}

	return selectedClusters, nil
}

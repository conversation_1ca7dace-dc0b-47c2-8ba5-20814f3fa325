package k8smgr

import (
	"context"
	"fmt"
	"time"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// AuthType 认证类型
type AuthType string

const (
	// AuthTypeKubeconfig 使用kubeconfig文件认证
	AuthTypeKubeconfig AuthType = "kubeconfig"
	// AuthTypeToken 使用ServiceAccount Token认证
	AuthTypeToken AuthType = "token"
	// AuthTypeCertificate 使用客户端证书认证
	AuthTypeCertificate AuthType = "certificate"
	// AuthTypeInCluster 集群内认证
	AuthTypeInCluster AuthType = "incluster"
)

// ClusterStatus 集群状态
type ClusterStatus string

const (
	// ClusterStatusHealthy 集群健康
	ClusterStatusHealthy ClusterStatus = "healthy"
	// ClusterStatusUnhealthy 集群不健康
	ClusterStatusUnhealthy ClusterStatus = "unhealthy"
	// ClusterStatusConnecting 连接中
	ClusterStatusConnecting ClusterStatus = "connecting"
	// ClusterStatusDisconnected 已断开
	ClusterStatusDisconnected ClusterStatus = "disconnected"
)

// ClusterConfig 集群配置
type ClusterConfig struct {
	// 基本信息
	Name        string `json:"name" yaml:"name"`               // 集群名称
	Scope       string `json:"scope" yaml:"scope"`             // 集群作用域(用于区分同名集群，如: dev, prod, us-west, team-a等)
	Description string `json:"description" yaml:"description"` // 集群描述
	Environment string `json:"environment" yaml:"environment"` // 环境标识 (dev/test/prod)
	Region      string `json:"region" yaml:"region"`           // 区域

	// 连接配置
	APIServer string   `json:"api_server" yaml:"api_server"` // API Server地址
	AuthType  AuthType `json:"auth_type" yaml:"auth_type"`   // 认证类型

	// 认证信息
	Auth AuthConfig `json:"auth" yaml:"auth"`

	// 连接参数
	Timeout       time.Duration `json:"timeout" yaml:"timeout"`               // 连接超时时间
	QPS           float32       `json:"qps" yaml:"qps"`                       // 每秒查询数限制
	Burst         int           `json:"burst" yaml:"burst"`                   // 突发请求数限制
	MaxRetries    int           `json:"max_retries" yaml:"max_retries"`       // 最大重试次数
	RetryInterval time.Duration `json:"retry_interval" yaml:"retry_interval"` // 重试间隔

	// 健康检查
	HealthCheck HealthCheckConfig `json:"health_check" yaml:"health_check"`

	// 标签和注解
	Labels      map[string]string `json:"labels" yaml:"labels"`           // 集群标签
	Annotations map[string]string `json:"annotations" yaml:"annotations"` // 集群注解

	// 优先级和权重
	Priority int `json:"priority" yaml:"priority"` // 集群优先级，数值越大优先级越高
	Weight   int `json:"weight" yaml:"weight"`     // 负载均衡权重

	// 是否启用
	Enabled bool `json:"enabled" yaml:"enabled"`
}

// GetUniqueID 获取集群的唯一标识符
// 格式: scope:name 或 name (如果scope为空)
func (c *ClusterConfig) GetUniqueID() string {
	if c.Scope != "" {
		return fmt.Sprintf("%s:%s", c.Scope, c.Name)
	}
	return c.Name
}

// GetDisplayName 获取集群的显示名称
func (c *ClusterConfig) GetDisplayName() string {
	if c.Scope != "" {
		return fmt.Sprintf("%s/%s", c.Scope, c.Name)
	}
	return c.Name
}

// AuthConfig 认证配置
type AuthConfig struct {
	// Kubeconfig认证
	KubeconfigPath string `json:"kubeconfig_path" yaml:"kubeconfig_path"` // kubeconfig文件路径
	KubeconfigData string `json:"kubeconfig_data" yaml:"kubeconfig_data"` // kubeconfig内容(base64编码)
	Context        string `json:"context" yaml:"context"`                 // kubeconfig上下文

	// Token认证
	Token     string `json:"token" yaml:"token"`         // ServiceAccount Token
	Namespace string `json:"namespace" yaml:"namespace"` // Token所在命名空间

	// 证书认证
	CertData string `json:"cert_data" yaml:"cert_data"` // 客户端证书(base64编码)
	KeyData  string `json:"key_data" yaml:"key_data"`   // 客户端私钥(base64编码)
	CAData   string `json:"ca_data" yaml:"ca_data"`     // CA证书(base64编码)

	// 证书文件路径(可选，优先使用Data字段)
	CertFile string `json:"cert_file" yaml:"cert_file"` // 客户端证书文件路径
	KeyFile  string `json:"key_file" yaml:"key_file"`   // 客户端私钥文件路径
	CAFile   string `json:"ca_file" yaml:"ca_file"`     // CA证书文件路径

	// TLS配置
	InsecureSkipTLSVerify bool   `json:"insecure_skip_tls_verify" yaml:"insecure_skip_tls_verify"` // 跳过TLS验证
	ServerName            string `json:"server_name" yaml:"server_name"`                           // TLS服务器名称
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled          bool          `json:"enabled" yaml:"enabled"`                     // 是否启用健康检查
	Interval         time.Duration `json:"interval" yaml:"interval"`                   // 检查间隔
	Timeout          time.Duration `json:"timeout" yaml:"timeout"`                     // 检查超时时间
	FailureThreshold int           `json:"failure_threshold" yaml:"failure_threshold"` // 失败阈值
	SuccessThreshold int           `json:"success_threshold" yaml:"success_threshold"` // 成功阈值
	InitialDelay     time.Duration `json:"initial_delay" yaml:"initial_delay"`         // 初始延迟
}

// ClusterInfo 集群运行时信息
type ClusterInfo struct {
	Config *ClusterConfig `json:"config"`
	Status ClusterStatus  `json:"status"`

	// 连接信息
	Client     kubernetes.Interface `json:"-"`
	RestConfig *rest.Config         `json:"-"`

	// 状态信息
	LastHealthCheck time.Time `json:"last_health_check"`
	LastError       error     `json:"-"`
	LastErrorMsg    string    `json:"last_error_msg"`
	ConnectedAt     time.Time `json:"connected_at"`

	// 统计信息
	HealthCheckCount    int64 `json:"health_check_count"`
	HealthCheckFailures int64 `json:"health_check_failures"`
	RequestCount        int64 `json:"request_count"`
	RequestFailures     int64 `json:"request_failures"`

	// 版本信息
	ServerVersion string `json:"server_version"`

	// 资源信息
	NodeCount int32 `json:"node_count"`
	PodCount  int32 `json:"pod_count"`
}

// ClusterManager 集群管理器接口
type ClusterManager interface {
	// 集群管理
	AddCluster(config *ClusterConfig) error
	RemoveCluster(name string) error
	GetCluster(name string) (*ClusterInfo, error)
	ListClusters() []*ClusterInfo
	UpdateCluster(name string, config *ClusterConfig) error

	// 集群操作
	GetClient(clusterName string) (kubernetes.Interface, error)
	GetRestConfig(clusterName string) (*rest.Config, error)

	// 健康检查
	CheckHealth(clusterName string) error
	CheckAllHealth() map[string]error

	// 状态查询
	GetClusterStatus(clusterName string) ClusterStatus
	IsClusterHealthy(clusterName string) bool

	// 生命周期
	Start(ctx context.Context) error
	Stop() error
}

// ClusterOperations k8s资源操作接口
type ClusterOperations interface {
	// Pod操作
	ListPods(ctx context.Context, clusterName, namespace string) error
	GetPod(ctx context.Context, clusterName, namespace, name string) error
	CreatePod(ctx context.Context, clusterName string, pod interface{}) error
	DeletePod(ctx context.Context, clusterName, namespace, name string) error

	// Deployment操作
	ListDeployments(ctx context.Context, clusterName, namespace string) error
	GetDeployment(ctx context.Context, clusterName, namespace, name string) error
	CreateDeployment(ctx context.Context, clusterName string, deployment interface{}) error
	UpdateDeployment(ctx context.Context, clusterName string, deployment interface{}) error
	DeleteDeployment(ctx context.Context, clusterName, namespace, name string) error

	// Service操作
	ListServices(ctx context.Context, clusterName, namespace string) error
	GetService(ctx context.Context, clusterName, namespace, name string) error
	CreateService(ctx context.Context, clusterName string, service interface{}) error
	DeleteService(ctx context.Context, clusterName, namespace, name string) error

	// 跨集群操作
	ListPodsFromAllClusters(ctx context.Context, namespace string) error
	DeployToMultipleClusters(ctx context.Context, clusterNames []string, resource interface{}) error
}

// ClusterSelector 集群选择器
type ClusterSelector struct {
	Names       []string          `json:"names"`       // 指定集群名称
	Labels      map[string]string `json:"labels"`      // 标签选择器
	Environment string            `json:"environment"` // 环境选择器
	Region      string            `json:"region"`      // 区域选择器
	Status      ClusterStatus     `json:"status"`      // 状态选择器
}

// LoadBalanceStrategy 负载均衡策略
type LoadBalanceStrategy string

const (
	// LoadBalanceRoundRobin 轮询
	LoadBalanceRoundRobin LoadBalanceStrategy = "round_robin"
	// LoadBalanceWeighted 加权轮询
	LoadBalanceWeighted LoadBalanceStrategy = "weighted"
	// LoadBalanceRandom 随机
	LoadBalanceRandom LoadBalanceStrategy = "random"
	// LoadBalanceLeastConnections 最少连接
	LoadBalanceLeastConnections LoadBalanceStrategy = "least_connections"
)

// MultiClusterConfig 多集群配置
type MultiClusterConfig struct {
	Clusters            []*ClusterConfig    `json:"clusters" yaml:"clusters"`
	DefaultCluster      string              `json:"default_cluster" yaml:"default_cluster"`
	LoadBalanceStrategy LoadBalanceStrategy `json:"load_balance_strategy" yaml:"load_balance_strategy"`

	// 全局配置
	GlobalTimeout     time.Duration `json:"global_timeout" yaml:"global_timeout"`
	GlobalRetries     int           `json:"global_retries" yaml:"global_retries"`
	HealthCheckPeriod time.Duration `json:"health_check_period" yaml:"health_check_period"`

	// 连接池配置
	MaxIdleConns    int           `json:"max_idle_conns" yaml:"max_idle_conns"`
	MaxActiveConns  int           `json:"max_active_conns" yaml:"max_active_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime" yaml:"conn_max_lifetime"`
}

# Kubernetes 多集群管理模块

这个模块提供了一个完整的Kubernetes多集群管理解决方案，支持多种认证方式、连接池管理、健康检查、负载均衡等功能。

## 功能特性

- **多集群支持**: 同时管理多个Kubernetes集群
- **多种认证方式**: 支持kubeconfig、ServiceAccount Token、客户端证书、集群内认证
- **连接池管理**: 客户端连接复用和生命周期管理
- **健康检查**: 实时监控集群状态，自动故障检测和恢复
- **负载均衡**: 支持轮询、加权、随机、最少连接等策略
- **跨集群操作**: 统一的API接口，支持跨集群资源操作
- **故障容错**: 自动重连、故障转移、错误重试

## 快速开始

### 1. 基本使用

```go
package main

import (
    "context"
    "log"
    "time"
    
    "your-project/pkg/k8s"
)

func main() {
    // 创建集群配置
    config := &k8s.MultiClusterConfig{
        Clusters: []*k8s.ClusterConfig{
            {
                Name:        "dev-cluster",
                Description: "Development cluster",
                Environment: "dev",
                AuthType:    k8s.AuthTypeKubeconfig,
                Auth: k8s.AuthConfig{
                    KubeconfigPath: "/path/to/dev-kubeconfig",
                },
                Enabled: true,
            },
            {
                Name:        "prod-cluster", 
                Description: "Production cluster",
                Environment: "prod",
                APIServer:   "https://prod-k8s-api.example.com",
                AuthType:    k8s.AuthTypeToken,
                Auth: k8s.AuthConfig{
                    Token: "your-service-account-token",
                    CAData: "base64-encoded-ca-cert",
                },
                Enabled: true,
            },
        },
        DefaultCluster:      "prod-cluster",
        LoadBalanceStrategy: k8s.LoadBalanceWeighted,
    }

    // 创建管理器
    manager, err := k8s.NewManager(config)
    if err != nil {
        log.Fatal(err)
    }

    // 启动管理器
    ctx := context.Background()
    if err := manager.Start(ctx); err != nil {
        log.Fatal(err)
    }
    defer manager.Stop()

    // 获取客户端
    client, err := manager.GetClient("dev-cluster")
    if err != nil {
        log.Fatal(err)
    }

    // 使用客户端进行操作
    namespaces, err := client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
    if err != nil {
        log.Fatal(err)
    }
    
    log.Printf("Found %d namespaces", len(namespaces.Items))
}
```

### 2. 认证配置

#### Kubeconfig文件认证
```go
config := &k8s.ClusterConfig{
    Name:     "my-cluster",
    AuthType: k8s.AuthTypeKubeconfig,
    Auth: k8s.AuthConfig{
        KubeconfigPath: "/path/to/kubeconfig",
        Context:        "my-context", // 可选
    },
}
```

#### ServiceAccount Token认证
```go
config := &k8s.ClusterConfig{
    Name:      "my-cluster",
    APIServer: "https://k8s-api.example.com",
    AuthType:  k8s.AuthTypeToken,
    Auth: k8s.AuthConfig{
        Token:  "eyJhbGciOiJSUzI1NiIs...",
        CAData: "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...",
    },
}
```

#### 客户端证书认证
```go
config := &k8s.ClusterConfig{
    Name:      "my-cluster",
    APIServer: "https://k8s-api.example.com",
    AuthType:  k8s.AuthTypeCertificate,
    Auth: k8s.AuthConfig{
        CertData: "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...",
        KeyData:  "LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...",
        CAData:   "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...",
    },
}
```

#### 集群内认证（Pod内运行）
```go
config := &k8s.ClusterConfig{
    Name:     "in-cluster",
    AuthType: k8s.AuthTypeInCluster,
}
```

### 3. 跨集群操作

```go
ops := manager.GetOperations()

// 从所有健康集群列出Pod
allPods, err := ops.ListPodsFromAllClusters(ctx, "default")
if err != nil {
    log.Fatal(err)
}

for clusterName, pods := range allPods {
    log.Printf("Cluster %s has %d pods", clusterName, len(pods.Items))
}

// 部署到多个集群
deployment := &appsv1.Deployment{...}
clusterNames := []string{"dev-cluster", "prod-cluster"}
results, err := ops.DeployToMultipleClusters(ctx, clusterNames, deployment)

for clusterName, err := range results {
    if err != nil {
        log.Printf("Deploy to %s failed: %v", clusterName, err)
    } else {
        log.Printf("Deploy to %s succeeded", clusterName)
    }
}
```

### 4. 健康检查

```go
// 检查所有集群健康状态
healthResults := manager.CheckAllHealth()
for clusterName, err := range healthResults {
    if err != nil {
        log.Printf("Cluster %s is unhealthy: %v", clusterName, err)
    } else {
        log.Printf("Cluster %s is healthy", clusterName)
    }
}

// 获取详细健康信息
clusters := manager.ListClusters()
for _, cluster := range clusters {
    log.Printf("Cluster: %s, Status: %s, Nodes: %d", 
        cluster.Config.Name, cluster.Status, cluster.NodeCount)
}
```

### 5. 集群选择器

```go
ops := manager.GetOperations()

// 按环境选择集群
selector := &k8s.ClusterSelector{
    Environment: "prod",
    Status:      k8s.ClusterStatusHealthy,
}

clusters, err := ops.GetClustersBySelector(selector)
if err != nil {
    log.Fatal(err)
}

log.Printf("Found %d production clusters", len(clusters))
```

## 配置选项

### 集群配置 (ClusterConfig)

| 字段 | 类型 | 描述 |
|------|------|------|
| Name | string | 集群名称（必填） |
| Description | string | 集群描述 |
| Environment | string | 环境标识（dev/test/prod） |
| Region | string | 区域 |
| APIServer | string | API Server地址 |
| AuthType | AuthType | 认证类型 |
| Auth | AuthConfig | 认证配置 |
| Timeout | time.Duration | 连接超时时间 |
| QPS | float32 | 每秒查询数限制 |
| Burst | int | 突发请求数限制 |
| HealthCheck | HealthCheckConfig | 健康检查配置 |
| Labels | map[string]string | 集群标签 |
| Priority | int | 集群优先级 |
| Weight | int | 负载均衡权重 |
| Enabled | bool | 是否启用 |

### 多集群配置 (MultiClusterConfig)

| 字段 | 类型 | 描述 |
|------|------|------|
| Clusters | []*ClusterConfig | 集群配置列表 |
| DefaultCluster | string | 默认集群名称 |
| LoadBalanceStrategy | LoadBalanceStrategy | 负载均衡策略 |
| GlobalTimeout | time.Duration | 全局超时时间 |
| HealthCheckPeriod | time.Duration | 健康检查周期 |
| MaxIdleConns | int | 最大空闲连接数 |
| MaxActiveConns | int | 最大活跃连接数 |
| ConnMaxLifetime | time.Duration | 连接最大生命周期 |

## 负载均衡策略

- **LoadBalanceRoundRobin**: 轮询
- **LoadBalanceWeighted**: 加权轮询
- **LoadBalanceRandom**: 随机
- **LoadBalanceLeastConnections**: 最少连接

## 最佳实践

1. **配置管理**: 将集群配置存储在配置文件中，支持热更新
2. **安全认证**: 使用ServiceAccount Token或客户端证书，避免使用kubeconfig文件
3. **健康检查**: 启用健康检查，设置合适的检查间隔和失败阈值
4. **连接池**: 根据负载情况调整连接池大小
5. **错误处理**: 实现重试机制和故障转移逻辑
6. **监控告警**: 监控集群状态和连接池指标

## 故障排查

### 常见问题

1. **连接失败**: 检查网络连通性、认证配置、API Server地址
2. **认证失败**: 验证Token有效性、证书配置、权限设置
3. **健康检查失败**: 检查集群状态、网络延迟、超时配置
4. **负载不均衡**: 检查集群权重配置、健康状态

### 日志级别

使用klog设置日志级别：
```go
import "k8s.io/klog/v2"

// 设置日志级别
klog.InitFlags(nil)
flag.Set("v", "4") // 0-4，数字越大日志越详细
```

## 扩展开发

### 自定义认证方式

实现自定义认证逻辑：
```go
func (c *ClusterConfig) buildCustomRestConfig() (*rest.Config, error) {
    // 实现自定义认证逻辑
    return config, nil
}
```

### 自定义健康检查

扩展健康检查逻辑：
```go
func (h *HealthChecker) customHealthCheck(clusterName string) error {
    // 实现自定义健康检查逻辑
    return nil
}
```

## 测试

运行测试：
```bash
go test ./pkg/k8s -v
```

运行基准测试：
```bash
go test ./pkg/k8s -bench=. -benchmem
```

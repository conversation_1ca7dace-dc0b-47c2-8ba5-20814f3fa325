package k8smgr

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestKruiseGameOperations(t *testing.T) {
	// 跳过集成测试，除非设置了环境变量
	if testing.Short() {
		t.<PERSON>p("Skipping integration test in short mode")
	}

	// 创建测试配置
	config := &MultiClusterConfig{
		Clusters: []*ClusterConfig{
			{
				Name:      "test-cluster",
				Scope:     "test",
				APIServer: "https://127.0.0.1:6443", // 假设的测试集群
				AuthType:  AuthTypeKubeconfig,
				Auth: AuthConfig{
					KubeconfigPath: "",
					Context:        "kind-test-cluster",
				},
				Timeout: 30 * time.Second,
			},
		},
	}

	// 创建管理器
	manager, err := NewManager(config)
	require.NoError(t, err)

	// 启动管理器
	ctx := context.Background()
	err = manager.Start(ctx)
	require.NoError(t, err)
	defer manager.Stop()

	// 获取 OpenKruise Game 操作接口
	kruiseGame := manager.KruiseGame()
	require.NotNil(t, kruiseGame)

	t.Run("TestGameServerSetOperations", func(t *testing.T) {
		testGameServerSetOperations(t, kruiseGame)
	})

	t.Run("TestGameServerOperations", func(t *testing.T) {
		testGameServerOperations(t, kruiseGame)
	})

	t.Run("TestBatchOperations", func(t *testing.T) {
		testBatchOperations(t, kruiseGame)
	})

	t.Run("TestStatsOperations", func(t *testing.T) {
		testStatsOperations(t, kruiseGame)
	})
}

func testGameServerSetOperations(t *testing.T, kruiseGame KruiseGameOperations) {
	ctx := context.Background()
	clusterName := "test:test-cluster"
	namespace := "default"
	gssName := "test-gameserverset"

	// 测试创建 GameServerSet
	createReq := &GameServerSetCreateRequest{
		Name:      gssName,
		Namespace: namespace,
		Replicas:  2,
		Image:     "nginx:latest",
		Ports: []GameServerPort{
			{Name: "game-port", Port: 7777, Protocol: "TCP"},
		},
		Env: map[string]string{
			"TEST_ENV": "test-value",
		},
		Labels: map[string]string{
			"test": "true",
		},
	}

	gssInfo, err := kruiseGame.CreateGameServerSet(ctx, clusterName, createReq)
	if err != nil {
		t.Logf("Failed to create GameServerSet (expected in unit test): %v", err)
		return // 在单元测试中，这是预期的，因为没有真实的集群
	}

	assert.Equal(t, gssName, gssInfo.Name)
	assert.Equal(t, namespace, gssInfo.Namespace)
	assert.Equal(t, int32(2), gssInfo.Replicas)

	// 测试获取 GameServerSet
	gssInfo, err = kruiseGame.GetGameServerSet(ctx, clusterName, namespace, gssName)
	if err != nil {
		t.Logf("Failed to get GameServerSet (expected in unit test): %v", err)
		return
	}

	assert.Equal(t, gssName, gssInfo.Name)

	// 测试列出 GameServerSet
	gssList, err := kruiseGame.ListGameServerSets(ctx, clusterName, &GameServerSetFilter{
		Namespace: namespace,
	})
	if err != nil {
		t.Logf("Failed to list GameServerSets (expected in unit test): %v", err)
		return
	}

	assert.GreaterOrEqual(t, len(gssList), 1)

	// 测试伸缩 GameServerSet
	err = kruiseGame.ScaleGameServerSet(ctx, clusterName, namespace, gssName, 3)
	if err != nil {
		t.Logf("Failed to scale GameServerSet (expected in unit test): %v", err)
	}

	// 测试更新 GameServerSet
	updateReq := &GameServerSetUpdateRequest{
		Name:      gssName,
		Namespace: namespace,
		Image:     "nginx:1.21",
		Labels: map[string]string{
			"updated": "true",
		},
	}

	_, err = kruiseGame.UpdateGameServerSet(ctx, clusterName, updateReq)
	if err != nil {
		t.Logf("Failed to update GameServerSet (expected in unit test): %v", err)
	}

	// 测试删除 GameServerSet
	err = kruiseGame.DeleteGameServerSet(ctx, clusterName, namespace, gssName)
	if err != nil {
		t.Logf("Failed to delete GameServerSet (expected in unit test): %v", err)
	}
}

func testGameServerOperations(t *testing.T, kruiseGame KruiseGameOperations) {
	ctx := context.Background()
	clusterName := "test:test-cluster"
	namespace := "default"

	// 测试列出 GameServer
	gameServers, err := kruiseGame.ListGameServers(ctx, clusterName, &GameServerFilter{
		Namespace: namespace,
	})
	if err != nil {
		t.Logf("Failed to list GameServers (expected in unit test): %v", err)
		return
	}

	t.Logf("Found %d GameServers", len(gameServers))

	if len(gameServers) == 0 {
		t.Log("No GameServers found, skipping GameServer operations tests")
		return
	}

	// 使用第一个 GameServer 进行测试
	gs := gameServers[0]

	// 测试获取 GameServer
	gsInfo, err := kruiseGame.GetGameServer(ctx, clusterName, gs.Namespace, gs.Name)
	if err != nil {
		t.Logf("Failed to get GameServer (expected in unit test): %v", err)
		return
	}

	assert.Equal(t, gs.Name, gsInfo.Name)

	// 测试更新 GameServer
	updatePriority := 1
	updateReq := &GameServerUpdateRequest{
		Name:           gs.Name,
		Namespace:      gs.Namespace,
		UpdatePriority: &updatePriority,
		Labels: map[string]string{
			"test-updated": "true",
		},
	}

	_, err = kruiseGame.UpdateGameServer(ctx, clusterName, updateReq)
	if err != nil {
		t.Logf("Failed to update GameServer (expected in unit test): %v", err)
	}
}

func testBatchOperations(t *testing.T, kruiseGame KruiseGameOperations) {
	ctx := context.Background()
	clusterName := "test:test-cluster"

	// 测试批量更新
	updatePriority := 2
	updatedNames, err := kruiseGame.BatchUpdateGameServers(ctx, clusterName,
		&GameServerFilter{
			Namespace: "default",
			State:     GameServerStateReady,
		},
		&GameServerUpdateRequest{
			UpdatePriority: &updatePriority,
			Labels: map[string]string{
				"batch-updated": "true",
			},
		})

	if err != nil {
		t.Logf("Failed to batch update GameServers (expected in unit test): %v", err)
		return
	}

	t.Logf("Batch updated %d GameServers: %v", len(updatedNames), updatedNames)
}

func testStatsOperations(t *testing.T, kruiseGame KruiseGameOperations) {
	ctx := context.Background()
	clusterName := "test:test-cluster"

	// 测试获取统计信息
	stats, err := kruiseGame.GetGameServerStats(ctx, clusterName, "default")
	if err != nil {
		t.Logf("Failed to get GameServer stats (expected in unit test): %v", err)
		return
	}

	assert.GreaterOrEqual(t, stats.Total, 0)
	t.Logf("GameServer stats - Total: %d, Ready: %d, NotReady: %d",
		stats.Total, stats.Ready, stats.NotReady)

	// 测试获取所有集群统计
	allStats, err := kruiseGame.GetAllClustersGameServerStats(ctx)
	if err != nil {
		t.Logf("Failed to get all clusters stats (expected in unit test): %v", err)
		return
	}

	assert.GreaterOrEqual(t, len(allStats), 0)

	// 测试跨集群操作
	allServers, err := kruiseGame.ListGameServersFromAllClusters(ctx, &GameServerFilter{
		State: GameServerStateReady,
	})
	if err != nil {
		t.Logf("Failed to list GameServers from all clusters (expected in unit test): %v", err)
		return
	}

	t.Logf("Found GameServers in %d clusters", len(allServers))

	allStatsMap, err := kruiseGame.GetGameServerStatsFromAllClusters(ctx)
	if err != nil {
		t.Logf("Failed to get stats from all clusters (expected in unit test): %v", err)
		return
	}

	assert.GreaterOrEqual(t, len(allStatsMap), 0)
}

// TestKruiseGameTypes 测试类型定义
func TestKruiseGameTypes(t *testing.T) {
	// 测试 GameServerState
	assert.Equal(t, "Ready", string(GameServerStateReady))
	assert.Equal(t, "NotReady", string(GameServerStateNotReady))
	assert.Equal(t, "Crash", string(GameServerStateCrash))

	// 测试 GameServerOpsState
	assert.Equal(t, "None", string(GameServerOpsStateNone))
	assert.Equal(t, "Kill", string(GameServerOpsStateKill))

	// 测试结构体创建
	gsInfo := &GameServerInfo{
		Name:        "test-server",
		Namespace:   "default",
		ClusterName: "test-cluster",
		State:       GameServerStateReady,
		OpsState:    GameServerOpsStateNone,
		Ports: []GameServerPort{
			{Name: "game", Port: 7777, Protocol: "TCP"},
		},
	}

	assert.Equal(t, "test-server", gsInfo.Name)
	assert.Equal(t, GameServerStateReady, gsInfo.State)
	assert.Equal(t, 1, len(gsInfo.Ports))
	assert.Equal(t, int32(7777), gsInfo.Ports[0].Port)

	// 测试统计信息
	stats := &GameServerStats{
		Total:    10,
		Ready:    8,
		NotReady: 1,
		Crash:    1,
	}

	assert.Equal(t, 10, stats.Total)
	assert.Equal(t, 8, stats.Ready)
}

// BenchmarkKruiseGameOperations 性能测试
func BenchmarkKruiseGameOperations(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	// 这里可以添加性能测试，但需要真实的集群环境
	b.Log("Benchmark requires real cluster environment")
}

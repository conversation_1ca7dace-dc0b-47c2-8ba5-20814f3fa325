package k8smgr

import (
	"context"
	"fmt"
	"log"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ExampleUsage 展示如何使用k8s多集群管理模块
func ExampleUsage() {
	// 1. 创建多集群配置
	config := &MultiClusterConfig{
		Clusters: []*ClusterConfig{
			{
				Name:        "dev-cluster",
				Description: "Development cluster",
				Environment: "dev",
				Region:      "us-west-1",
				APIServer:   "https://dev-k8s-api.example.com",
				AuthType:    AuthTypeKubeconfig,
				Auth: AuthConfig{
					KubeconfigPath: "/home/<USER>/.kube/dev-config",
				},
				Timeout:       30 * time.Second,
				QPS:           100,
				Burst:         100,
				MaxRetries:    3,
				RetryInterval: 5 * time.Second,
				HealthCheck: HealthCheckConfig{
					Enabled:          true,
					Interval:         30 * time.Second,
					Timeout:          10 * time.Second,
					FailureThreshold: 3,
					SuccessThreshold: 1,
					InitialDelay:     10 * time.Second,
				},
				Labels: map[string]string{
					"env":    "dev",
					"region": "us-west-1",
				},
				Priority: 1,
				Weight:   1,
				Enabled:  true,
			},
			{
				Name:        "prod-cluster",
				Description: "Production cluster",
				Environment: "prod",
				Region:      "us-east-1",
				APIServer:   "https://prod-k8s-api.example.com",
				AuthType:    AuthTypeToken,
				Auth: AuthConfig{
					Token:  "**********************************************...",
					CAData: "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...",
				},
				Timeout:       30 * time.Second,
				QPS:           200,
				Burst:         200,
				MaxRetries:    3,
				RetryInterval: 5 * time.Second,
				HealthCheck: HealthCheckConfig{
					Enabled:          true,
					Interval:         30 * time.Second,
					Timeout:          10 * time.Second,
					FailureThreshold: 3,
					SuccessThreshold: 1,
					InitialDelay:     10 * time.Second,
				},
				Labels: map[string]string{
					"env":    "prod",
					"region": "us-east-1",
				},
				Priority: 2,
				Weight:   3,
				Enabled:  true,
			},
		},
		DefaultCluster:      "prod-cluster",
		LoadBalanceStrategy: LoadBalanceWeighted,
		GlobalTimeout:       60 * time.Second,
		GlobalRetries:       3,
		HealthCheckPeriod:   30 * time.Second,
		MaxIdleConns:        10,
		MaxActiveConns:      100,
		ConnMaxLifetime:     1 * time.Hour,
	}

	// 2. 创建集群管理器
	manager, err := NewManager(config)
	if err != nil {
		log.Fatalf("Failed to create manager: %v", err)
	}

	// 3. 启动管理器
	ctx := context.Background()
	if err := manager.Start(ctx); err != nil {
		log.Fatalf("Failed to start manager: %v", err)
	}
	defer manager.Stop()

	// 4. 基本操作示例
	basicOperationsExample(manager)

	// 5. 跨集群操作示例
	crossClusterOperationsExample(manager)

	// 6. 健康检查示例
	healthCheckExample(manager)

	// 7. 负载均衡示例
	loadBalanceExample(manager)
}

// basicOperationsExample 基本操作示例
func basicOperationsExample(manager *Manager) {
	fmt.Println("=== Basic Operations Example ===")

	ctx := context.Background()
	ops := manager.GetOperations()

	// 列出所有集群
	clusters := manager.ListClusters()
	fmt.Printf("Available clusters: %d\n", len(clusters))
	for _, cluster := range clusters {
		fmt.Printf("- %s (%s): %s\n", cluster.Config.Name, cluster.Config.Environment, cluster.Status)
	}

	// 获取指定集群的客户端
	client, err := manager.GetClient("dev-cluster")
	if err != nil {
		log.Printf("Failed to get client for dev-cluster: %v", err)
		return
	}

	// 使用客户端进行操作
	namespaces, err := client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Printf("Failed to list namespaces: %v", err)
		return
	}

	fmt.Printf("Namespaces in dev-cluster: %d\n", len(namespaces.Items))

	// 使用操作接口列出Pod
	pods, err := ops.ListPods(ctx, "dev-cluster", "default")
	if err != nil {
		log.Printf("Failed to list pods: %v", err)
		return
	}

	fmt.Printf("Pods in dev-cluster/default: %d\n", len(pods.Items))
}

// crossClusterOperationsExample 跨集群操作示例
func crossClusterOperationsExample(manager *Manager) {
	fmt.Println("=== Cross-Cluster Operations Example ===")

	ctx := context.Background()
	ops := manager.GetOperations()

	// 从所有集群列出Pod
	allPods, err := ops.ListPodsFromAllClusters(ctx, "default")
	if err != nil {
		log.Printf("Failed to list pods from all clusters: %v", err)
		return
	}

	fmt.Printf("Pods from all clusters:\n")
	for clusterName, pods := range allPods {
		fmt.Printf("- %s: %d pods\n", clusterName, len(pods.Items))
	}

	// 创建示例Deployment
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "example-app",
			Namespace: "default",
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: int32Ptr(2),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "example-app",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "example-app",
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  "app",
							Image: "nginx:1.20",
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 80,
								},
							},
						},
					},
				},
			},
		},
	}

	// 部署到多个集群
	clusterNames := []string{"dev-cluster", "prod-cluster"}
	results, err := ops.DeployToMultipleClusters(ctx, clusterNames, deployment)
	if err != nil {
		log.Printf("Some deployments failed: %v", err)
	}

	fmt.Printf("Deployment results:\n")
	for clusterName, err := range results {
		if err != nil {
			fmt.Printf("- %s: FAILED (%v)\n", clusterName, err)
		} else {
			fmt.Printf("- %s: SUCCESS\n", clusterName)
		}
	}
}

// healthCheckExample 健康检查示例
func healthCheckExample(manager *Manager) {
	fmt.Println("=== Health Check Example ===")

	// 检查所有集群健康状态
	healthResults := manager.CheckAllHealth()
	fmt.Printf("Health check results:\n")
	for clusterName, err := range healthResults {
		if err != nil {
			fmt.Printf("- %s: UNHEALTHY (%v)\n", clusterName, err)
		} else {
			fmt.Printf("- %s: HEALTHY\n", clusterName)
		}
	}

	// 获取详细健康状态
	clusters := manager.ListClusters()
	for _, cluster := range clusters {
		fmt.Printf("Cluster %s details:\n", cluster.Config.Name)
		fmt.Printf("  Status: %s\n", cluster.Status)
		fmt.Printf("  Server Version: %s\n", cluster.ServerVersion)
		fmt.Printf("  Node Count: %d\n", cluster.NodeCount)
		fmt.Printf("  Health Checks: %d (failures: %d)\n",
			cluster.HealthCheckCount, cluster.HealthCheckFailures)
		fmt.Printf("  Requests: %d (failures: %d)\n",
			cluster.RequestCount, cluster.RequestFailures)
		fmt.Printf("  Connected At: %s\n", cluster.ConnectedAt.Format(time.RFC3339))
		fmt.Printf("  Last Health Check: %s\n", cluster.LastHealthCheck.Format(time.RFC3339))
	}
}

// loadBalanceExample 负载均衡示例
func loadBalanceExample(manager *Manager) {
	fmt.Println("=== Load Balance Example ===")

	// 多次获取默认客户端，观察负载均衡效果
	clientCounts := make(map[string]int)

	for i := 0; i < 10; i++ {
		_, err := manager.GetDefaultClient()
		if err != nil {
			log.Printf("Failed to get default client: %v", err)
			continue
		}

		// 通过客户端配置判断是哪个集群
		// 这里简化处理，实际使用中可以通过其他方式识别
		fmt.Printf("Request %d: Got client (simulated cluster selection)\n", i+1)
	}

	fmt.Printf("Client selection distribution:\n")
	for cluster, count := range clientCounts {
		fmt.Printf("- %s: %d requests\n", cluster, count)
	}
}

// ExampleWithConfiguration 配置示例
func ExampleWithConfiguration() {
	// 1. 使用kubeconfig文件认证
	kubeconfigCluster := &ClusterConfig{
		Name:        "kubeconfig-cluster",
		Description: "Cluster using kubeconfig file",
		Environment: "dev",
		AuthType:    AuthTypeKubeconfig,
		Auth: AuthConfig{
			KubeconfigPath: "/path/to/kubeconfig",
			Context:        "my-context", // 可选，指定context
		},
	}

	// 2. 使用ServiceAccount Token认证
	tokenCluster := &ClusterConfig{
		Name:        "token-cluster",
		Description: "Cluster using service account token",
		Environment: "prod",
		APIServer:   "https://k8s-api.example.com",
		AuthType:    AuthTypeToken,
		Auth: AuthConfig{
			Token:  "eyJhbGciOiJSUzI1NiIs...",
			CAData: "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...", // base64编码的CA证书
		},
	}

	// 3. 使用客户端证书认证
	certCluster := &ClusterConfig{
		Name:        "cert-cluster",
		Description: "Cluster using client certificate",
		Environment: "prod",
		APIServer:   "https://k8s-api.example.com",
		AuthType:    AuthTypeCertificate,
		Auth: AuthConfig{
			CertData: "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...", // base64编码的客户端证书
			KeyData:  "LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...", // base64编码的私钥
			CAData:   "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...", // base64编码的CA证书
		},
	}

	// 4. 集群内认证（Pod内运行时）
	inClusterConfig := &ClusterConfig{
		Name:        "in-cluster",
		Description: "In-cluster authentication",
		Environment: "prod",
		AuthType:    AuthTypeInCluster,
	}

	fmt.Printf("Configuration examples created:\n")
	fmt.Printf("- Kubeconfig: %s\n", kubeconfigCluster.Name)
	fmt.Printf("- Token: %s\n", tokenCluster.Name)
	fmt.Printf("- Certificate: %s\n", certCluster.Name)
	fmt.Printf("- In-cluster: %s\n", inClusterConfig.Name)
}

// int32Ptr 辅助函数，返回int32指针
func int32Ptr(i int32) *int32 {
	return &i
}

// ExampleClusterSelector 集群选择器示例
func ExampleClusterSelector(manager *Manager) {
	ops := manager.GetOperations()

	// 1. 按名称选择
	selector1 := &ClusterSelector{
		Names: []string{"dev-cluster", "test-cluster"},
	}

	// 2. 按环境选择
	selector2 := &ClusterSelector{
		Environment: "prod",
	}

	// 3. 按标签选择
	selector3 := &ClusterSelector{
		Labels: map[string]string{
			"region": "us-west-1",
			"env":    "dev",
		},
	}

	// 4. 按状态选择
	selector4 := &ClusterSelector{
		Status: ClusterStatusHealthy,
	}

	// 5. 组合选择
	selector5 := &ClusterSelector{
		Environment: "prod",
		Region:      "us-east-1",
		Status:      ClusterStatusHealthy,
		Labels: map[string]string{
			"tier": "production",
		},
	}

	selectors := []*ClusterSelector{selector1, selector2, selector3, selector4, selector5}
	for i, selector := range selectors {
		clusters, err := ops.GetClustersBySelector(selector)
		if err != nil {
			log.Printf("Selector %d failed: %v", i+1, err)
			continue
		}
		fmt.Printf("Selector %d matched clusters: %v\n", i+1, clusters)
	}
}

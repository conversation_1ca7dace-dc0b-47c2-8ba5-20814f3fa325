```mermaid
sequenceDiagram
    participant User as 用户应用
    participant Service as K8sService
    participant Manager as Manager
    participant Pool as ClientPool
    participant Health as HealthChecker
    participant Ops as Operations
    participant LB as 负载均衡器
    participant Cluster as K8s集群

    Note over User,Cluster: 1. 系统初始化流程
    User->>Service: 创建K8sService
    Service->>Manager: 创建Manager
    Manager->>Pool: 创建ClientPool
    Manager->>Health: 创建HealthChecker
    Manager->>Ops: 创建Operations
    Manager->>Manager: 启动健康检查协程
    Manager->>Pool: 初始化集群连接
    Pool->>Cluster: 建立连接并测试

    Note over User,Cluster: 2. 健康检查流程
    loop 定期健康检查
        Health->>Cluster: 检查集群状态
        Cluster-->>Health: 返回健康状态
        Health->>Health: 更新健康状态
        alt 集群不健康
            Health->>Pool: 标记连接为不可用
            Health->>Cluster: 尝试重连
        end
    end

    Note over User,Cluster: 3. 请求处理流程
    User->>Service: 发起K8s操作请求
    Service->>Manager: 调用管理器方法
    
    alt 指定集群名称
        Manager->>Pool: 获取指定集群客户端
    else 使用负载均衡
        Manager->>Health: 获取健康集群列表
        Health-->>Manager: 返回健康集群
        Manager->>LB: 根据策略选择集群
        LB-->>Manager: 返回选中的集群
        Manager->>Pool: 获取选中集群客户端
    end
    
    Pool->>Pool: 检查连接池
    alt 连接存在且有效
        Pool-->>Manager: 返回现有客户端
    else 需要新建连接
        Pool->>Cluster: 创建新连接
        Cluster-->>Pool: 返回客户端
        Pool->>Pool: 缓存客户端
        Pool-->>Manager: 返回新客户端
    end
    
    Manager->>Ops: 调用操作接口
    Ops->>Cluster: 执行K8s操作
    Cluster-->>Ops: 返回操作结果
    Ops-->>Manager: 返回结果
    Manager-->>Service: 返回结果
    Service-->>User: 返回最终结果

    Note over User,Cluster: 4. 跨集群操作流程
    User->>Service: 发起跨集群操作
    Service->>Ops: 调用跨集群方法
    Ops->>Health: 获取目标集群列表
    Health-->>Ops: 返回集群列表
    
    loop 遍历目标集群
        Ops->>Pool: 获取集群客户端
        Pool-->>Ops: 返回客户端
        Ops->>Cluster: 执行操作
        Cluster-->>Ops: 返回结果
        Ops->>Ops: 收集结果
    end
    
    Ops-->>Service: 返回汇总结果
    Service-->>User: 返回最终结果

    Note over User,Cluster: 5. 故障处理流程
    Ops->>Cluster: 执行操作
    Cluster-->>Ops: 返回错误
    Ops->>Ops: 检查是否需要重试
    
    alt 需要重试
        Ops->>Pool: 获取新连接
        Pool->>Cluster: 重新连接
        Ops->>Cluster: 重试操作
    else 故障转移
        Ops->>Health: 获取备用集群
        Health-->>Ops: 返回备用集群
        Ops->>Pool: 获取备用集群客户端
        Ops->>Cluster: 在备用集群执行
    end
```
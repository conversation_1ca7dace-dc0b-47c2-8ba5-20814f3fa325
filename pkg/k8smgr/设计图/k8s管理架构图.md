```mermaid
graph TB
    %% 用户层
    subgraph "用户层 (User Layer)"
        APP[应用程序]
        GAME[游戏服务器管理]
        API[REST API]
    end

    %% 服务层
    subgraph "服务层 (Service Layer)"
        K8S_SERVICE[K8sService<br/>集成服务]
        MANAGER[Manager<br/>集群管理器]
    end

    %% 核心组件层
    subgraph "核心组件层 (Core Components)"
        subgraph "连接管理"
            POOL[ClientPool<br/>连接池管理器]
            WRAPPER[ClientWrapper<br/>客户端包装器]
        end
        
        subgraph "健康监控"
            HEALTH[HealthChecker<br/>健康检查器]
            STATUS[HealthStatus<br/>健康状态]
        end
        
        subgraph "操作接口"
            OPS[Operations<br/>统一操作接口]
            SELECTOR[ClusterSelector<br/>集群选择器]
        end
        
        subgraph "配置管理"
            CONFIG[ClusterConfig<br/>集群配置]
            MULTI_CONFIG[MultiClusterConfig<br/>多集群配置]
        end
    end

    %% 负载均衡策略
    subgraph "负载均衡策略 (Load Balance Strategies)"
        LB_RR[轮询<br/>Round Robin]
        LB_WEIGHTED[加权<br/>Weighted]
        LB_RANDOM[随机<br/>Random]
        LB_LEAST[最少连接<br/>Least Connections]
    end

    %% 认证方式
    subgraph "认证方式 (Authentication Types)"
        AUTH_KUBECONFIG[Kubeconfig文件]
        AUTH_TOKEN[ServiceAccount Token]
        AUTH_CERT[客户端证书]
        AUTH_INCLUSTER[集群内认证]
    end

    %% K8s集群
    subgraph "Kubernetes集群 (K8s Clusters)"
        CLUSTER1[开发集群<br/>Dev Cluster]
        CLUSTER2[测试集群<br/>Test Cluster]
        CLUSTER3[生产集群<br/>Prod Cluster]
        CLUSTER4[灾备集群<br/>DR Cluster]
    end

    %% K8s资源
    subgraph "K8s资源 (K8s Resources)"
        PODS[Pods]
        DEPLOYMENTS[Deployments]
        SERVICES[Services]
        NAMESPACES[Namespaces]
    end

    %% 连接关系
    APP --> K8S_SERVICE
    GAME --> K8S_SERVICE
    API --> K8S_SERVICE
    
    K8S_SERVICE --> MANAGER
    
    MANAGER --> POOL
    MANAGER --> HEALTH
    MANAGER --> OPS
    MANAGER --> CONFIG
    
    POOL --> WRAPPER
    WRAPPER --> CLUSTER1
    WRAPPER --> CLUSTER2
    WRAPPER --> CLUSTER3
    WRAPPER --> CLUSTER4
    
    HEALTH --> STATUS
    HEALTH --> CLUSTER1
    HEALTH --> CLUSTER2
    HEALTH --> CLUSTER3
    HEALTH --> CLUSTER4
    
    OPS --> SELECTOR
    OPS --> POOL
    OPS --> HEALTH
    
    CONFIG --> MULTI_CONFIG
    CONFIG --> AUTH_KUBECONFIG
    CONFIG --> AUTH_TOKEN
    CONFIG --> AUTH_CERT
    CONFIG --> AUTH_INCLUSTER
    
    MANAGER --> LB_RR
    MANAGER --> LB_WEIGHTED
    MANAGER --> LB_RANDOM
    MANAGER --> LB_LEAST
    
    CLUSTER1 --> PODS
    CLUSTER1 --> DEPLOYMENTS
    CLUSTER1 --> SERVICES
    CLUSTER1 --> NAMESPACES
    
    CLUSTER2 --> PODS
    CLUSTER2 --> DEPLOYMENTS
    CLUSTER2 --> SERVICES
    CLUSTER2 --> NAMESPACES
    
    CLUSTER3 --> PODS
    CLUSTER3 --> DEPLOYMENTS
    CLUSTER3 --> SERVICES
    CLUSTER3 --> NAMESPACES
    
    CLUSTER4 --> PODS
    CLUSTER4 --> DEPLOYMENTS
    CLUSTER4 --> SERVICES
    CLUSTER4 --> NAMESPACES

    %% 样式定义
    classDef userLayer fill:#000000,stroke:#01579b,stroke-width:2px
    classDef serviceLayer fill:#000000,stroke:#4a148c,stroke-width:2px
    classDef coreComponent fill:#000000,stroke:#1b5e20,stroke-width:2px
    classDef strategy fill:#000000,stroke:#e65100,stroke-width:2px
    classDef auth fill:#000000,stroke:#880e4f,stroke-width:2px
    classDef resource fill:#000000,stroke:#33691e,stroke-width:2px

    class APP,GAME,API userLayer
    class K8S_SERVICE,MANAGER serviceLayer
    class POOL,WRAPPER,HEALTH,STATUS,OPS,SELECTOR,CONFIG,MULTI_CONFIG coreComponent
    class LB_RR,LB_WEIGHTED,LB_RANDOM,LB_LEAST strategy
    class AUTH_KUBECONFIG,AUTH_TOKEN,AUTH_CERT,AUTH_INCLUSTER auth
    class CLUSTER1,CLUSTER2,CLUSTER3,CLUSTER4 cluster
    class PODS,DEPLOYMENTS,SERVICES,NAMESPACES resource
```
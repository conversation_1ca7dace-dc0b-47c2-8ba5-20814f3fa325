package k8smgr

import (
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// DefaultClusterConfig 返回默认集群配置
func DefaultClusterConfig() *ClusterConfig {
	return &ClusterConfig{
		Name:          "default",
		Description:   "Default Kubernetes cluster",
		Environment:   "dev",
		AuthType:      AuthTypeKubeconfig,
		Timeout:       30 * time.Second,
		QPS:           100,
		Burst:         100,
		MaxRetries:    3,
		RetryInterval: 5 * time.Second,
		HealthCheck: HealthCheckConfig{
			Enabled:          true,
			Interval:         30 * time.Second,
			Timeout:          10 * time.Second,
			FailureThreshold: 3,
			SuccessThreshold: 1,
			InitialDelay:     10 * time.Second,
		},
		Labels:      make(map[string]string),
		Annotations: make(map[string]string),
		Priority:    1,
		Weight:      1,
		Enabled:     true,
	}
}

// DefaultMultiClusterConfig 返回默认多集群配置
func DefaultMultiClusterConfig() *MultiClusterConfig {
	return &MultiClusterConfig{
		Clusters:            []*ClusterConfig{},
		LoadBalanceStrategy: LoadBalanceRoundRobin,
		GlobalTimeout:       60 * time.Second,
		GlobalRetries:       3,
		HealthCheckPeriod:   30 * time.Second,
		MaxIdleConns:        10,
		MaxActiveConns:      100,
		ConnMaxLifetime:     1 * time.Hour,
	}
}

// Validate 验证集群配置
func (c *ClusterConfig) Validate() error {
	if c.Name == "" {
		return fmt.Errorf("cluster name cannot be empty")
	}

	// 验证scope格式（如果提供）
	if c.Scope != "" {
		if len(c.Scope) > 63 {
			return fmt.Errorf("cluster scope cannot exceed 63 characters")
		}
		// 简单的DNS标签验证
		for _, char := range c.Scope {
			if !((char >= 'a' && char <= 'z') || (char >= '0' && char <= '9') || char == '-') {
				return fmt.Errorf("cluster scope must contain only lowercase letters, numbers, and hyphens")
			}
		}
	}

	if c.APIServer == "" && c.AuthType != AuthTypeInCluster {
		return fmt.Errorf("api_server is required for auth type %s", c.AuthType)
	}

	switch c.AuthType {
	case AuthTypeKubeconfig:
		if c.Auth.KubeconfigPath == "" && c.Auth.KubeconfigData == "" {
			return fmt.Errorf("kubeconfig_path or kubeconfig_data is required for kubeconfig auth")
		}
	case AuthTypeToken:
		if c.Auth.Token == "" {
			return fmt.Errorf("token is required for token auth")
		}
	case AuthTypeCertificate:
		if (c.Auth.CertData == "" && c.Auth.CertFile == "") ||
			(c.Auth.KeyData == "" && c.Auth.KeyFile == "") {
			return fmt.Errorf("cert and key are required for certificate auth")
		}
	case AuthTypeInCluster:
		// 集群内认证不需要额外验证
	default:
		return fmt.Errorf("unsupported auth type: %s", c.AuthType)
	}

	if c.Timeout <= 0 {
		c.Timeout = 30 * time.Second
	}

	if c.QPS <= 0 {
		c.QPS = 100
	}

	if c.Burst <= 0 {
		c.Burst = 100
	}

	return nil
}

// BuildRestConfig 根据配置构建rest.Config
func (c *ClusterConfig) BuildRestConfig() (*rest.Config, error) {
	var config *rest.Config
	var err error

	switch c.AuthType {
	case AuthTypeInCluster:
		config, err = rest.InClusterConfig()
		if err != nil {
			return nil, fmt.Errorf("failed to get in-cluster config: %w", err)
		}

	case AuthTypeKubeconfig:
		config, err = c.buildKubeconfigRestConfig()
		if err != nil {
			return nil, fmt.Errorf("failed to build kubeconfig rest config: %w", err)
		}

	case AuthTypeToken:
		config, err = c.buildTokenRestConfig()
		if err != nil {
			return nil, fmt.Errorf("failed to build token rest config: %w", err)
		}

	case AuthTypeCertificate:
		config, err = c.buildCertificateRestConfig()
		if err != nil {
			return nil, fmt.Errorf("failed to build certificate rest config: %w", err)
		}

	default:
		return nil, fmt.Errorf("unsupported auth type: %s", c.AuthType)
	}

	// 应用通用配置
	if c.APIServer != "" {
		config.Host = c.APIServer
	}

	config.QPS = c.QPS
	config.Burst = c.Burst
	config.Timeout = c.Timeout

	// TLS配置
	if c.Auth.InsecureSkipTLSVerify {
		config.Insecure = true
		config.TLSClientConfig.Insecure = true
	}

	if c.Auth.ServerName != "" {
		config.TLSClientConfig.ServerName = c.Auth.ServerName
	}

	return config, nil
}

// buildKubeconfigRestConfig 构建kubeconfig认证的rest.Config
func (c *ClusterConfig) buildKubeconfigRestConfig() (*rest.Config, error) {
	var kubeconfigData []byte
	var err error

	if c.Auth.KubeconfigData != "" {
		// 使用base64编码的kubeconfig数据
		kubeconfigData, err = base64.StdEncoding.DecodeString(c.Auth.KubeconfigData)
		if err != nil {
			return nil, fmt.Errorf("failed to decode kubeconfig data: %w", err)
		}
	} else if c.Auth.KubeconfigPath != "" {
		// 使用kubeconfig文件路径
		kubeconfigPath := c.Auth.KubeconfigPath
		if kubeconfigPath == "" {
			// 使用默认路径
			homeDir, _ := os.UserHomeDir()
			kubeconfigPath = filepath.Join(homeDir, ".kube", "config")
		}

		kubeconfigData, err = os.ReadFile(kubeconfigPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read kubeconfig file %s: %w", kubeconfigPath, err)
		}
	} else {
		return nil, fmt.Errorf("kubeconfig_path or kubeconfig_data is required")
	}

	// 解析kubeconfig
	config, err := clientcmd.RESTConfigFromKubeConfig(kubeconfigData)
	if err != nil {
		// 如果指定了context，尝试使用指定的context
		if c.Auth.Context != "" {
			clientConfig, err := clientcmd.NewClientConfigFromBytes(kubeconfigData)
			if err != nil {
				return nil, fmt.Errorf("failed to parse kubeconfig: %w", err)
			}

			rawConfig, err := clientConfig.RawConfig()
			if err != nil {
				return nil, fmt.Errorf("failed to get raw config: %w", err)
			}

			// 设置当前context
			rawConfig.CurrentContext = c.Auth.Context

			clientConfig = clientcmd.NewDefaultClientConfig(rawConfig, &clientcmd.ConfigOverrides{})
			config, err = clientConfig.ClientConfig()
			if err != nil {
				return nil, fmt.Errorf("failed to build client config with context %s: %w", c.Auth.Context, err)
			}
		} else {
			return nil, fmt.Errorf("failed to build rest config from kubeconfig: %w", err)
		}
	}

	return config, nil
}

// buildTokenRestConfig 构建token认证的rest.Config
func (c *ClusterConfig) buildTokenRestConfig() (*rest.Config, error) {
	config := &rest.Config{
		Host:        c.APIServer,
		BearerToken: c.Auth.Token,
	}

	// 设置CA证书
	if c.Auth.CAData != "" {
		caData, err := base64.StdEncoding.DecodeString(c.Auth.CAData)
		if err != nil {
			return nil, fmt.Errorf("failed to decode ca data: %w", err)
		}
		config.TLSClientConfig.CAData = caData
	} else if c.Auth.CAFile != "" {
		config.TLSClientConfig.CAFile = c.Auth.CAFile
	}

	return config, nil
}

// buildCertificateRestConfig 构建证书认证的rest.Config
func (c *ClusterConfig) buildCertificateRestConfig() (*rest.Config, error) {
	config := &rest.Config{
		Host: c.APIServer,
	}

	// 设置客户端证书
	if c.Auth.CertData != "" && c.Auth.KeyData != "" {
		certData, err := base64.StdEncoding.DecodeString(c.Auth.CertData)
		if err != nil {
			return nil, fmt.Errorf("failed to decode cert data: %w", err)
		}

		keyData, err := base64.StdEncoding.DecodeString(c.Auth.KeyData)
		if err != nil {
			return nil, fmt.Errorf("failed to decode key data: %w", err)
		}

		config.TLSClientConfig.CertData = certData
		config.TLSClientConfig.KeyData = keyData
	} else if c.Auth.CertFile != "" && c.Auth.KeyFile != "" {
		config.TLSClientConfig.CertFile = c.Auth.CertFile
		config.TLSClientConfig.KeyFile = c.Auth.KeyFile
	} else {
		return nil, fmt.Errorf("cert and key are required for certificate auth")
	}

	// 设置CA证书
	if c.Auth.CAData != "" {
		caData, err := base64.StdEncoding.DecodeString(c.Auth.CAData)
		if err != nil {
			return nil, fmt.Errorf("failed to decode ca data: %w", err)
		}
		config.TLSClientConfig.CAData = caData
	} else if c.Auth.CAFile != "" {
		config.TLSClientConfig.CAFile = c.Auth.CAFile
	}

	return config, nil
}

// Clone 克隆集群配置
func (c *ClusterConfig) Clone() *ClusterConfig {
	clone := *c

	// 深拷贝map
	if c.Labels != nil {
		clone.Labels = make(map[string]string)
		for k, v := range c.Labels {
			clone.Labels[k] = v
		}
	}

	if c.Annotations != nil {
		clone.Annotations = make(map[string]string)
		for k, v := range c.Annotations {
			clone.Annotations[k] = v
		}
	}

	return &clone
}

// Merge 合并配置，other的配置会覆盖当前配置
func (c *ClusterConfig) Merge(other *ClusterConfig) {
	if other.Name != "" {
		c.Name = other.Name
	}
	if other.Description != "" {
		c.Description = other.Description
	}
	if other.Environment != "" {
		c.Environment = other.Environment
	}
	if other.Region != "" {
		c.Region = other.Region
	}
	if other.APIServer != "" {
		c.APIServer = other.APIServer
	}
	if other.AuthType != "" {
		c.AuthType = other.AuthType
	}

	// 合并认证配置
	if other.Auth.KubeconfigPath != "" {
		c.Auth.KubeconfigPath = other.Auth.KubeconfigPath
	}
	if other.Auth.KubeconfigData != "" {
		c.Auth.KubeconfigData = other.Auth.KubeconfigData
	}
	if other.Auth.Context != "" {
		c.Auth.Context = other.Auth.Context
	}
	if other.Auth.Token != "" {
		c.Auth.Token = other.Auth.Token
	}
	if other.Auth.Namespace != "" {
		c.Auth.Namespace = other.Auth.Namespace
	}

	// 合并标签和注解
	if other.Labels != nil {
		if c.Labels == nil {
			c.Labels = make(map[string]string)
		}
		for k, v := range other.Labels {
			c.Labels[k] = v
		}
	}

	if other.Annotations != nil {
		if c.Annotations == nil {
			c.Annotations = make(map[string]string)
		}
		for k, v := range other.Annotations {
			c.Annotations[k] = v
		}
	}

	// 合并其他配置
	if other.Timeout > 0 {
		c.Timeout = other.Timeout
	}
	if other.QPS > 0 {
		c.QPS = other.QPS
	}
	if other.Burst > 0 {
		c.Burst = other.Burst
	}
	if other.Priority != 0 {
		c.Priority = other.Priority
	}
	if other.Weight != 0 {
		c.Weight = other.Weight
	}
}

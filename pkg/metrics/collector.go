package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/collectors"
)

// InitMetrics 初始化并注册所有指标
func InitMetrics() {
	// 创建一个新的注册表
	registry := prometheus.NewRegistry()

	// 只注册我们关心的指标
	registry.MustRegister(
		HTTPRequestsTotal,
		HTTPRequestDuration,
		DatabaseOperationsTotal,
		DatabaseOperationDuration,
		ActiveConnections,
		// 添加一些基本的系统指标
		collectors.NewGoCollector(),
		collectors.NewProcessCollector(collectors.ProcessCollectorOpts{}),
	)

	// 设置为默认注册表
	prometheus.DefaultRegisterer = registry
	prometheus.DefaultGatherer = registry
}

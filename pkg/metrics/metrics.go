package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

const (
	// 指标命名空间
	namespace = "func"

	// 标签名称
	labelMethod    = "method"
	labelPath      = "path"
	labelStatus    = "status"
	labelOperation = "operation"
	labelTable     = "table"
)

var (
	// HTTPRequestsTotal 记录 HTTP 请求总数
	HTTPRequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: namespace,
			Name:      "http_requests_total",
			Help:      "Total number of HTTP requests",
		},
		[]string{labelMethod, labelPath, labelStatus},
	)

	// HTTPRequestDuration 记录 HTTP 请求处理时间
	HTTPRequestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: namespace,
			Name:      "http_request_duration_seconds",
			Help:      "HTTP request duration in seconds",
			Buckets:   []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10},
		},
		[]string{labelMethod, labelPath},
	)

	// DatabaseOperationsTotal 记录数据库操作总数
	DatabaseOperationsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: namespace,
			Name:      "database_operations_total",
			Help:      "Total number of database operations",
		},
		[]string{labelOperation, labelTable},
	)

	// DatabaseOperationDuration 记录数据库操作处理时间
	DatabaseOperationDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: namespace,
			Name:      "database_operation_duration_seconds",
			Help:      "Database operation duration in seconds",
			Buckets:   []float64{.001, .005, .01, .025, .05, .1, .25, .5, 1},
		},
		[]string{labelOperation, labelTable},
	)

	// ActiveConnections 记录当前活跃连接数
	ActiveConnections = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: namespace,
			Name:      "active_connections",
			Help:      "Number of currently active connections",
		},
	)

	// SystemMemoryUsage 记录系统内存使用情况
	SystemMemoryUsage = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: namespace,
			Name:      "system_memory_bytes",
			Help:      "System memory usage in bytes",
		},
		[]string{"type"},
	)

	// SystemGoroutines 记录当前 goroutine 数量
	SystemGoroutines = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: namespace,
			Name:      "system_goroutines",
			Help:      "Number of currently running goroutines",
		},
	)
)

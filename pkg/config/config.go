package config

import (
	"fmt"
	"log"
	"os"

	"github.com/spf13/viper"
)

// Config 应用配置结构体
type Config struct {
	Server           ServerConfig           `mapstructure:"server"`
	Database         DatabaseConfig         `mapstructure:"database"`
	JWT              JWTConfig              `mapstructure:"jwt"`
	Redis            RedisConfig            `mapstructure:"redis"`
	Kafka            KafkaConfig            `mapstructure:"kafka"`
	Elasticsearch    ElasticsearchConfig    `mapstructure:"elasticsearch"`
	Jaeger           JaegerConfig           `mapstructure:"jaeger"`
	S3               S3Config               `mapstructure:"s3"`
	Canal            CanalConfig            `mapstructure:"canal"`
	SwaggerBasicAuth SwaggerBasicAuthConfig `mapstructure:"swagger_basic_auth"`
	Faker            FakerConfig            `mapstructure:"faker"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Address string `mapstructure:"address"`
	Env     string `mapstructure:"env"`
}

func (c *ServerConfig) IsProd() bool {
	return c.Env == "prod"
}

func (c *ServerConfig) IsTest() bool {
	return c.Env == "test"
}

func (c *ServerConfig) IsDev() bool {
	return c.Env == "dev"
}

func (c *ServerConfig) IsTestMode() bool {
	return os.Getenv("TEST_MODE") == "true"
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Name     string `mapstructure:"name"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	Expiration int    `mapstructure:"expiration"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host           string `mapstructure:"host"`
	Port           int    `mapstructure:"port"`
	Password       string `mapstructure:"password"`
	DB             int    `mapstructure:"db"`
	EnableApiCache bool   `mapstructure:"enable_api_cache"` // 是否启用接口缓存
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Brokers          []string          `mapstructure:"brokers"`
	Topics           map[string]string `mapstructure:"topics"` // 业务key->topic名
	ConsumerGroup    string            `mapstructure:"consumer_group"`
	Version          string            `mapstructure:"version"`
	SecurityProtocol string            `mapstructure:"security_protocol"`
	SASLUsername     string            `mapstructure:"sasl_username"`
	SASLPassword     string            `mapstructure:"sasl_password"`
}

// ElasticsearchConfig Elasticsearch配置
type ElasticsearchConfig struct {
	Hosts              []string `mapstructure:"hosts"`
	Username           string   `mapstructure:"username"`
	Password           string   `mapstructure:"password"`
	Sniff              bool     `mapstructure:"sniff"`
	IndexPrefix        string   `mapstructure:"index_prefix"`
	CACertPath         string   `mapstructure:"ca_cert_path"`         // 新增: CA证书路径
	InsecureSkipVerify bool     `mapstructure:"insecure_skip_verify"` // 新增: 跳过证书校验
}

// JaegerConfig Jaeger配置
type JaegerConfig struct {
	ServiceName    string `mapstructure:"service_name"`
	OTLPEndpoint   string `mapstructure:"otlp_endpoint"`
	Environment    string `mapstructure:"environment"`
	ServiceVersion string `mapstructure:"service_version"`
}

// S3Config S3配置
type S3Config struct {
	Endpoint        string `mapstructure:"endpoint"`          // S3服务端点
	AccessKeyId     string `mapstructure:"access_key_id"`     // 访问密钥ID
	AccessKeySecret string `mapstructure:"access_key_secret"` // 访问密钥密码
	Bucket          string `mapstructure:"bucket"`            // 存储桶名称
	Region          string `mapstructure:"region"`            // 区域
	UseSSL          bool   `mapstructure:"use_ssl"`           // 是否使用SSL
	ForcePathStyle  bool   `mapstructure:"force_path_style"`  // 是否强制使用路径样式
}

// CanalConfig Canal配置
type CanalConfig struct {
	Address     string `mapstructure:"address"`
	Port        int    `mapstructure:"port"`
	Username    string `mapstructure:"username"`
	Password    string `mapstructure:"password"`
	Destination string `mapstructure:"destination"`
	SoTimeout   int32  `mapstructure:"so_timeout"`
	IdleTimeout int32  `mapstructure:"idle_timeout"`
	Enable      bool   `mapstructure:"enable"`
}

// SwaggerBasicAuthConfig 用于 swagger basic auth 配置
type SwaggerBasicAuthConfig struct {
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Enable   bool   `mapstructure:"enable"`
}

// FakerConfig Faker配置
type FakerConfig struct {
	Enable    bool   `mapstructure:"enable"`
	TenantID  int64  `mapstructure:"tenant_id"`
	UserID    int64  `mapstructure:"user_id"`
	ProjectID int64  `mapstructure:"project_id"`
	UserUUID  string `mapstructure:"user_uuid"`
	UserName  string `mapstructure:"user_name"`
	UserEmail string `mapstructure:"user_email"`
	UserPhone string `mapstructure:"user_phone"`
}

var (
	// GlobalConfig 全局配置实例
	GlobalConfig Config
)

// InitConfig 初始化配置，可选传入配置文件路径
func InitConfig(configPath string) error {
	// 设置配置文件路径
	if configPath != "" {
		viper.AddConfigPath(configPath)
	} else {
		viper.AddConfigPath("configs")
	}
	// viper.AddConfigPath(".")

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("未找到配置文件，使用默认配置")
		} else {
			return fmt.Errorf("读取配置文件失败: %v", err)
		}
	}

	// 将配置解析到结构体
	if err := viper.Unmarshal(&GlobalConfig); err != nil {
		return fmt.Errorf("解析配置失败: %v", err)
	}

	// 打印加载的配置
	fmt.Printf("已加载配置文件: %s\n", viper.ConfigFileUsed())
	return nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 服务器配置
	viper.SetDefault("server.address", ":8080")

	// 数据库配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.username", "root")
	viper.SetDefault("database.password", "")
	viper.SetDefault("database.name", "multiverse")

	// JWT配置
	viper.SetDefault("jwt.secret", "your-jwt-secret-key")
	viper.SetDefault("jwt.expiration", 24)

	// Redis配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.enable_api_cache", false)

	// Kafka配置
	viper.SetDefault("kafka.brokers", []string{"localhost:9092"})
	viper.SetDefault("kafka.topics", map[string]string{})
	viper.SetDefault("kafka.version", "2.8.0")
	viper.SetDefault("kafka.security_protocol", "PLAINTEXT")

	// Elasticsearch配置
	viper.SetDefault("elasticsearch.hosts", []string{"http://localhost:9200"})
	viper.SetDefault("elasticsearch.username", "")
	viper.SetDefault("elasticsearch.password", "")
	viper.SetDefault("elasticsearch.sniff", false)
	viper.SetDefault("elasticsearch.index_prefix", "audit_logs")
	viper.SetDefault("elasticsearch.ca_cert_path", "")
	viper.SetDefault("elasticsearch.insecure_skip_verify", false)

	// Jaeger配置
	viper.SetDefault("jaeger.service_name", "multiverse-service")
	viper.SetDefault("jaeger.otlp_endpoint", "localhost:4318")
	viper.SetDefault("jaeger.environment", "development")
	viper.SetDefault("jaeger.service_version", "1.0.0")

	// S3配置
	viper.SetDefault("s3.endpoint", "")
	viper.SetDefault("s3.access_key_id", "")
	viper.SetDefault("s3.access_key_secret", "")
	viper.SetDefault("s3.bucket", "")
	viper.SetDefault("s3.region", "us-east-1")
	viper.SetDefault("s3.use_ssl", true)
	viper.SetDefault("s3.force_path_style", false)

	// Canal配置
	viper.SetDefault("canal.address", "")
	viper.SetDefault("canal.port", 0)
	viper.SetDefault("canal.username", "")
	viper.SetDefault("canal.password", "")
	viper.SetDefault("canal.destination", "")
	viper.SetDefault("canal.so_timeout", 0)
	viper.SetDefault("canal.idle_timeout", 0)
	viper.SetDefault("canal.enable", false)

	// Swagger Basic Auth
	viper.SetDefault("swagger_basic_auth.username", "admin")
	viper.SetDefault("swagger_basic_auth.password", "admin123")
	viper.SetDefault("swagger_basic_auth.enable", true)
}

package config

import (
	"log"

	"github.com/spf13/viper"
)

type GameFrontendConfig struct {
	OpenMatchFrontendAddr string `mapstructure:"openmatch_frontend_addr"`
	OpenMatchFrontendPort int    `mapstructure:"openmatch_frontend_port"`
	Port                  int    `mapstructure:"port"`
	SwaggerEnable         bool   `mapstructure:"swagger_enable"`
}

var GameFrontendCfg GameFrontendConfig

func LoadGameFrontendConfig(configPath string) {
	viper.SetConfigFile(configPath)
	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("读取配置文件失败: %v", err)
	}

	// 设置默认值
	viper.SetDefault("openmatch_frontend_addr", "open-match-frontend.open-match.svc.cluster.local")
	viper.SetDefault("openmatch_frontend_port", 50504)
	viper.SetDefault("port", 8081)
	viper.SetDefault("swagger_enable", false)

	if err := viper.UnmarshalKey("gamefrontend", &GameFrontendCfg); err != nil {
		log.Fatalf("解析配置失败: %v", err)
	}
}

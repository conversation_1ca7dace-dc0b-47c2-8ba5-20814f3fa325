package serverallocation

import (
	"context"
	"fmt"
	"log"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/k8smgr"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// KruiseGameIntegrationExample 展示如何在项目中集成和使用 OpenKruise Game
func KruiseGameIntegrationExample(cfg *config.Config) {
	fmt.Println("=== OpenKruise Game 集成示例 ===")

	// 2. 创建 K8s 服务
	k8sService, err := NewK8sService(cfg)
	if err != nil {
		log.Fatalf("Failed to create K8s service: %v", err)
	}

	// 3. 启动服务
	ctx := context.Background()
	if err := k8sService.Start(ctx); err != nil {
		log.Fatalf("Failed to start K8s service: %v", err)
	}
	defer k8sService.Stop()

	// 等待服务启动
	time.Sleep(2 * time.Second)

	// 4. 检查集群健康状态
	healthyClusters := k8sService.manager.GetHealthyClusters()
	fmt.Printf("Healthy clusters: %v\n", healthyClusters)

	if len(healthyClusters) == 0 {
		fmt.Println("No healthy clusters available. Please check your cluster configuration.")
		return
	}

	// 5. OpenKruise Game 操作示例
	demonstrateKruiseGameOperations(ctx, k8sService)

	fmt.Println("\n=== OpenKruise Game 集成示例完成 ===")

}

// demonstrateKruiseGameOperations 演示 OpenKruise Game 操作
func demonstrateKruiseGameOperations(ctx context.Context, k8sService *K8sService) {
	fmt.Println("\n=== OpenKruise Game 操作示例 ===")

	kruiseGame := k8sService.GetKruiseGameOperations()
	healthyClusters := k8sService.manager.GetHealthyClusters()

	if len(healthyClusters) == 0 {
		fmt.Println("No healthy clusters available for GameServerSet creation")
		return
	}

	clusterName := healthyClusters[0]
	//MOCK
	clusterName = "local:kind-1m3w"

	// 1. 创建游戏服务器集合
	fmt.Println("\n1. 创建游戏服务器集合")
	gssReq := &k8smgr.GameServerSetCreateRequest{
		Name:      "demo-game-servers",
		Namespace: "default",
		Replicas:  2,
		Image:     "nginx:latest", // 在实际环境中应该是游戏服务器镜像
		Ports: []k8smgr.GameServerPort{
			{Name: "game-port", Port: 7777, Protocol: "TCP"},
			{Name: "query-port", Port: 7778, Protocol: "UDP"},
		},
		Env: map[string]string{
			"GAME_MODE":     "PVP",
			"MAX_PLAYERS":   "10",
			"SERVER_REGION": "us-west",
		},
		Labels: map[string]string{
			"game":        "demo-game",
			"environment": "development",
			"region":      "us-west",
			"allocated":   "false",
		},
		// NetworkType: "Kubernetes-HostPort", // 注释掉，使用默认网络配置
		Resources: k8smgr.ResourceRequirements{
			Requests: k8smgr.ResourceList{
				CPU:    "500m",
				Memory: "1Gi",
			},
			Limits: k8smgr.ResourceList{
				CPU:    "1000m",
				Memory: "2Gi",
			},
		},
	}

	gssInfo, err := kruiseGame.CreateGameServerSet(ctx, clusterName, gssReq)
	if err != nil {
		fmt.Printf("Failed to create GameServerSet: %v\n", err)
		// 在开发环境中这是预期的，因为可能没有安装 OpenKruise Game
		fmt.Println("Note: This is expected if OpenKruise Game is not installed in the cluster")
		fmt.Println("\n要解决此问题，请在集群中安装 OpenKruise Game:")
		fmt.Println("kubectl apply -f https://github.com/openkruise/kruise-game/releases/latest/download/kruise_game.yaml")

		// 演示其他不依赖 OpenKruise Game 的功能
		demonstrateBasicK8sOperations(ctx, k8sService)
		return
	}

	fmt.Printf("Created GameServerSet: %s/%s with %d replicas\n",
		gssInfo.Namespace, gssInfo.Name, gssInfo.Replicas)

	// 等待一段时间让 GameServer 启动
	time.Sleep(5 * time.Second)

	// 2. 列出游戏服务器
	fmt.Println("\n2. 列出游戏服务器")
	gameServers, err := kruiseGame.ListGameServers(ctx, clusterName, &k8smgr.GameServerFilter{
		Namespace: "default",
		Labels: map[string]string{
			"game": "demo-game",
		},
	})
	if err != nil {
		fmt.Printf("Failed to list GameServers: %v\n", err)
		return
	}

	fmt.Printf("Found %d GameServers:\n", len(gameServers))
	for _, gs := range gameServers {
		fmt.Printf("  - %s: State=%s, OpsState=%s, Address=%s\n",
			gs.Name, gs.State, gs.OpsState, gs.InternalAddress)
	}

	// 3. 获取统计信息
	fmt.Println("\n3. 获取统计信息")
	stats, err := k8sService.GetGameServerStats(ctx)
	if err != nil {
		fmt.Printf("Failed to get game server stats: %v\n", err)
	} else {
		for clusterName, clusterStats := range stats {
			fmt.Printf("Cluster %s: Total=%d, Ready=%d, NotReady=%d, Crash=%d\n",
				clusterName, clusterStats.Total, clusterStats.Ready, clusterStats.NotReady, clusterStats.Crash)
		}
	}

	// 4. 伸缩游戏服务器集合
	fmt.Println("\n4. 伸缩游戏服务器集合")
	err = kruiseGame.ScaleGameServerSet(ctx, clusterName, "default", "demo-game-servers", 3)
	if err != nil {
		fmt.Printf("Failed to scale GameServerSet: %v\n", err)
	} else {
		fmt.Println("Successfully scaled GameServerSet to 3 replicas")
	}

	// 5. 批量操作示例
	fmt.Println("\n5. 批量操作示例")
	updatePriority := 1
	updatedNames, err := kruiseGame.BatchUpdateGameServers(ctx, clusterName,
		&k8smgr.GameServerFilter{
			Namespace: "default",
			Labels: map[string]string{
				"game": "demo-game",
			},
		},
		&k8smgr.GameServerUpdateRequest{
			UpdatePriority: &updatePriority,
			Labels: map[string]string{
				"batch-updated": "true",
			},
		})
	if err != nil {
		fmt.Printf("Failed to batch update GameServers: %v\n", err)
	} else {
		fmt.Printf("Batch updated %d GameServers: %v\n", len(updatedNames), updatedNames)
	}
	time.Sleep(240 * time.Second)

	// 6. 清理资源
	fmt.Println("\n6. 清理资源")
	err = kruiseGame.DeleteGameServerSet(ctx, clusterName, "default", "demo-game-servers")
	if err != nil {
		fmt.Printf("Failed to delete GameServerSet: %v\n", err)
	} else {
		fmt.Println("Successfully deleted GameServerSet")
	}
}

// demonstrateBasicK8sOperations 演示基础的 K8s 操作（不依赖 OpenKruise Game）
func demonstrateBasicK8sOperations(ctx context.Context, k8sService *K8sService) {
	fmt.Println("\n=== 基础 K8s 操作演示 ===")

	// 获取集群信息
	healthyClusters := k8sService.manager.GetHealthyClusters()
	fmt.Printf("健康集群数量: %d\n", len(healthyClusters))

	for _, clusterName := range healthyClusters {
		fmt.Printf("集群: %s\n", clusterName)

		// 获取客户端
		client, err := k8sService.manager.GetClient(clusterName)
		if err != nil {
			fmt.Printf("  获取客户端失败: %v\n", err)
			continue
		}

		// 列出命名空间
		namespaces, err := client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
		if err != nil {
			fmt.Printf("  列出命名空间失败: %v\n", err)
			continue
		}

		fmt.Printf("  命名空间数量: %d\n", len(namespaces.Items))

		// 列出 default 命名空间中的 Pod
		pods, err := client.CoreV1().Pods("default").List(ctx, metav1.ListOptions{})
		if err != nil {
			fmt.Printf("  列出 Pod 失败: %v\n", err)
			continue
		}

		fmt.Printf("  default 命名空间中的 Pod 数量: %d\n", len(pods.Items))

		// 列出 Service
		services, err := client.CoreV1().Services("default").List(ctx, metav1.ListOptions{})
		if err != nil {
			fmt.Printf("  列出 Service 失败: %v\n", err)
			continue
		}

		fmt.Printf("  default 命名空间中的 Service 数量: %d\n", len(services.Items))
	}

	// 获取管理器统计信息
	stats := k8sService.manager.GetStats()
	fmt.Println("\n管理器统计信息:")
	fmt.Printf("  配置: %+v\n", stats["config"])
	fmt.Printf("  健康状态: %+v\n", stats["health"])
}

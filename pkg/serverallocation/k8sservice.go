package serverallocation

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/k8smgr"
)

// 游戏服namespace
func GetGSSNamespace() string {
	return "multiverse-game-server"
}

// 每CPU内存限制(一个cpu对应1.5g内存)
func GetMemoryLimitPerCpu() int {
	return 1024 * 1.5
}

// 计算cpu对应的内存 最大4核
func CalcMemoryForCPU(cpuNumber float32) int {
	if cpuNumber <= 0 {
		cpuNumber = 0.1
	}
	if cpuNumber > 4 {
		cpuNumber = 4
	}
	// 内存 = CPU核数 × 每CPU内存限制
	return int(cpuNumber * float32(GetMemoryLimitPerCpu()))
}

// K8sService 集成到现有项目的k8s服务
type K8sService struct {
	manager *k8smgr.Manager
	config  *config.Config
}

// NewK8sService 创建k8s服务实例
func NewK8sService(cfg *config.Config) (*K8sService, error) {
	// 从项目配置创建多集群配置
	multiClusterConfig := createMultiClusterConfigFromProjectConfig(cfg)

	// 创建管理器
	manager, err := k8smgr.NewManager(multiClusterConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create k8s manager: %w", err)
	}

	return &K8sService{
		manager: manager,
		config:  cfg,
	}, nil
}

// Start 启动k8s服务
func (s *K8sService) Start(ctx context.Context) error {
	log.Println("Starting K8s service...")

	if err := s.manager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start k8s manager: %w", err)
	}

	log.Println("K8s service started successfully")
	return nil
}

// Stop 停止k8s服务
func (s *K8sService) Stop() error {
	log.Println("Stopping K8s service...")

	if err := s.manager.Stop(); err != nil {
		return fmt.Errorf("failed to stop k8s manager: %w", err)
	}

	log.Println("K8s service stopped")
	return nil
}

// GetManager 获取集群管理器
func (s *K8sService) GetManager() *k8smgr.Manager {
	return s.manager
}

// createMultiClusterConfigFromProjectConfig 从项目配置创建多集群配置
func createMultiClusterConfigFromProjectConfig(cfg *config.Config) *k8smgr.MultiClusterConfig {
	// 这里可以从项目配置文件或环境变量读取集群配置
	// TODO 这里从配置文件读取或者从数据库中读取

	clusters := []*k8smgr.ClusterConfig{
		{
			Name:        "kind-1m3w",
			Scope:       "local",
			Description: "Development cluster",
			Environment: "dev",
			Region:      "us-west-1",
			APIServer:   "https://127.0.0.1:45875",
			AuthType:    k8smgr.AuthTypeToken,
			Auth: k8smgr.AuthConfig{
				Token:                 "eyJhbGciOiJSUzI1NiIsImtpZCI6InFMMVk4dDFMUUoyaDFSU09LWks0WDJnd0s2YlZpTDBHQjhOOTU0Uy1ZakEifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.U5UJYLfIBfRxaQfvrbh8Xy9W5iAJS5eP6lN2rRWBCkNzVMKPR7wjKrsnnpF7-K7xKDQEadf52mG--YQLO1VGDQOw66YcRODczX3Ul09euWCFbKp5UkCSgI1bofWJ6Zd9uP5wac7GZyzZjh_IPLdpsFhsF5YF0HnaVW7Odswux18YQQr8VuWdb6BXvcvPGlMJ3fTJXZel6AAw1SoZmPW_VmMVbmp9F4WejqHrB5ku41PbhXTaWDWMJc5zV8tQ9RK2ZgS1yc4mU8YMOkqGGDQh4Oi6yBVD2J4zXvRBqabj0VHjHuVeEsO1rhHfG_OM1CAXPGhbctuJ0VGTiajQh1sHzQ", // 使用上面方法获取的实际 token
				InsecureSkipTLSVerify: true,
			},
			Timeout:       30 * time.Second,
			QPS:           100,
			Burst:         100,
			MaxRetries:    3,
			RetryInterval: 5 * time.Second,
			HealthCheck: k8smgr.HealthCheckConfig{
				Enabled:          true,
				Interval:         30 * time.Second,
				Timeout:          10 * time.Second,
				FailureThreshold: 3,
				SuccessThreshold: 1,
				InitialDelay:     10 * time.Second,
			},
			Labels: map[string]string{
				"env":    "dev",
				"region": "us-west-1",
			},
			Priority: 1,
			Weight:   1,
			Enabled:  true,
		},
		{
			Name:        "kind-1m1w",
			Scope:       "local",
			Description: "Production cluster",
			Environment: "prod",
			Region:      "us-east-1",
			APIServer:   "https://127.0.0.1:36823",
			AuthType:    k8smgr.AuthTypeToken, // 生产环境使用集群内认证
			Auth: k8smgr.AuthConfig{
				Token:                 "eyJhbGciOiJSUzI1NiIsImtpZCI6IkxhcmdJZzAzVk5FREJvZlVCeHczOHpsQXFOYVdlQjBzSHRGTGhpSUxNdWsifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BkSbiHXijFPoIB8mvdNrFdo0qk-MRmyqq54ph0AQwrlP3shQ4bMBju-Jp_RR2tkNfceSGGmrJCmnZiNjYqPiAiHQQ1z2E_gCnsrj0pc1GBbD-3Y_GdJwNZZ0t4X8Qy8U1LG_aNm5S-ec0Y2jf6PpbPb3jP9dPH-wyIb6IyEFoeKiT3YHI2aA5HrtrrmG9XqeKZr6W_8WS79p_k2fUNrgA4CX5IhuAlKaAOJsLYSx9wApmaHSAVqTcapqMtGNqh-84EpCIqCSc_VdlSotLJiFGkh2okQnyRsWdRDZbys6VX1on05rUKTedW6rvPjUPUuC7hlNXh7Otwu7n8USyGUDJg",
				InsecureSkipTLSVerify: true,
			},
			Timeout:       30 * time.Second,
			QPS:           200,
			Burst:         200,
			MaxRetries:    3,
			RetryInterval: 5 * time.Second,
			HealthCheck: k8smgr.HealthCheckConfig{
				Enabled:          true,
				Interval:         30 * time.Second,
				Timeout:          10 * time.Second,
				FailureThreshold: 3,
				SuccessThreshold: 1,
				InitialDelay:     10 * time.Second,
			},
			Labels: map[string]string{
				"env":    "prod",
				"region": "us-east-1",
			},
			Priority: 2,
			Weight:   3,
			Enabled:  true,
		},
	}

	// 过滤启用的集群
	var enabledClusters []*k8smgr.ClusterConfig
	for _, cluster := range clusters {
		if cluster.Enabled {
			enabledClusters = append(enabledClusters, cluster)
		}
	}

	return &k8smgr.MultiClusterConfig{
		Clusters:            enabledClusters,
		DefaultCluster:      getDefaultCluster(cfg),
		LoadBalanceStrategy: k8smgr.LoadBalanceWeighted,
		GlobalTimeout:       60 * time.Second,
		GlobalRetries:       3,
		HealthCheckPeriod:   30 * time.Second,
		MaxIdleConns:        10,
		MaxActiveConns:      100,
		ConnMaxLifetime:     1 * time.Hour,
	}
}

// ############################################################
// ############  封装 OpenKruiseGame 操作接口 ############
// ############################################################
// GetKruiseGameOperations 获取 OpenKruise Game 操作接口
func (s *K8sService) GetKruiseGameOperations() k8smgr.KruiseGameOperations {
	return s.manager.KruiseGame()
}

// ReleaseGameServer 释放游戏服务器
func (s *K8sService) ReleaseGameServer(ctx context.Context, clusterName, namespace, serverName string) error {
	kruiseGame := s.manager.KruiseGame()

	updateReq := &k8smgr.GameServerUpdateRequest{
		Name:      serverName,
		Namespace: namespace,
		Labels: map[string]string{
			"allocated": "false",
		},
		Annotations: map[string]string{
			"release.time": time.Now().Format(time.RFC3339),
		},
	}

	_, err := kruiseGame.UpdateGameServer(ctx, clusterName, updateReq)
	if err != nil {
		return fmt.Errorf("failed to release game server: %w", err)
	}

	log.Printf("Released game server %s/%s", namespace, serverName)
	return nil
}

// 自动分配游戏服务器
func (s *K8sService) AutoAllocateGSS(ctx context.Context, region string, req *k8smgr.GameServerSetCreateRequest) (*k8smgr.GameServerSetInfo, error) {
	clusters := s.manager.GetHealthyClusters()

	// TODO 根据调度策略选择集群
	if len(clusters) == 0 {
		return nil, fmt.Errorf("no healthy clusters found")
	}
	cluster := clusters[0]
	// 根据region选择集群
	for _, v := range clusters {
		if v == "kind-1m3w" {
			cluster = v
		}
	}

	// 创建游戏服务器集合
	gssInfo, err := s.CreateGSS(ctx, cluster, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create GameServerSet: %w", err)
	}

	return gssInfo, nil
}

// CreateGSS 创建游戏服务器集合
func (s *K8sService) CreateGSS(ctx context.Context, clusterName string, req *k8smgr.GameServerSetCreateRequest) (*k8smgr.GameServerSetInfo, error) {
	kruiseGame := s.manager.KruiseGame()

	gssInfo, err := kruiseGame.CreateGameServerSet(ctx, clusterName, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create GameServerSet: %w", err)
	}

	log.Printf("Created GameServerSet %s/%s with %d replicas in cluster %s",
		gssInfo.Namespace, gssInfo.Name, gssInfo.Replicas, clusterName)

	return gssInfo, nil
}

// GetGameServerStats 获取游戏服务器统计信息
func (s *K8sService) GetGameServerStats(ctx context.Context) (map[string]*k8smgr.GameServerStats, error) {
	kruiseGame := s.manager.KruiseGame()

	stats, err := kruiseGame.GetGameServerStatsFromAllClusters(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get game server stats: %w", err)
	}

	return stats, nil
}

// getDefaultCluster 根据环境获取默认集群
func getDefaultCluster(cfg *config.Config) string {
	if cfg.Server.IsProd() {
		return "prod-cluster"
	}
	return "kind-1m3w"
}

// k8s service 单例
var K8sServiceSingleton = sync.OnceValue(func() *K8sService {
	mgr, err := NewK8sService(&config.GlobalConfig)

	if err != nil {
		panic(err)
	}
	return mgr
})

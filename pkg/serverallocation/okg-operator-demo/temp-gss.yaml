apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  annotations:
    uuid: 5ee69913-27b2-45d8-901f-5612c4cd5422
  labels:
    uuid: 5ee69913-27b2-45d8-901f-5612c4cd5422
  name: demo002-5ee69913-27b2-45d8-901f-5612c4cd5422
spec:
  gameServerTemplate:
    spec:
      containers:
        - env:
            - name: env1
              value: value1
          image: nginx:latest
          name: game-server
          ports:
            - containerPort: 80
              name: http
              protocol: TCP
          resources:
            requests:
              cpu: "0.200000"
              memory: 307Mi
  replicas: 1

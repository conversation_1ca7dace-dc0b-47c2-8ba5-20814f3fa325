Name:         minecraft
Namespace:    default
Labels:       <none>
Annotations:  <none>
API Version:  game.kruise.io/v1alpha1
Kind:         GameServerSet
Metadata:
  Creation Timestamp:  2025-07-30T06:30:51Z
  Generation:          1
  Resource Version:    347584
  UID:                 6fa92ca9-95dd-441d-8e21-ec5509774da6
Spec:
  Game Server Template:
    Spec:
      Containers:
        Image:  registry.cn-hangzhou.aliyuncs.com/acs/minecraft-demo:1.12.2
        Name:   minecraft
  Replicas:     3
  Update Strategy:
    Rolling Update:
      Pod Update Policy:  InPlaceIfPossible
Status:
  Available Replicas:           0
  Current Replicas:             3
  Label Selector:               game.kruise.io/owner-gss=minecraft
  Maintaining Replicas:         0
  Observed Generation:          1
  Pre Delete Replicas:          0
  Ready Replicas:               0
  Replicas:                     3
  Updated Replicas:             3
  Wait To Be Deleted Replicas:  0
Events:
  Type    Reason          Age   From                      Message
  ----    ------          ----  ----                      -------
  Normal  CreateWorkload  40s   gameserverset-controller  created Advanced StatefulSet

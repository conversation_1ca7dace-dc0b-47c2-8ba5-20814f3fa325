package uuid

import (
	"fmt"
	"strings"

	"github.com/google/uuid"
)

// UUIDGenerator UUID生成器接口
type UUIDGenerator interface {
	Generate() string
	GenerateWithPrefix(prefix string) string
	GenerateShort() string
	GenerateV7() (string, error)
	GenerateV1() (string, error)
	GenerateCustom(format string) string
	IsValid(uuidStr string) bool
	Parse(uuidStr string) (uuid.UUID, error)
}

// DefaultGenerator 默认UUID生成器
type DefaultGenerator struct{}

// NewGenerator 创建新的UUID生成器
func NewGenerator() UUIDGenerator {
	return &DefaultGenerator{}
}

// Generate 生成标准UUID v4
func (g *DefaultGenerator) Generate() string {
	return uuid.New().String()
}

// GenerateV7 生成UUID v7 (时间排序友好)
func (g *DefaultGenerator) GenerateV7() (string, error) {
	id, err := uuid.NewV7()
	if err != nil {
		return "", fmt.Errorf("生成UUID v7失败: %w", err)
	}
	return id.String(), nil
}

// GenerateV1 生成UUID v1 (基于时间)
func (g *DefaultGenerator) GenerateV1() (string, error) {
	id, err := uuid.NewUUID()
	if err != nil {
		return "", fmt.Errorf("生成UUID v1失败: %w", err)
	}
	return id.String(), nil
}

// GenerateWithPrefix 生成带前缀的UUID
func (g *DefaultGenerator) GenerateWithPrefix(prefix string) string {
	if prefix == "" {
		return g.Generate()
	}
	return fmt.Sprintf("%s_%s", prefix, strings.ReplaceAll(g.Generate(), "-", ""))
}

// GenerateShort 生成短UUID (去掉连字符)
func (g *DefaultGenerator) GenerateShort() string {
	return strings.ReplaceAll(g.Generate(), "-", "")
}

// GenerateCustom 生成自定义格式UUID
func (g *DefaultGenerator) GenerateCustom(format string) string {
	id := g.Generate()
	switch format {
	case "short":
		return strings.ReplaceAll(id, "-", "")
	case "upper":
		return strings.ToUpper(id)
	case "lower":
		return strings.ToLower(id)
	default:
		return id
	}
}

// IsValid 验证UUID是否有效
func (g *DefaultGenerator) IsValid(uuidStr string) bool {
	_, err := uuid.Parse(uuidStr)
	return err == nil
}

// Parse 解析UUID字符串
func (g *DefaultGenerator) Parse(uuidStr string) (uuid.UUID, error) {
	return uuid.Parse(uuidStr)
}

// 便捷函数 - 全局实例
var defaultGenerator = NewGenerator()

// Generate 生成标准UUID
func Generate() string {
	return defaultGenerator.Generate()
}

// GenerateV7 生成UUID v7
func GenerateV7() (string, error) {
	return defaultGenerator.GenerateV7()
}

// GenerateV1 生成UUID v1
func GenerateV1() (string, error) {
	return defaultGenerator.GenerateV1()
}

// GenerateWithPrefix 生成带前缀的UUID
func GenerateWithPrefix(prefix string) string {
	return defaultGenerator.GenerateWithPrefix(prefix)
}

// GenerateShort 生成短UUID
func GenerateShort() string {
	return defaultGenerator.GenerateShort()
}

// GenerateCustom 生成自定义格式UUID
func GenerateCustom(format string) string {
	return defaultGenerator.GenerateCustom(format)
}

// IsValid 验证UUID
func IsValid(uuidStr string) bool {
	return defaultGenerator.IsValid(uuidStr)
}

// Parse 解析UUID
func Parse(uuidStr string) (uuid.UUID, error) {
	return defaultGenerator.Parse(uuidStr)
}

// BatchGenerate 批量生成UUID
func BatchGenerate(count int) []string {
	uuids := make([]string, count)
	for i := 0; i < count; i++ {
		uuids[i] = Generate()
	}
	return uuids
}

// BatchGenerateWithPrefix 批量生成带前缀的UUID
func BatchGenerateWithPrefix(prefix string, count int) []string {
	uuids := make([]string, count)
	for i := 0; i < count; i++ {
		uuids[i] = GenerateWithPrefix(prefix)
	}
	return uuids
}

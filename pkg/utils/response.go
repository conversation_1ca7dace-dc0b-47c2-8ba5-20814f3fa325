package utils

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

type CodeType int

const (
	CodeSuccess CodeType = 200  // 通用成功
	CodeFail    CodeType = 6499 // 通用失败

	// 6500~6599 通用业务错误
	CodeInvalidParameter CodeType = 6500 // 参数无效
	CodeNotFound         CodeType = 6501 // 资源不存在
	CodeAlreadyExists    CodeType = 6502 // 资源已存在
	CodeUnauthorized     CodeType = 6503 // 未授权
	CodeForbidden        CodeType = 6504 // 禁止访问
	CodeMethodNotAllowed CodeType = 6505 // 方法不允许
	CodeTooManyRequests  CodeType = 6506 // 请求过多

	// 6600~6699 用户相关业务错误
	CodeUserNotFound      CodeType = 6600 // 用户不存在
	CodeUserAlreadyExists CodeType = 6601 // 用户已存在
	CodeIncorrectPassword CodeType = 6602 // 密码不正确
	CodeAccountLocked     CodeType = 6603 // 账号被锁定

	// 6700~6799 数据库相关业务错误
	CodeDatabaseError  CodeType = 6700 // 数据库操作失败
	CodeRecordNotFound CodeType = 6701 // 记录不存在
	CodeDuplicateEntry CodeType = 6702 // 记录已存在（唯一约束冲突）

	// 6800~6899 外部服务相关业务错误
	CodeExternalServiceError   CodeType = 6800 // 调用外部服务失败
	CodeExternalServiceTimeout CodeType = 6801 // 调用外部服务超时
)

func ResponseWithGin(c *gin.Context, message string, success bool, code CodeType, data interface{}) {
	// 统一响应结构，所有响应都包含 code 字段
	if success {
		c.JSON(http.StatusOK, gin.H{
			"code":    code, // 使用传入的code
			"message": message,
			"data":    data,
		})
	} else {
		statusCode := http.StatusBadRequest
		// 对于失败响应，如果传入了特定的错误码，就使用它，否则使用通用失败码 CodeFail
		if code == CodeSuccess {
			code = CodeFail // 避免成功码用于失败响应
		}
		if message == "服务器内部错误" || code == http.StatusInternalServerError {
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{
			"code":    code,
			"error":   message,
			"message": message,
		})
	}
}

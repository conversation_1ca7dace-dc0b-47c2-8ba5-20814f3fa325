package parameter

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	ib "git.mg.xyz/paas-group/ros-group/multiverse/pkg/imagebuild"
)

// ImageBuildUploadRequest 上传请求参数
type ImageBuildUploadRequest struct {
	BaseImage   string `json:"baseImage" binding:"required"`
	ProjectName string `json:"projectName" binding:"required"`
	Description string `json:"description"`
	EntryPoint  string `json:"entryPoint" binding:"required"`
}

// ImageBuildUploadResponse 上传响应
type ImageBuildUploadResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	ProjectID string `json:"projectId,omitempty"`
	BuildID   string `json:"buildId,omitempty"`
}

// ImageBuildRuntimesResponse 运行时环境响应
type ImageBuildRuntimesResponse struct {
	BaseImages map[string]*model.BaseImageConfig `json:"baseImages"`
}

// ImageBuildStatusResponse 构建状态响应
type ImageBuildStatusResponse struct {
	Build *ib.BuildStatus `json:"build"`
}

// ImageBuildListResponse 构建列表响应
type ImageBuildListResponse struct {
	Builds []*ib.BuildStatus `json:"builds"`
}

// ImageBuildSecurityCheckResponse 安全检查响应
type ImageBuildSecurityCheckResponse struct {
	Safe            bool     `json:"safe"`
	Score           int      `json:"score"`
	Errors          []string `json:"errors,omitempty"`
	Recommendations []string `json:"recommendations,omitempty"`
}

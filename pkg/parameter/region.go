package parameter

import "git.mg.xyz/paas-group/ros-group/multiverse/internal/model"

type CreateUserRegionRequest struct {
	UserId string `json:"user_id"` //用户id
}

type UserRegionListRequest struct {
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
	UserName   string `json:"user_name"`
	RegionName string `json:"region_name"`
	Zone       string `json:"zone"`
	Enabled    int    `json:"enabled"`
}

type UserRegionListResponse struct {
	Total int64              `json:"total"` //总数
	Limit int                `json:"limit"` //限制数
	List  []model.UserRegion `json:"list"`  //数据
}

type UserRegionEnabledOrDisabled struct {
	Id int `json:"id"`
}

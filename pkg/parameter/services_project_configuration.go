package parameter

import "git.mg.xyz/paas-group/ros-group/multiverse/internal/model"

// 项目设置请求相关参数
type ServiceProjectConfiguration struct {
	ServerTimeout int64  `json:"server_timeout" binding:"required"`
	ServerOS      string `json:"server_os" binding:"required"`
	CallbackURL   string `json:"callback_url" binding:"omitempty,url"`
}

// 项目设置响应相关参数
type ServiceProjectConfigurationResponse model.ProjectConfiguration

package parameter

import "git.mg.xyz/paas-group/ros-group/multiverse/internal/model"

// 启动配置请求相关参数
type ServiceStartupConfigurationCreateReq struct {
	Name string `json:"name" binding:"required,name_1_128"` // 启动配置名字 长度大于1小于128个字符
}

// 更新启动配置请求相关参数
type ServiceStartupConfigurationUpdateReq struct {
	ImageConfigID int64 `json:"image_config_id" binding:"required"` // 镜像配置ID
}

// 启动配置响应相关参数
type ServiceStartupConfigurationResponse model.StartupConfiguration

package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// BusinessError 业务错误类型
type BusinessError struct {
	Code    CodeType `json:"code"`
	Message string   `json:"message"`
}

func (e *BusinessError) Error() string {
	return e.Message
}

// ResponseWithAudit 封装带审计日志的HTTP响应
// code 字段说明：
//
//	6400：通用成功
//	6499：通用失败
//	6500~6999：业务自定义
func ResponseWithAudit(c *gin.Context, operationType string, resourceType string, message string, success bool, code CodeType, data interface{}) {
	// // 获取客户端IP
	// clientIP := c.ClientIP()

	// // 记录审计日志
	// status := "success"
	// if !success {
	// 	status = "failed"
	// }

	// if err := audit.GetLogger().LogWithContext(
	// 	operationType,
	// 	"system",
	// 	resourceType,
	// 	message,
	// 	status,
	// 	clientIP,
	// 	"",
	// ); err != nil {
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": "审计日志记录失败", "code": CodeFail})
	// 	return
	// }

	// 统一响应结构，所有响应都包含 code 字段
	if success {
		c.JSON(http.StatusOK, CommonResponse{
			Code:    code, // 使用传入的code
			Message: message,
			Data:    data,
		})
	} else {
		statusCode := http.StatusBadRequest
		// 对于失败响应，如果传入了特定的错误码，就使用它，否则使用通用失败码 CodeFail
		if code == CodeSuccess {
			code = CodeFail // 避免成功码用于失败响应
		}
		if message == "服务器内部错误" || code == http.StatusInternalServerError {
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, CommonResponse{
			Code:    code,
			Error:   message,
			Message: message,
		})
	}
}

// HandleErrorWithAudit 处理错误并记录审计日志
// code 字段说明：
//
//	6499：通用失败
//	6500~6999：业务自定义错误码
func HandleErrorWithAudit(c *gin.Context, operationType string, resourceType string, err error, customMessage string, code CodeType) {
	message := customMessage
	if err != nil && err.Error() != "" {
		if message != "" {
			message = message + ": " + err.Error()
		} else {
			message = err.Error()
		}
	}
	ResponseWithAudit(c, operationType, resourceType, message, false, code, nil)
}

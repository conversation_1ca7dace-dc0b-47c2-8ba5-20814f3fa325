package response

// CodeType 业务响应码类型
// 取值范围：6400~6999
//
//	CodeSuccess: 6400 通用成功
//	CodeFail:    6499 通用失败
//	6500~6999:   业务自定义
type CodeType int

const (
	CodeSuccess CodeType = 6400 // 通用成功
	CodeFail    CodeType = 6499 // 通用失败

	// 6500~6599 通用业务错误
	CodeInvalidParameter CodeType = 6500 // 参数无效
	CodeNotFound         CodeType = 6501 // 资源不存在
	CodeAlreadyExists    CodeType = 6502 // 资源已存在
	CodeUnauthorized     CodeType = 6503 // 未授权
	CodeForbidden        CodeType = 6504 // 禁止访问
	CodeMethodNotAllowed CodeType = 6505 // 方法不允许
	CodeTooManyRequests  CodeType = 6506 // 请求过多

	// 6600~6699 用户相关业务错误
	CodeUserNotFound      CodeType = 6600 // 用户不存在
	CodeUserAlreadyExists CodeType = 6601 // 用户已存在
	CodeIncorrectPassword CodeType = 6602 // 密码不正确
	CodeAccountLocked     CodeType = 6603 // 账号被锁定

	// 6700~6799 数据库相关业务错误
	CodeDatabaseError  CodeType = 6700 // 数据库操作失败
	CodeRecordNotFound CodeType = 6701 // 记录不存在
	CodeDuplicateEntry CodeType = 6702 // 记录已存在（唯一约束冲突）

	// 6800~6899 外部服务相关业务错误
	CodeExternalServiceError   CodeType = 6800 // 调用外部服务失败
	CodeExternalServiceTimeout CodeType = 6801 // 调用外部服务超时
)

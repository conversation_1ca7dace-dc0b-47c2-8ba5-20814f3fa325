#!/bin/bash

# Rancher Setup Script for Kind Clusters
# This script helps configure <PERSON><PERSON> to manage Kind clusters

set -e

echo "=== Rancher Setup for Kind Clusters ==="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
RANCHER_URL="https://localhost:8443"
BOOTSTRAP_PASSWORD="dzfnt44rczpbchdlgk6t4bf2zmrv4l9s6g94bvrslhk2p4vc74lbg7"

echo -e "${GREEN}Step 1: Rancher Server Information${NC}"
echo "Rancher URL: $RANCHER_URL"
echo "Bootstrap Password: $BOOTSTRAP_PASSWORD"
echo ""

echo -e "${GREEN}Step 2: Checking Kind Clusters${NC}"
echo "Available Kind clusters:"
kind get clusters
echo ""

echo -e "${GREEN}Step 3: Getting cluster contexts${NC}"
kubectl config get-contexts
echo ""

echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Open your browser and go to: $RANCHER_URL"
echo "2. Accept the self-signed certificate warning"
echo "3. Use the bootstrap password: $BOOTSTRAP_PASSWORD"
echo "4. Set up your admin password and Rancher server URL"
echo "5. Once logged in, you can import your Kind clusters"
echo ""

echo -e "${GREEN}Step 4: Preparing cluster import commands${NC}"
echo ""

# Function to generate import commands for each cluster
generate_import_commands() {
    local cluster_name=$1
    local context_name="kind-$cluster_name"
    
    echo -e "${YELLOW}For cluster: $cluster_name${NC}"
    echo "Context: $context_name"
    
    # Get the cluster endpoint
    local cluster_endpoint=$(kubectl config view -o jsonpath="{.clusters[?(@.name=='$context_name')].cluster.server}")
    echo "Cluster endpoint: $cluster_endpoint"
    
    # Get the cluster CA certificate
    echo "To get cluster CA certificate, run:"
    echo "kubectl config view --raw -o jsonpath='{.clusters[?(@.name==\"$context_name\")].cluster.certificate-authority-data}' | base64 -d"
    
    echo ""
}

# Generate commands for both clusters
generate_import_commands "1m1w"
generate_import_commands "1m3w"

echo -e "${GREEN}Step 5: Additional Setup for Kind Clusters${NC}"
echo ""
echo "Since Kind clusters run in Docker containers, you may need to:"
echo "1. Ensure Rancher can reach the Kind cluster API servers"
echo "2. The Kind clusters are accessible at:"
echo "   - 1m1w: https://127.0.0.1:36823"
echo "   - 1m3w: https://127.0.0.1:45875"
echo ""

echo -e "${YELLOW}Important Notes:${NC}"
echo "- Kind clusters use localhost/127.0.0.1 which may not be accessible from Rancher container"
echo "- You might need to use the Docker internal network or host networking"
echo "- Consider using the Docker host IP instead of localhost"
echo ""

# Get Docker host IP
echo "Docker host IP (use this instead of localhost in Rancher):"
docker network inspect bridge | grep Gateway | awk '{print $2}' | tr -d '",'

echo ""
echo -e "${GREEN}Setup script completed!${NC}"
echo "You can now proceed to configure Rancher through the web interface."

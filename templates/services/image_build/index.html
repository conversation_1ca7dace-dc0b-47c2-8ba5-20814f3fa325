<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .file-list {
            max-height: 200px;
            overflow-y: auto;
        }
        .build-log {
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            padding: 15px;
            border-radius: 4px;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .runtime-card {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .runtime-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .runtime-card.selected {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-rocket text-primary"></i>
                    玩家程序构建系统
                </h1>
            </div>
        </div>

        <div class="row">
            <!-- 左侧：上传区域 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-upload"></i> 上传程序文件</h5>
                    </div>
                    <div class="card-body">
                        <form id="uploadForm">
                            <!-- 基础镜像选择 -->
                            <div class="mb-3">
                                <label class="form-label">选择基础镜像</label>
                                <div id="baseImageSelection" class="row g-2">
                                    <!-- 动态加载基础镜像选项 -->
                                </div>
                            </div>

                            <!-- 项目信息 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="projectName" class="form-label">项目名称</label>
                                    <input type="text" class="form-control" id="projectName" name="projectName" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="description" class="form-label">项目描述</label>
                                    <input type="text" class="form-control" id="description" name="description">
                                </div>
                            </div>

                            <!-- 入口程序配置 -->
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label for="entryPoint" class="form-label">入口程序</label>
                                    <input type="text" class="form-control" id="entryPoint" name="entryPoint"
                                           placeholder="例如: ./app.exe, ./start.sh, java -jar app.jar" required>
                                    <div class="form-text">指定应用程序的启动命令</div>
                                </div>
                                <div class="col-md-4">
                                    <label for="workingDir" class="form-label">工作目录</label>
                                    <input type="text" class="form-control" id="workingDir" name="workingDir"
                                           placeholder="/app" value="/app">
                                    <div class="form-text">容器内的工作目录</div>
                                </div>
                            </div>

                            <!-- ZIP文件上传区域 -->
                            <div class="mb-3">
                                <label class="form-label">上传ZIP包</label>
                                <div class="upload-area" id="uploadArea">
                                    <i class="fas fa-file-archive fa-3x text-muted mb-3"></i>
                                    <p class="mb-2">点击或拖拽ZIP文件到此处</p>
                                    <p class="text-muted small">只支持ZIP格式，最大100MB</p>
                                    <input type="file" id="zipInput" name="zipFile" accept=".zip" style="display: none;">
                                </div>
                            </div>

                            <!-- ZIP文件信息 -->
                            <div id="zipInfoContainer" class="mb-3" style="display: none;">
                                <label class="form-label">ZIP文件信息</label>
                                <div id="zipInfo" class="border rounded p-2 bg-light">
                                </div>
                            </div>



                            <!-- 提交按钮 -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <i class="fas fa-rocket"></i> 开始构建
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 右侧：构建状态 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tasks"></i> 构建状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="buildStatus" class="text-center text-muted">
                            <i class="fas fa-info-circle"></i>
                            <p>请先上传文件开始构建</p>
                        </div>
                        <div id="buildLogs" style="display: none;">
                            <h6>构建日志</h6>
                            <div class="build-log" id="logContainer">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 历史构建 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-history"></i> 构建历史</h6>
                    </div>
                    <div class="card-body">
                        <div id="buildHistory">
                            <p class="text-muted small">暂无构建历史</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedBaseImage = '';
        let selectedZipFile = null;
        let currentBuildId = '';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadBaseImages();
            loadBuildHistory();
            setupEventListeners();
        });

        // 加载基础镜像
        async function loadBaseImages() {
            try {
                const response = await fetch('/api/v1/ros/multiverse/services/image-build/runtimes');
                const data = await response.json();

                if (data.code === 6400 && data.data) {
                    const container = document.getElementById('baseImageSelection');
                    container.innerHTML = '';

                    Object.keys(data.data.baseImages).forEach(key => {
                        const baseImage = data.data.baseImages[key];
                        const card = document.createElement('div');
                        card.className = 'col-md-6 col-lg-4';
                        card.innerHTML = `
                            <div class="card runtime-card h-100" data-base-image="${key}">
                                <div class="card-body text-center p-2">
                                    <div class="mb-1">${baseImage.icon}</div>
                                    <h6 class="card-title mb-1">${baseImage.name}</h6>
                                    <small class="text-muted">${baseImage.description}</small>
                                </div>
                            </div>
                        `;
                        container.appendChild(card);
                    });

                    // 添加点击事件
                    document.querySelectorAll('.runtime-card').forEach(card => {
                        card.addEventListener('click', function() {
                            document.querySelectorAll('.runtime-card').forEach(c => c.classList.remove('selected'));
                            this.classList.add('selected');
                            selectedBaseImage = this.dataset.baseImage;
                        });
                    });
                }
            } catch (error) {
                console.error('加载基础镜像失败:', error);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const zipInput = document.getElementById('zipInput');
            const form = document.getElementById('uploadForm');

            // 点击上传区域
            uploadArea.addEventListener('click', () => zipInput.click());

            // ZIP文件选择
            zipInput.addEventListener('change', handleZipSelect);

            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // 表单提交
            form.addEventListener('submit', handleSubmit);
        }

        // 处理ZIP文件选择
        function handleZipSelect(event) {
            const file = event.target.files[0];
            if (file) {
                selectedZipFile = file;
                displayZipInfo(file);
            }
        }

        // 处理拖拽
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = Array.from(event.dataTransfer.files);
            if (files.length > 0 && files[0].name.toLowerCase().endsWith('.zip')) {
                selectedZipFile = files[0];
                displayZipInfo(files[0]);
            } else {
                alert('请选择ZIP文件');
            }
        }

        // 显示ZIP文件信息
        function displayZipInfo(file) {
            const container = document.getElementById('zipInfoContainer');
            const info = document.getElementById('zipInfo');

            container.style.display = 'block';
            info.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-file-archive text-primary"></i>
                        <strong>${file.name}</strong>
                    </div>
                    <div class="text-end">
                        <div>${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                        <small class="text-muted">${new Date(file.lastModified).toLocaleString()}</small>
                    </div>
                </div>
            `;
        }

        // 处理表单提交
        async function handleSubmit(event) {
            event.preventDefault();

            if (!selectedBaseImage) {
                alert('请选择基础镜像');
                return;
            }

            if (!selectedZipFile) {
                alert('请选择要上传的ZIP文件');
                return;
            }

            if (!document.getElementById('entryPoint').value.trim()) {
                alert('请指定入口程序');
                return;
            }

            const formData = new FormData();
            formData.append('baseImage', selectedBaseImage);
            formData.append('projectName', document.getElementById('projectName').value);
            formData.append('description', document.getElementById('description').value);
            formData.append('entryPoint', document.getElementById('entryPoint').value);
            formData.append('workingDir', document.getElementById('workingDir').value);
            formData.append('zipFile', selectedZipFile);

            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';

            try {
                const response = await fetch('/api/v1/ros/multiverse/services/image-build/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.code === 6400 && data.data) {
                    currentBuildId = data.data.buildId;
                    showBuildStatus(data.data.buildId);
                    startPollingBuildStatus(data.data.buildId);
                    loadBuildHistory();
                } else {
                    alert('上传失败: ' + (data.message || data.error || '未知错误'));
                }
            } catch (error) {
                console.error('上传失败:', error);
                alert('上传失败: ' + error.message);
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-rocket"></i> 开始构建';
            }
        }

        // 显示构建状态
        function showBuildStatus(buildId) {
            const statusDiv = document.getElementById('buildStatus');
            statusDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">构建中...</span>
                    </div>
                    <p class="mt-2">构建中...</p>
                    <small class="text-muted">构建ID: ${buildId}</small>
                </div>
            `;
            document.getElementById('buildLogs').style.display = 'block';
        }

        // 轮询构建状态
        function startPollingBuildStatus(buildId) {
            const interval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/v1/ros/multiverse/services/image-build/build/${buildId}`);
                    const data = await response.json();

                    if (data.code === 6400 && data.data) {
                        updateBuildStatus(data.data.build);
                        updateBuildLogs(data.data.build.logs);

                        if (data.data.build.status === 'success' || data.data.build.status === 'failed') {
                            clearInterval(interval);
                            loadBuildHistory();
                        }
                    }
                } catch (error) {
                    console.error('获取构建状态失败:', error);
                }
            }, 2000);
        }

        // 更新构建状态
        function updateBuildStatus(build) {
            const statusDiv = document.getElementById('buildStatus');
            let statusHtml = '';
            
            switch (build.status) {
                case 'pending':
                    statusHtml = `
                        <div class="text-center">
                            <i class="fas fa-clock text-warning fa-2x"></i>
                            <p class="mt-2">等待构建</p>
                        </div>
                    `;
                    break;
                case 'building':
                    statusHtml = `
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p class="mt-2">构建中...</p>
                        </div>
                    `;
                    break;
                case 'success':
                    statusHtml = `
                        <div class="text-center">
                            <i class="fas fa-check-circle text-success fa-2x"></i>
                            <p class="mt-2">构建成功</p>
                            <small class="text-muted">镜像: ${build.imageName}</small>
                        </div>
                    `;
                    break;
                case 'failed':
                    statusHtml = `
                        <div class="text-center">
                            <i class="fas fa-times-circle text-danger fa-2x"></i>
                            <p class="mt-2">构建失败</p>
                            <small class="text-danger">${build.error}</small>
                        </div>
                    `;
                    break;
            }
            
            statusDiv.innerHTML = statusHtml;
        }

        // 更新构建日志
        function updateBuildLogs(logs) {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = logs.map(log => `<div>${log}</div>`).join('');
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 加载构建历史
        async function loadBuildHistory() {
            try {
                const response = await fetch('/api/v1/ros/multiverse/services/image-build/builds');
                const data = await response.json();

                if (data.code === 6400 && data.data && data.data.builds.length > 0) {
                    const historyDiv = document.getElementById('buildHistory');
                    historyDiv.innerHTML = data.data.builds.slice(0, 5).map(build => `
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="fw-bold">${build.baseImage || '未知'}</small>
                                <span class="badge ${getStatusBadgeClass(build.status)} status-badge">${getStatusText(build.status)}</span>
                            </div>
                            <small class="text-muted">${new Date(build.createdAt).toLocaleString()}</small>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('加载构建历史失败:', error);
            }
        }

        // 获取状态徽章样式
        function getStatusBadgeClass(status) {
            switch (status) {
                case 'success': return 'bg-success';
                case 'failed': return 'bg-danger';
                case 'building': return 'bg-primary';
                case 'pending': return 'bg-warning';
                default: return 'bg-secondary';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'success': return '成功';
                case 'failed': return '失败';
                case 'building': return '构建中';
                case 'pending': return '等待中';
                default: return '未知';
            }
        }


    </script>
</body>
</html>

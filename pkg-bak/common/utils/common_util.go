package utils

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	mrand "math/rand"
	"net"
	"os"
	"strings"
	"time"
)

func BaseJsonEncode(data interface{}) string {
	mjson, _ := json.Marshal(data)
	mString := string(mjson)
	return mString
}

var (
	chars  = []byte("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
	chars2 = []byte("0123456789abcdefghijklmnopqrstuvwxyz") // 数字、小写字母
)

// RandString .
func RandString(l int) string {
	var bs []byte
	for i := 0; i < l; i++ {
		bs = append(bs, chars[mrand.Intn(len(chars))])
	}
	return string(bs)
}

// RandStringV2 .
func RandStringV2(l int) string {
	var bs []byte
	for i := 0; i < l; i++ {
		bs = append(bs, chars[mrand.Intn(len(chars2))])
	}
	return string(bs)
}

// 获取本机的ip
func GetLocalIP() (net.IP, error) {
	tt, err := net.Interfaces()
	if err != nil {
		return nil, err
	}
	for _, t := range tt {
		aa, err := t.Addrs()
		if err != nil {
			return nil, err
		}
		for _, a := range aa {
			ipnet, ok := a.(*net.IPNet)
			if !ok {
				continue
			}
			v4 := ipnet.IP.To4()
			if v4 == nil || v4[0] == 127 || v4[0] == 169 || v4[0] == 192 { // loopback address
				continue
			}
			return v4, nil
		}
	}
	return nil, errors.New("cannot find local IP address")
}

func GetLocalOkIP() (net.IP, error) {
	for i := 1; i <= 3; i++ {
		ip, err := GetLocalIP()
		if err != nil {
			time.Sleep(time.Second)
			continue
		}
		return ip, err
	}
	return nil, errors.New("get local ip is err")
}

func GetOutBoundIP() (ip string, err error) {
	conn, err := net.Dial("udp", "*******:53")
	if err != nil {
		return
	}
	localAddr := conn.LocalAddr().(*net.UDPAddr)
	ip = strings.Split(localAddr.String(), ":")[0]
	return
}

func GetLocalMac() (mac string) {
	// 获取本机的MAC地址
	interfaces, err := net.Interfaces()
	if err != nil {
		return ""
	}
	for _, inter := range interfaces {
		mac = inter.HardwareAddr.String()
		if inter.Name == "以太网" {
			break
		}
	}
	return mac
}

func GetLocalIdentification() (identification string) {
	return GetLocalMac()
}

// 版本号对比：v1 > v2 ==> 1 或 v1 < v2 ==> -1 或 v1 == v2 ==> 0
func VersionCompare(v1, v2 string) int {
	sv1 := strsToSlice(v1)
	sv2 := strsToSlice(v2)
	s1Appended, s2Appended := apeendZreo(sv1, sv2)
	for i := 0; i < len(s1Appended); i++ {
		if s1Appended[i] > s2Appended[i] {
			return 1
		}
		if s1Appended[i] < s2Appended[i] {
			return -1
		}
	}
	// 退出循环表示版本号相同
	return 0
}

// 补全切片
func apeendZreo(s1, s2 []string) ([]string, []string) {
	var count int
	if len(s1) > len(s2) {
		count = len(s1) - len(s2)
		for i := 0; i < count; i++ {
			s2 = append(s2, "0")
		}
	}
	if len(s1) < len(s2) {
		count = len(s2) - len(s1)
		for i := 0; i < count; i++ {
			s1 = append(s1, "0")
		}
	}

	return s1, s2
}

func strsToSlice(version string) []string {
	return strings.Split(version, ".")
}

// IsZipFile reports file whether is a zip file.
func IsZipFile(filepath string) (bool, error) {
	f, err := os.Open(filepath)
	if err != nil {
		return false, err
	}
	defer f.Close()

	buf := make([]byte, 4)
	_, err = f.Read(buf)
	if err == io.EOF {
		return false, nil
	}
	if err != nil {
		return false, err
	}
	return bytes.Equal(buf, []byte("PK\x03\x04")), nil
}

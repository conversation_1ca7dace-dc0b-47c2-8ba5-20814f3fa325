package utils

import (
	"encoding/base64"
	"encoding/json"
)

// WebRtcEncode Encode encodes the input in base64
func WebRtcEncode(obj interface{}) (string, error) {
	b, err := json.Marshal(obj)
	if err != nil {
		return "", err
	}

	//fmt.Println("encode:%s", string(b))
	return base64.StdEncoding.EncodeToString(b), nil
}

// WebRtcDecode Decode decodes the input from base64
func WebRtcDecode(in string, obj interface{}) error {
	b, err := base64.StdEncoding.DecodeString(in)
	if err != nil {
		return err
	}

	err = json.Unmarshal(b, obj)
	if err != nil {
		return err
	}

	return nil
}

func Base64Encode(in string) string {
	return base64.StdEncoding.EncodeToString([]byte(in))
}

func Base64EncodeByte(in []byte) string {
	return base64.StdEncoding.EncodeToString(in)
}

func Base64Decode(in string) (string, error) {
	b, err := base64.StdEncoding.DecodeString(in)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

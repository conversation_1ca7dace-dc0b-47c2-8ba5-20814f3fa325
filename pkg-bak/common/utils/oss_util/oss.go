package oss_util

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/config"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

func GetSignedURL(objectKey string, expireSeconds int64) (string, error) {
	securityToken, err := GetSecurityToken()
	if err != nil {
		return "", fmt.Errorf("获取安全令牌失败: %w", err)
	}
	client, err := oss.New(
		config.OssSetting.Endpoint,
		securityToken.AccessKeyId,
		securityToken.AccessKeySecret,
		oss.SecurityToken(securityToken.Token), // 兼容STS场景
	)
	if err != nil {
		return "", fmt.Errorf("OSS客户端初始化失败: %w", err)
	}

	bucket, err := client.Bucket(config.OssSetting.BucketName)
	if err != nil {
		return "", err
	}

	// 生成签名 URL
	signedURL, err := bucket.SignURL(objectKey, oss.HTTPGet, expireSeconds)
	if err != nil {
		return "", err
	}

	return signedURL, nil
}

// DownloadFromOSS 下载文件到本地
func DownloadFromOSS(objectKey, localPath string) error {
	// 初始化OSS客户端（补充安全令牌逻辑）
	securityToken, err := GetSecurityToken()
	if err != nil {
		return fmt.Errorf("获取安全令牌失败: %w", err)
	}
	client, err := oss.New(
		config.OssSetting.Endpoint,
		securityToken.AccessKeyId,
		securityToken.AccessKeySecret,
		oss.SecurityToken(securityToken.Token), // 兼容STS场景
	)
	if err != nil {
		return fmt.Errorf("OSS客户端初始化失败: %w", err)
	}

	// 获取存储桶
	bucket, err := client.Bucket(config.OssSetting.BucketName)
	if err != nil {
		return fmt.Errorf("获取存储桶[%s]失败: %w", config.OssSetting.BucketName, err)
	}

	// 创建本地目录（权限模式改为可配置）
	if err := os.MkdirAll(filepath.Dir(localPath), os.ModePerm); err != nil {
		return fmt.Errorf("目录创建失败: %s | 错误: %w", filepath.Dir(localPath), err)
	}

	// 下载文件（优化分片大小与并发数）
	err = bucket.DownloadFile(
		objectKey,
		localPath,
		5*1024*1024,                     // 分片大小调整为5MB
		oss.Routines(5),                 // 并发协程数统一为5
		oss.Checkpoint(true, ""),        // 断点续传记录位置
		oss.TrafficLimitHeader(1048576), // 限速1MB/s（单位：bit/s）
	)

	// 错误处理（增加删除失败日志）
	if err != nil {
		if removeErr := os.Remove(localPath); removeErr != nil {
			log.Printf("警告: 清理文件[%s]失败: %v", localPath, removeErr)
		}
		return fmt.Errorf("文件下载失败 | OSS路径: %s | 错误: %w", objectKey, err)
	}

	// 添加完整性校验（建议补充MD5校验逻辑）
	return nil
}

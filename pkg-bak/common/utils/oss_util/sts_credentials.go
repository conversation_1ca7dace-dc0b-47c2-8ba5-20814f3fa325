package oss_util

import (
	"context"
	"crypto"
	"crypto/hmac"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"hash"
	"io"
	"net/http"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/config"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
	"github.com/aliyun/credentials-go/credentials"
	"github.com/gin-gonic/gin"
)

// 定义全局变量
var (
	region     string
	bucketName string
	product    = "oss"
)

// PolicyToken 结构体用于存储生成的表单数据
type PolicyToken struct {
	FileID           string `json:"file_id"` // upload_file保存的文件ID
	Policy           string `json:"policy"`
	SecurityToken    string `json:"security_token"`
	SignatureVersion string `json:"x_oss_signature_version"`
	Credential       string `json:"x_oss_credential"`
	Date             string `json:"x_oss_date"`
	Signature        string `json:"signature"`
	Host             string `json:"host"`
	Dir              string `json:"dir"`
	Callback         string `json:"callback"`
}

type CallbackParam struct {
	CallbackUrl      string `json:"callbackUrl"`
	CallbackBody     string `json:"callbackBody"`
	CallbackBodyType string `json:"callbackBodyType"`
}

func GetSecurityToken() (*dao.SecurityToken, error) {
	securityToken, err := dao.GetSecurityToken()
	if securityToken != nil {
		return securityToken, nil
	}
	conf := new(credentials.Config).
		SetType(config.OssSetting.Type).
		SetAccessKeyId(config.OssSetting.AccessKeyId).
		SetAccessKeySecret(config.OssSetting.AccessKeySecret).
		SetRoleArn(config.OssSetting.RoleArn).
		SetRoleSessionName(config.OssSetting.RoleSessionName).
		SetPolicy(config.OssSetting.Policy).
		SetRoleSessionExpiration(config.OssSetting.RoleSessionExpiration) //1小时

	// 根据配置创建凭证提供器
	provider, err := credentials.NewCredential(conf)
	if err != nil {
		return nil, err
	}

	// 从凭证提供器获取凭证
	cred, err := provider.GetCredential()
	if err != nil {
		return nil, err
	}
	securityToken = &dao.SecurityToken{
		AccessKeyId:     utils.GetValueByPointer(cred.AccessKeyId),
		AccessKeySecret: utils.GetValueByPointer(cred.AccessKeySecret),
		Token:           utils.GetValueByPointer(cred.SecurityToken),
	}
	_ = dao.SetSecurityToken(securityToken, 50*time.Minute)
	return securityToken, nil
}

// GetPolicyToken 函数生成 OSS 上传所需的签名和凭证
func GetPolicyToken(ctx context.Context) (*PolicyToken, error) {
	fileID := utils.GetUUID()
	err := models.CreateFile(ctx, &models.UploadedFile{
		FileID: fileID,
	})
	if err != nil {
		return nil, err
	}

	// 设置bucket所处地域
	region = config.OssSetting.Region
	// 设置bucket名称
	bucketName = config.OssSetting.BucketName
	// 设置 OSS 上传地址
	host := fmt.Sprintf("https://%s.oss-%s.aliyuncs.com", bucketName, region)
	// 设置上传目录
	dir := config.OssSetting.Dir
	// callbackUrl为 上传回调服务器的URL，请将下面的IP和Port配置为您自己的真实信息。
	callbackUrl := config.OssSetting.CallbackUrl

	securityToken, err := GetSecurityToken()
	if err != nil {
		return nil, err
	}

	// 构建policy
	utcTime := time.Now().UTC()
	date := utcTime.Format("20060102")
	expiration := utcTime.Add(1 * time.Hour)
	policyMap := map[string]any{
		"expiration": expiration.Format("2006-01-02T15:04:05.000Z"),
		"conditions": []any{
			map[string]string{"bucket": bucketName},
			map[string]string{"x-oss-signature-version": "OSS4-HMAC-SHA256"},
			map[string]string{"x-oss-credential": fmt.Sprintf("%v/%v/%v/%v/aliyun_v4_request", securityToken.AccessKeyId, date, region, product)},
			map[string]string{"x-oss-date": utcTime.Format("20060102T150405Z")},
			map[string]string{"x-oss-security-token": securityToken.Token},
		},
	}

	// 将policy转换为 JSON 格式
	policy, err := json.Marshal(policyMap)
	if err != nil {
		return nil, err
	}

	// 构造待签名字符串（StringToSign）
	stringToSign := base64.StdEncoding.EncodeToString(policy)

	hmacHash := func() hash.Hash { return sha256.New() }
	// 构建signing key
	signingKey := "aliyun_v4" + securityToken.AccessKeySecret
	h1 := hmac.New(hmacHash, []byte(signingKey))
	_, _ = io.WriteString(h1, date)
	h1Key := h1.Sum(nil)

	h2 := hmac.New(hmacHash, h1Key)
	_, _ = io.WriteString(h2, region)
	h2Key := h2.Sum(nil)

	h3 := hmac.New(hmacHash, h2Key)
	_, _ = io.WriteString(h3, product)
	h3Key := h3.Sum(nil)

	h4 := hmac.New(hmacHash, h3Key)
	_, _ = io.WriteString(h4, "aliyun_v4_request")
	h4Key := h4.Sum(nil)

	// 生成签名
	h := hmac.New(hmacHash, h4Key)
	_, _ = io.WriteString(h, stringToSign)
	signature := hex.EncodeToString(h.Sum(nil))

	var callbackParam CallbackParam
	callbackParam.CallbackUrl = callbackUrl
	callbackParam.CallbackBody = "bucket=${bucket}&object=${object}&filename=${object}&size=${size}&mimeType=${mimeType}&file_id=${x:file_id}"
	callbackParam.CallbackBodyType = "application/x-www-form-urlencoded"
	callbackStr, err := json.Marshal(callbackParam)
	if err != nil {
		return nil, err
	}
	callbackBase64 := base64.StdEncoding.EncodeToString(callbackStr)
	// 构建返回给前端的表单
	policyToken := PolicyToken{
		FileID:           fileID,
		Policy:           stringToSign,
		SecurityToken:    securityToken.Token,
		SignatureVersion: config.OssSetting.SignatureVersion,
		Credential:       fmt.Sprintf("%v/%v/%v/%v/aliyun_v4_request", securityToken.AccessKeyId, date, region, product),
		Date:             utcTime.UTC().Format("20060102T150405Z"),
		Signature:        signature,
		Host:             host,           // 返回 OSS 上传地址
		Dir:              dir,            // 返回上传目录
		Callback:         callbackBase64, // 返回上传回调参数
	}

	return &policyToken, nil
}

func VerifyOssCallback(c *gin.Context) bool {
	// 获取并解码公钥地址
	pubKeyUrlBase64 := c.GetHeader("x-oss-pub-key-url")
	pubKeyUrl, err := base64.StdEncoding.DecodeString(pubKeyUrlBase64)
	if err != nil {
		return false
	}

	// 获取阿里 OSS 公钥
	resp, err := http.Get(string(pubKeyUrl))
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	pubKeyData, _ := io.ReadAll(resp.Body)

	block, _ := pem.Decode(pubKeyData)
	parsedKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return false
	}
	rsaPubKey := parsedKey.(*rsa.PublicKey)

	// 构建待验证签名串
	authStr := c.Request.RequestURI + "\n" + c.Request.PostForm.Encode()

	// 获取签名
	signatureBase64 := c.GetHeader("authorization")
	sigBytes, _ := base64.StdEncoding.DecodeString(signatureBase64)

	// 验证签名
	hasher := sha1.New()
	hasher.Write([]byte(authStr))
	hashed := hasher.Sum(nil)

	return rsa.VerifyPKCS1v15(rsaPubKey, crypto.SHA1, hashed, sigBytes) == nil
}

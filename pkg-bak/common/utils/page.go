package utils

import "math"

// Pagination 分页参数结构体
type Pagination struct {
	Page     int // 当前页码（从1开始）
	PageSize int // 每页数量
}

// PageResult 分页结果结构体
type PageResult struct {
	Total    int64 `json:"total"`    // 总记录数
	Page     int   `json:"page"`     // 当前页码
	PageSize int   `json:"pageSize"` // 每页数量
	Pages    int   `json:"pages"`    // 总页数
}

// SafePagination 安全分页参数处理
func SafePagination(page, pageSize int, maxPageSize int) Pagination {
	// 处理页码
	if page < 1 {
		page = 1
	}

	// 处理每页数量
	switch {
	case pageSize < 1:
		pageSize = 10
	case pageSize > maxPageSize:
		pageSize = maxPageSize
	}

	return Pagination{
		Page:     page,
		PageSize: pageSize,
	}
}

// OffsetLimit 转换分页参数
func (p Pagination) OffsetLimit() (offset, limit int) {
	offset = (p.Page - 1) * p.PageSize
	limit = p.PageSize

	// 确保最小偏移量
	if offset < 0 {
		offset = 0
	}
	return
}

// CalcTotalPages 计算总页数
func CalcTotalPages(total int64, pageSize int) int {
	if pageSize == 0 {
		return 0
	}
	return int(math.Ceil(float64(total) / float64(pageSize)))
}

// BuildPageResult 构建完整分页结果
func BuildPageResult(total int64, p Pagination) PageResult {
	return PageResult{
		Total:    total,
		Page:     p.Page,
		PageSize: p.PageSize,
		Pages:    CalcTotalPages(total, p.PageSize),
	}
}

package utils

// GetPointer 返回传入变量的指针
func GetPointer[T any](input T) *T {
	return &input
}

// GetValueByPointer .
func GetValueByPointer[T any](input *T) T {
	if input == nil {
		return *new(T)
	}
	return *input
}

// GetValueFromPointer 返回传入变量指针的值
func GetValueFromPointer[T any](input *T, defaultValue T) T {
	if input == nil {
		return defaultValue
	}
	return *input
}

// OffsetLimitToPage converts offset and limit to page and size
func OffsetLimitToPage(offset, limit int) (page, size int) {
	if offset < 0 {
		offset = 0
	}
	if limit <= 0 {
		limit = 10 // 默认每页显示10条数据
	}

	page = offset/limit + 1
	size = limit

	return
}

package k8s

import (
	"context"
	"fmt"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"strings"
)

type BuildRequest struct {
	BaseImage       string // 基础镜像 ubuntu:20.04
	ImageTag        string // 构建后的镜像 tag boss-8df08e
	ZipOSSPath      string // 上传到 OSS 的 zip 包路径 oss://xxx.zip
	ExecFile        string // 程序启动文件名
	AccessKeyID     string
	AccessKeySecret string
}

func StartImageBuild(req BuildRequest) error {
	job := BuildKanikoJob(req)
	client, err := GetK8sClient() // 或 kubeconfig 外部连接
	if err != nil {
		return err
	}

	_, err = client.BatchV1().Jobs("game-build").Create(context.TODO(), job, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建 Kaniko Job 失败: %w", err)
	}
	return nil
}

func GetImageDestination(imageTag string) string {
	return fmt.Sprintf("hb.mg.xyz/multiverse/%s", imageTag)
}

func BuildKanikoJob(req BuildRequest) *batchv1.Job {
	jobName := fmt.Sprintf("build-job-%s", strings.ReplaceAll(req.ImageTag, ":", "-"))
	volumeName := "build-context"

	initContainerScript := fmt.Sprintf(`
set -euxo pipefail
apk add --no-cache curl unzip

echo "Downloading zip from: %s"
curl -L -o /data/game.zip "%s"
unzip /data/game.zip -d /data
mv /data/game-server/* /data
rm -rf /data/game.zip /data/game-server

cat <<EOF > /data/Dockerfile
# 构建阶段
FROM golang:1.23.0 AS builder
WORKDIR /app
COPY . .
RUN go mod download
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -mod=readonly -ldflags="-s -w" -o %s .

# 运行阶段
FROM %s
WORKDIR /app
COPY --from=builder /app/%s .
EXPOSE 7777
ENTRYPOINT ["./%s"]
EOF
`, req.ZipOSSPath, req.ZipOSSPath, req.ExecFile, req.BaseImage, req.ExecFile, req.ExecFile)

	return &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: "game-build",
		},
		Spec: batchv1.JobSpec{
			Template: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					RestartPolicy: corev1.RestartPolicyNever,
					ImagePullSecrets: []corev1.LocalObjectReference{
						{Name: "harbor-auth"},
					},
					Volumes: []corev1.Volume{
						{
							Name: volumeName,
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
						{
							Name: "kaniko-secret",
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									SecretName: "kaniko-secret",
									Items: []corev1.KeyToPath{
										{
											Key:  ".dockerconfigjson",
											Path: "config.json", // 必须命名为 config.json
										},
									},
								},
							},
						},
					},
					InitContainers: []corev1.Container{
						{
							Name:    "init-unzip",
							Image:   "alpine:3.18",
							Command: []string{"sh", "-c", initContainerScript},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      volumeName,
									MountPath: "/data",
								},
							},
						},
					},
					Containers: []corev1.Container{
						{
							Name:  "kaniko",
							Image: "gcr.io/kaniko-project/executor:latest", // 要梯子
							//Image: "registry.cn-hangzhou.aliyuncs.com/acs/kaniko-executor:latest",
							Args: []string{
								"--dockerfile=/workspace/Dockerfile",
								"--context=/workspace",
								fmt.Sprintf("--destination=%s", GetImageDestination(req.ImageTag)),
								"--insecure",
								"--skip-tls-verify",
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      volumeName,
									MountPath: "/workspace", // ✅ 上下文挂载目录
								},
								{
									Name:      "kaniko-secret",
									MountPath: "/kaniko/.docker", // ✅ Secret 挂载点
								},
							},
							Env: []corev1.EnvVar{
								{
									Name:  "DOCKER_CONFIG",
									Value: "/kaniko/.docker/", // ✅ 必须指向上面挂载目录
								},
							},
						},
					},
				},
			},
		},
	}
}

// 文件：pkg/k8s/client.go

package k8s

import (
	"context"
	"fmt"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
	"os"
	"path/filepath"
	"sync"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

var (
	once         sync.Once
	clientset    *kubernetes.Clientset
	clientsetErr error
)

func GetRestConfig() (*rest.Config, error) {
	var config *rest.Config

	// 优先级 1: 集群内配置（Pod 中运行）
	if inClusterConfig, err := rest.InClusterConfig(); err == nil {
		config = inClusterConfig
		klog.Info("Using in-cluster Kubernetes configuration")
	} else {
		// 优先级 2: 本地 Kubeconfig 文件（开发环境）
		kubeconfigPath := getKubeconfigPath()
		config, err = clientcmd.BuildConfigFromFlags("", kubeconfigPath)
		if err != nil {
			clientsetErr = err
			return nil, err
		}
		klog.Infof("Using kubeconfig from: %s", kubeconfigPath)
	}
	return config, nil
}

// GetK8sClient 获取全局唯一的 Kubernetes Clientset
func GetK8sClient() (*kubernetes.Clientset, error) {
	once.Do(func() {
		var config *rest.Config

		// 优先级 1: 集群内配置（Pod 中运行）
		if inClusterConfig, err := rest.InClusterConfig(); err == nil {
			config = inClusterConfig
			klog.Info("Using in-cluster Kubernetes configuration")
		} else {
			// 优先级 2: 本地 Kubeconfig 文件（开发环境）
			kubeconfigPath := getKubeconfigPath()
			config, err = clientcmd.BuildConfigFromFlags("", kubeconfigPath)
			if err != nil {
				clientsetErr = err
				return
			}
			klog.Infof("Using kubeconfig from: %s", kubeconfigPath)
		}

		// 性能调优（可选）
		config.QPS = 100   // 每秒最大查询数
		config.Burst = 100 // 突发请求数

		// 创建 Clientset
		clientset, clientsetErr = kubernetes.NewForConfig(config)
	})

	return clientset, clientsetErr
}

// getKubeconfigPath 获取 kubeconfig 文件路径
func getKubeconfigPath() string {
	// 优先级 1: 环境变量 KUBECONFIG
	if path := os.Getenv("KUBECONFIG"); path != "" {
		return path
	}
	// 优先级 2: 默认路径 ~/.kube/config
	homeDir, _ := os.UserHomeDir()
	return filepath.Join(homeDir, ".kube", "config")
}

func ListPods(ctx context.Context, namespace string) {
	clientset, err := GetK8sClient()
	if err != nil {
		klog.Fatalf("Error getting Kubernetes client: %v", err)
	}
	pods, err := clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		klog.Fatalf("Error listing pods: %v", err)
	}
	for _, pod := range pods.Items {
		fmt.Printf("Pod Name: %s\n", pod.Name)
	}
}

package routers

import (
	"time"

	_ "git.mg.xyz/paas-group/ros-group/multiverse/docs" // 导入生成的docs
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/controllers/api"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/controllers/api/sdk"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/middleware/jwt"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/middleware"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// InitRouter initialize routing information
func InitRouter() *gin.Engine {
	r := gin.New()

	// 配置CORS中间件
	//r.Use(cors.New(cors.Config{
	//	AllowOrigins:     []string{"*"}, // 允许的前端源
	//	AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
	//	AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
	//	ExposeHeaders:    []string{"Content-Length"},
	//	AllowCredentials: true,
	//	MaxAge:           12 * time.Hour,
	//}))

	// 自定义允许所有来源的配置
	r.Use(cors.New(cors.Config{
		AllowAllOrigins:  true, // 允许所有来源
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		ExposeHeaders:    []string{"*"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	pprof.Register(r) // 默认路由前缀为/debug/pprof

	// 注册Swagger路由
	r.GET("/swagger/*any",
		gin.BasicAuth(gin.Accounts{"admin": "secret"}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.PrometheusMiddleware())
	r.GET("/metrics",
		gin.BasicAuth(gin.Accounts{"admin": "secret"}),
		gin.WrapH(promhttp.Handler()),
	)

	r.GET("/ping", api.Ping)

	// 项目配置
	r.GET("/multiverse/os/:os_id", api.GetOsByID)
	r.GET("/multiverse/os", api.GetOsList)
	r.POST("/multiverse/os", api.CreateOs)

	r.GET("/multiverse/projects/:project_id", api.GetProject)
	r.GET("/multiverse/projects", api.GetProjectList)
	r.PUT("/multiverse/projects/:project_id", api.UpdProject)
	r.PUT("/multiverse/projects/:project_id/room_manager/enable", api.EnableRoomManager)
	r.PUT("/multiverse/projects/:project_id/room_manager/disable", api.DisableRoomManager)
	r.PUT("/multiverse/projects/:project_id/matchmaking/enable", api.EnableMatchmaking)
	r.PUT("/multiverse/projects/:project_id/matchmaking/disable", api.DisableMatchmaking)
	r.POST("/multiverse/projects", api.CreateProject)               // 创建项目
	r.DELETE("/multiverse/projects/:project_id", api.DeleteProject) // 删除项目

	// 镜像
	r.GET("/multiverse/oss/signature", api.GetUploadSignature)
	r.POST("/multiverse/oss/callback", api.OssCallback)
	r.GET("/multiverse/apps/:app_id/images/:image_id", api.GetImage)
	r.GET("/multiverse/apps/:app_id/images", api.GetImageList)
	r.POST("/multiverse/apps/:app_id/images", api.CreateImage)
	r.DELETE("/multiverse/apps/:app_id/images/:image_id", api.DeleteImage)

	// 启动配置
	r.GET("/multiverse/profiles", api.GetProfileList)
	r.GET("/multiverse/apps/:app_id/profiles/:profile_id", api.GetProfile)
	r.GET("/multiverse/profiles/:profile_id/revisions", api.GetProfileRevisionList)
	r.GET("/multiverse/profiles/revisions/:revision_id", api.GetProfileRevision)
	r.POST("/multiverse/profiles", api.CreateProfile)
	r.POST("/multiverse/profiles/:profile_id/revisions", api.CreateProfileRevision)
	r.PUT("/multiverse/profiles/:profile_id", api.UpdProfile)
	r.POST("/multiverse/profiles/:profile_id/release-resource", api.ReleaseResource) // 释放资源
	r.PUT("/multiverse/profiles/revisions/:revision_id", api.UpdProfileRevision)
	r.DELETE("/multiverse/profiles/:profile_id", api.DeleteProfile)
	r.DELETE("/multiverse/profiles/revisions/:revision_id", api.DeleteProfileRevision)
	r.POST("/multiverse/profiles/revisions/:revision_id/test_server", api.ProfileRevisionTestServer)                // 测试配置
	r.GET("/multiverse/profiles/revisions/test_server/:revision_id/status", api.GetProfileRevisionTestServerStatus) // 查询测试测试状态（轮询）
	r.POST("/multiverse/profiles/revisions/test_server/:revision_id/cancel", api.ProfileRevisionCancelTestServer)   // 取消测试配置
	r.POST("/multiverse/profiles/revisions/:revision_id/release", api.ProfileRevisionRelease)                       // 应用配置

	// 匹配
	r.GET("/multiverse/matches", api.GetMatchList)
	r.POST("/multiverse/matches", api.CreateMatch)
	r.GET("/multiverse/matches/:id", api.GetMatch)
	r.DELETE("/multiverse/matches/:id", api.DeleteMatch)
	r.PUT("/multiverse/matches/:id", api.UpdMatch)
	r.POST("/multiverse/matches/:id/test_players", api.CreateTestPlayers)
	r.GET("/multiverse/matches/rules", api.GetRules)              // 匹配参考语句-rules
	r.GET("/multiverse/matches/expansions", api.GetExpansions)    // 匹配参考语句-expansions
	r.GET("/multiverse/matches/templates", api.GetMatchTemplates) // 匹配分类和默认模板
	r.POST("/multiverse/matches/:id/test", api.MatchTest)         // 匹配模拟（测试）

	// 地域
	r.GET("/multiverse/regions", api.GetRegions)
	r.GET("/multiverse/regions/:region_id", api.GetRegion)
	r.PUT("/multiverse/regions/:region_id", api.UpdRegion)
	r.PUT("/multiverse/regions/:region_id/stop", api.StopRegion)
	r.PUT("/multiverse/regions/:region_id/start", api.StartRegion)
	r.PUT("/multiverse/regions/:region_id/release", api.ReleaseRegion) //释放资源
	r.POST("/multiverse/regions/:region_id/allocate", api.Allocate)    //分配服务器

	// 房间
	r.GET("/multiverse/rooms", api.ListRooms)
	r.GET("/multiverse/rooms/:room_id", api.GetRoom)
	r.POST("/multiverse/rooms", api.CreateRoom)

	//服务器
	r.GET("/multiverse/app/:app_id/allocations", api.ListAllocations)
	r.POST("/multiverse/allocations", api.CreateAllocation)
	r.GET("/multiverse/allocations/:allocation_id", api.GetAllocation)
	r.DELETE("/multiverse/allocations/:allocation_id", api.DeleteAllocation)

	//SDK 房间管理
	r.GET("/multiverse/sdk/rooms/server_info_async", sdk.GetServerInfoAsync)     // 获取房间ID 查找可分配服务器
	r.GET("/multiverse/sdk/rooms", sdk.GetRooms)                                 // 查询房间列表
	r.POST("/multiverse/sdk/rooms", sdk.CreateRoom)                              // 创建房间
	r.GET("/multiverse/sdk/rooms/:room_id", sdk.GetRoom)                         // 查询房间信息
	r.PUT("/multiverse/sdk/rooms/:room_id", sdk.UpdateRoom)                      // 更新房间信息（推荐服务端调用）
	r.POST("/multiverse/sdk/rooms/:room_id/confirm-join", sdk.ConfirmJoinRoom)   // 确认房间加入（推荐服务端调用）
	r.POST("/multiverse/sdk/rooms/:room_id/join", sdk.JoinRoom)                  // 加入房间（推荐客户端调用）
	r.POST("/multiverse/sdk/rooms/:room_id/leave", sdk.LeaveRoom)                // 离开房间
	r.POST("/multiverse/sdk/rooms/:room_id/dynamic-info", sdk.ReportDynamicInfo) // 上报房间信息（该功能需要联系后台开通）
	r.PUT("/multiverse/sdk/rooms/:room_id/status/:status", sdk.UpdateRoomStatus) // 更新房间状态
	r.GET("/multiverse/sdk/rooms/available", sdk.GetAvailableRooms)              // 获取可快速加入的房间列表（需要和上报房间实时人数功能结合使用）

	//SDK 服务器管理
	r.POST("/multiverse/sdk/game_server/ready/:allocation_id", sdk.Ready)             // 标记 game server 为 ready 状态
	r.GET("/multiverse/sdk/game_server/allocations/:allocation_id/envs", sdk.GetEnvs) // 获取启动配置里配置的和Allocation 接口传过来的环境变量
	r.GET("/multiverse/sdk/game_server/server_info", sdk.GetServerInfo)               // 获取server 基础配置信息，如 ip, port, allocationId等信息
	r.GET("/multiverse/sdk/game_server/expired_at", sdk.GetExpiredAt)                 // 获取当前 game server 到期时间
	r.POST("/multiverse/sdk/game_server/shutdown", sdk.Shutdown)                      // 关闭 game server
	r.POST("/multiverse/sdk/game_server/allocate", sdk.Allocate)                      // 标记 game server 为 allocated 状态, 舰队模式可用该方法来分配 game server
	r.POST("/multiverse/sdk/game_server/watch", sdk.Watch)                            // watch game server status, 舰队模式可用该方法监听 game server到 allocated 状态
	r.POST("/multiverse/sdk/game_server/:app_id/ping-services", sdk.PingServers)      // 获取游戏下启用的地域的端口信息(ping测试延迟)

	//SDK 匹配管理
	r.POST("/multiverse/sdk/matches/tickets", api.CreateTicket)                           // 创建ticket
	r.POST("/multiverse/sdk/matches/tickets/:ticket_id/status", api.GetTicketStatus)      // 轮询获取ticket状态
	r.DELETE("/multiverse/sdk/matches/tickets/:ticket_id", api.DeleteTicket)              // 删除ticket
	r.GET("/multiverse/sdk/matches/:match_id", api.GetMatchData)                          // 获取匹配数据
	r.GET("/multiverse/sdk/matches/:match_id/watch", api.WatchMatch)                      // 监听匹配数据
	r.POST("/multiverse/sdk/matches/:match_id/start_backfill", api.StartBackFill)         // 开启BackFill
	r.POST("/multiverse/sdk/matches/:match_id/stop_backfill", api.StopBackFill)           // 关闭BackFill
	r.GET("/multiverse/sdk/matches/:match_id/backfill/players", api.ListBackFillPlayers)  // 查询backfill获取到的新玩家
	r.GET("/multiverse/sdk/matches/:match_id/watch_backfill", api.WatchBackFill)          // 监听backfill
	r.GET("/multiverse/sdk/matches/:match_id/is_backfill_started", api.IsBackFillStarted) // 当前匹配是否开启backfill

	r.POST("/auth", api.GetAuth)

	// 获取一个游戏下所有启用的游戏地域的端口信息，用于ping测试延迟。
	//r.Use(authorization.AuthMiddleware()).GET("/ping-servers", func(c *gin.Context) {
	//	c.JSON(200, "pong")
	//})
	apiV1 := r.Group("/api/v1")
	apiV1.Use(jwt.JWT())
	{
	}

	return r
}

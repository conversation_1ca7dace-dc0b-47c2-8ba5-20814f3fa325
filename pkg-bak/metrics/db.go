package metrics

import (
	"time"

	"gorm.io/gorm"
)

// WithMetrics 包装数据库操作以收集指标
func WithMetrics(operation, table string, fn func() error) error {
	start := time.Now()
	err := fn()
	duration := time.Since(start).Seconds()

	// 记录操作总数
	DatabaseOperationsTotal.WithLabelValues(operation, table).Inc()

	// 记录操作时间
	DatabaseOperationDuration.WithLabelValues(operation, table).Observe(duration)

	return err
}

// DBWithMetrics 返回一个带有指标收集的数据库实例
func DBWithMetrics(db *gorm.DB) *gorm.DB {
	// 创建回调
	db.Callback().Create().Before("gorm:create").Register("metrics:create", func(db *gorm.DB) {
		WithMetrics("create", db.Statement.Table, func() error {
			return nil
		})
	})

	db.Callback().Update().Before("gorm:update").Register("metrics:update", func(db *gorm.DB) {
		WithMetrics("update", db.Statement.Table, func() error {
			return nil
		})
	})

	db.Callback().Delete().Before("gorm:delete").Register("metrics:delete", func(db *gorm.DB) {
		WithMetrics("delete", db.Statement.Table, func() error {
			return nil
		})
	})

	db.Callback().Query().Before("gorm:query").Register("metrics:query", func(db *gorm.DB) {
		WithMetrics("query", db.Statement.Table, func() error {
			return nil
		})
	})

	return db
}

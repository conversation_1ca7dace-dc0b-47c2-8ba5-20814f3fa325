package app

import (
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"github.com/gin-gonic/gin"
)

type Gin struct {
	C *gin.Context
}

type Response struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// Response setting gin.JSON
func (g *Gin) Response(httpCode, errCode int, data interface{}) {
	if data == nil {
		data = gin.H{}
	}
	g.C.JSON(httpCode, Response{
		Code: errCode,
		Msg:  conf.GetMsg(errCode),
		Data: data,
	})
	return
}

func (g *Gin) Error(errCode int, errMsg string, data interface{}) {
	if data == nil {
		data = gin.H{}
	}
	g.C.JSON(http.StatusOK, Response{
		Code: errCode,
		Msg:  errMsg,
		Data: data,
	})
	return
}

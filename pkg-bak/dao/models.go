package dao

import (
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"

	"time"
)

var Db *gorm.DB

type Model struct {
	ID        uint64    `gorm:"primary_key" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt time.Time `json:"deleted_at"`
	Deleted   int8      `json:"deleted"`
}

// OrmSetup initializes the database instance
func OrmSetup() {
	// var err error
	// Db, err = gorm.Open(config.DatabaseSetting.Type, fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8&parseTime=True&loc=Local",
	// 	config.DatabaseSetting.User,
	// 	config.DatabaseSetting.Password,
	// 	config.DatabaseSetting.Host,
	// 	config.DatabaseSetting.Name))

	// if err != nil {
	// 	log.Fatalf("dao.OrmSetup err: %v", err)
	// }

	// gorm.DefaultTableNameHandler = func(db *gorm.DB, defaultTableName string) string {
	// 	return config.DatabaseSetting.TablePrefix + defaultTableName
	// }

	// Db.SingularTable(true)
	// Db.DB().SetMaxIdleConns(10)
	// Db.DB().SetMaxOpenConns(100)

	// if config.DatabaseSetting.Debug {
	// 	Db = Db.Debug()
	// }
}

// CloseDB closes database connection (unnecessary)
func CloseDB() {
	defer Db.Close()
}

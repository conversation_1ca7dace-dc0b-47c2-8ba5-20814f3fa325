package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/goccy/go-json"

	"github.com/redis/go-redis/v9"
)

var RedisConn *redis.Client

// RedisSetup Initialize the Redis instance
func RedisSetup() {
	// RedisConn = redis.NewClient(&redis.Options{
	// 	//连接信息
	// 	Network:  "tcp",                        //网络类型，tcp or unix，默认tcp
	// 	Addr:     config.RedisSetting.Host,     //主机名+冒号+端口，默认localhost:6379
	// 	Password: config.RedisSetting.Password, //密码
	// 	DB:       0,                            // redis数据库index

	// 	//连接池容量及闲置连接数量
	// 	PoolSize:     16, // 连接池最大socket连接数，默认为4倍CPU数， 4 * runtime.NumCPU
	// 	MinIdleConns: 10, //在启动阶段创建指定数量的Idle连接，并长期维持idle状态的连接数不少于指定数量；。
	// 	//MaxIdleConns: 100,

	// 	//超时
	// 	DialTimeout:  5 * time.Second, //连接建立超时时间，默认5秒。
	// 	ReadTimeout:  3 * time.Second, //读超时，默认3秒， -1表示取消读超时
	// 	WriteTimeout: 3 * time.Second, //写超时，默认等于读超时
	// 	PoolTimeout:  4 * time.Second, //当所有连接都处在繁忙状态时，客户端等待可用连接的最大等待时长，默认为读超时+1秒。

	// 	//闲置连接检查包括
	// 	MaxIdleConns:    100,
	// 	ConnMaxIdleTime: 5 * time.Minute, //闲置超时，默认5分钟，-1表示取消闲置超时检查
	// 	ConnMaxLifetime: 0 * time.Second, //连接存活时长，从创建开始计时，超过指定时长则关闭连接，默认为0，即不关闭存活时长较长的连接

	// 	//命令执行失败时的重试策略
	// 	MaxRetries:      0,                      // 命令执行失败时，最多重试多少次，默认为0即不重试
	// 	MinRetryBackoff: 8 * time.Millisecond,   //每次计算重试间隔时间的下限，默认8毫秒，-1表示取消间隔
	// 	MaxRetryBackoff: 512 * time.Millisecond, //每次计算重试间隔时间的上限，默认512毫秒，-1表示取消间隔

	// 	//钩子函数
	// 	OnConnect: func(ctx context.Context, cn *redis.Conn) error { //仅当客户端执行命令时需要从连接池获取连接时，如果连接池需要新建连接时则会调用此钩子函数
	// 		fmt.Printf("conn=%v\n", cn)
	// 		return nil
	// 	},
	// })
}

func Set(key string, data interface{}, expiration time.Duration) error {
	err := RedisConn.Set(context.Background(), key, data, expiration).Err()
	if err != nil {
		return err
	}
	return nil
}

func Get(key string) string {
	cmd := RedisConn.Get(context.Background(), key)
	if cmd.Err() != nil {
		return ""
	}
	return cmd.String()
}

type SecurityToken struct {
	Token           string `json:"token"`
	AccessKeyId     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
}

const OssSecurityTokenKey = "oss:security_token"

func GetSecurityToken() (*SecurityToken, error) {
	tokenJson, err := RedisConn.Get(context.Background(), OssSecurityTokenKey).Result()
	if err != nil {
		return nil, err
	}
	if tokenJson == "" {
		return nil, fmt.Errorf("GetSecurityToken failed")
	}
	token := SecurityToken{}
	err = json.Unmarshal([]byte(tokenJson), &token)
	if err != nil {
		return nil, err
	}
	return &token, nil
}

func SetSecurityToken(token *SecurityToken, ttl time.Duration) error {
	tokenJson, err := json.Marshal(token)
	if err != nil {
		return err
	}
	err = RedisConn.Set(context.Background(), OssSecurityTokenKey, tokenJson, ttl).Err()
	if err != nil {
		return err
	}
	return nil
}

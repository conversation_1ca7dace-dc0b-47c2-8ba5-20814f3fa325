package dao

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"time"
)

// AcquireLock 获取锁
func AcquireLock(ctx context.Context, lockKey string, ttl time.Duration) (string, error) {
	lockValue := uuid.NewString() // 用于解锁验证
	ok, err := RedisConn.SetNX(ctx, lockKey, lockValue, ttl).Result()
	if err != nil {
		return "", err
	}
	if ok {
		return lockValue, nil // 加锁成功，返回锁的唯一值
	}
	return "", fmt.Errorf("加锁失败")
}

// ReleaseLock 释放锁
func ReleaseLock(ctx context.Context, lockKey, lockValue string) bool {
	// 使用 Lua 脚本保证原子性：只有当值匹配时才删除
	luaScript := `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
            return redis.call("DEL", KEYS[1])
        else
            return 0
        end
    `
	res, err := RedisConn.Eval(ctx, luaScript, []string{lockKey}, lockValue).Int()
	if err != nil {
		return false
	}
	return res == 1
}

package logger

import (
	"os"
	"sync"

	"go.uber.org/zap/zapcore"

	"go.uber.org/zap"
)

var (
	// Log 是全局日志实例
	Log      *zap.Logger
	initOnce sync.Once
)

// InitLogger 初始化日志配置
func InitLogger() {
	initOnce.Do(func() {
		// 创建自定义的编码器配置
		encoderConfig := zapcore.EncoderConfig{
			TimeKey:        "time",
			LevelKey:       "level",
			NameKey:        "logger",
			Caller<PERSON>ey:      "caller",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		}

		// 设置日志输出
		core := zapcore.NewCore(
			zapcore.NewJSONEncoder(encoderConfig),
			zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout)),
			zapcore.InfoLevel,
		)

		// 创建Logger
		Log = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	})
}

// Debug 输出Debug级别日志
func Debug(msg string, fields ...zap.Field) {
	Log.Debug(msg, fields...)
}

// Info 输出Info级别日志
func Info(msg string, fields ...zap.Field) {
	Log.Info(msg, fields...)
}

// Warn 输出Warn级别日志
func Warn(msg string, fields ...zap.Field) {
	Log.Warn(msg, fields...)
}

// Error 输出Error级别日志
func Error(msg string, fields ...zap.Field) {
	Log.Error(msg, fields...)
}

// Fatal 输出Fatal级别日志
func Fatal(msg string, fields ...zap.Field) {
	Log.Fatal(msg, fields...)
}

// With 创建子Logger
func With(fields ...zap.Field) *zap.Logger {
	return Log.With(fields...)
}

// Sync 同步日志缓冲
func Sync() error {
	return Log.Sync()
}

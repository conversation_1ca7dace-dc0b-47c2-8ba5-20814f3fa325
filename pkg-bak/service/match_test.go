package service

import (
	"fmt"
	"testing"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
)

func TestGetTemplateByLevel(t *testing.T) {
	parents, err := models.GetTemplateByLevel(1, 0)
	fmt.Println(parents, err)

	for _, parent := range parents {
		fmt.Println(parent.ID)

		children, err := models.GetTemplateByLevel(2, parent.ID)
		if err != nil {
			return
		}
		fmt.Println(children)
	}
}

func TestGetTemplates(t *testing.T) {
	templates, err := GetTemplates()

	for _, template := range templates {
		fmt.Println(template.SubCategories)
	}
	fmt.Println(templates, err)
}

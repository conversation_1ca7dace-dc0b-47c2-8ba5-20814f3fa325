package service

import (
	"context"
	"fmt"
	"path"
	"strconv"
	"strings"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils/oss_util"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/k8s"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
	"github.com/openkruise/kruise-api/apps/v1beta1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/utils/pointer"
	"sigs.k8s.io/controller-runtime/pkg/client"

	gamev1alpha1 "github.com/openkruise/kruise-game/apis/v1alpha1"
	v1 "k8s.io/api/apps/v1"
	apiv1 "k8s.io/api/core/v1"
	corev1 "k8s.io/api/core/v1" // Pod、Volume、Container 等
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
)

type LaunchConfig struct {
	Image      string
	CPU        string
	Memory     string
	TimeoutSec int
	Command    []string
	TimeZone   string
	Port       int
	Mounts     []corev1.VolumeMount
}

func CreateGameServerSetV2(profileID string, revision *ProfileRevisionV2, allocation *Allocation) (*gamev1alpha1.GameServerSet, error) {
	// 1. 构建容器端口配置
	var ports []corev1.ContainerPort
	for _, port := range revision.GameServerPorts {
		if !utils.IsInStringArray(port.Protocol, []string{string(corev1.ProtocolTCP), string(corev1.ProtocolUDP)}) {
			return nil, fmt.Errorf("invalid port protocol: %s", port.Protocol)
		}

		containerPort := corev1.ContainerPort{
			Name:          port.Name,
			ContainerPort: int32(port.Port),
			Protocol:      corev1.Protocol(port.Protocol),
		}

		switch port.PortPolicy {
		case "FixedHostPort":
			containerPort.HostPort = int32(port.Port)
		case "DynamicHostPort":
			containerPort.HostPort = 0
		}
		ports = append(ports, containerPort)
	}

	// 2. 构建环境变量
	envVars := make([]corev1.EnvVar, 0, len(revision.EnvironmentVariables))
	for name, value := range revision.EnvironmentVariables {
		envVars = append(envVars, corev1.EnvVar{
			Name:  name,
			Value: value,
		})
	}

	// 3. 构建 ConfigMap 卷挂载
	volumes := make([]corev1.Volume, 0)
	volumeMounts := make([]corev1.VolumeMount, 0)

	for i, fileCfg := range revision.FileConfigs {
		configMapName := fmt.Sprintf("%s-config-%d", profileID, i)

		volumes = append(volumes, corev1.Volume{
			Name: configMapName,
			VolumeSource: corev1.VolumeSource{
				ConfigMap: &corev1.ConfigMapVolumeSource{
					LocalObjectReference: corev1.LocalObjectReference{
						Name: configMapName,
					},
					Items: []corev1.KeyToPath{
						{
							Key:  "content",
							Path: fileCfg.Filename,
						},
					},
				},
			},
		})

		// ❗避免挂载到根目录 . ，改为 /config 下
		mountPath := "/config" + fileCfg.MountPath
		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      configMapName,
			MountPath: path.Dir(mountPath),
			ReadOnly:  true,
		})
	}

	// 4. 构建资源限制
	resources := corev1.ResourceRequirements{
		Requests: corev1.ResourceList{},
		Limits:   corev1.ResourceList{},
	}

	if revision.CPURequest != "" {
		resources.Requests[corev1.ResourceCPU] = resource.MustParse(revision.CPURequest)
	}
	if revision.MemoryRequest != "" {
		resources.Requests[corev1.ResourceMemory] = resource.MustParse(revision.MemoryRequest)
	}
	if revision.CPULimit != "" {
		resources.Limits[corev1.ResourceCPU] = resource.MustParse(revision.CPULimit)
	}
	if revision.MemoryLimit != "" {
		resources.Limits[corev1.ResourceMemory] = resource.MustParse(revision.MemoryLimit)
	}

	// 5. 构建健康检查探针（建议添加）
	//livenessProbe := &corev1.Probe{
	//	ProbeHandler: corev1.ProbeHandler{
	//		TCPSocket: &corev1.TCPSocketAction{
	//			Port: intstr.FromInt32(int32(tcpPort)),
	//		},
	//	},
	//	InitialDelaySeconds: 30,
	//	PeriodSeconds:       10,
	//}
	//
	//readinessProbe := &corev1.Probe{
	//	ProbeHandler: corev1.ProbeHandler{
	//		TCPSocket: &corev1.TCPSocketAction{
	//			Port: intstr.FromInt32(int32(tcpPort)),
	//		},
	//	},
	//	InitialDelaySeconds: 10,
	//	PeriodSeconds:       5,
	//}

	// 6. 构建 Pod 模板
	readOnlyRootFilesystem := false
	podTemplate := corev1.PodTemplateSpec{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{
				"app":         "game-server",
				"profile-id":  profileID,
				"revision-id": revision.RevisionID,
			},
			Annotations: map[string]string{
				"game-start-duration": strconv.FormatInt(revision.GameStartDuration, 10),
			},
		},
		Spec: corev1.PodSpec{
			HostNetwork:   true,
			RestartPolicy: corev1.RestartPolicyAlways, // 明确指定重启策略
			Containers: []corev1.Container{
				{
					Name:            "game-server",
					Image:           revision.GameImage.ImageUrl,
					ImagePullPolicy: corev1.PullAlways,
					Command:         revision.GameServerEntryPoint,
					Ports:           ports,
					Env:             envVars,
					//VolumeMounts:    volumeMounts,
					Resources: resources,
					//LivenessProbe:   livenessProbe,
					//ReadinessProbe:  readinessProbe,
					SecurityContext: &corev1.SecurityContext{
						ReadOnlyRootFilesystem: &readOnlyRootFilesystem, // 默认关闭只读文件系统
					},
				},
			},
			ImagePullSecrets: []corev1.LocalObjectReference{
				{Name: "aliyun-cr-auth"},
			},
			Volumes: volumes,
		},
	}

	// 7. 构建 GameServerSet 对象
	namespace := "default"
	if revision.Namespace != "" {
		namespace = revision.Namespace
	}

	return &gamev1alpha1.GameServerSet{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "game.kruise.io/v1alpha1",
			Kind:       "GameServerSet",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%d", revision.GetGssTemplate(), allocation.ID),
			Namespace: namespace,
		},
		Spec: gamev1alpha1.GameServerSetSpec{
			Replicas: pointer.Int32(1),
			UpdateStrategy: gamev1alpha1.UpdateStrategy{
				Type: v1.RollingUpdateStatefulSetStrategyType,
				RollingUpdate: &gamev1alpha1.RollingUpdateStatefulSetStrategy{
					PodUpdatePolicy: v1beta1.InPlaceIfPossiblePodUpdateStrategyType,
					MaxUnavailable: &intstr.IntOrString{
						Type:   intstr.String,
						StrVal: "10%",
					},
				},
			},
			Network: &gamev1alpha1.Network{
				NetworkType: "Kubernetes-HostPort",
			},
			GameServerTemplate: gamev1alpha1.GameServerTemplate{
				PodTemplateSpec: podTemplate,
			},
		},
	}, nil
}

// CreateConfigMaps 创建关联的ConfigMap资源
func CreateConfigMaps(profileID string, revision *ProfileRevisionV2, namespace string) []*corev1.ConfigMap {
	configMaps := make([]*corev1.ConfigMap, 0, len(revision.FileConfigs))

	for i, fileCfg := range revision.FileConfigs {
		configMapName := fmt.Sprintf("%s-config-%d", profileID, i)

		cm := &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:      configMapName,
				Namespace: namespace,
				Labels: map[string]string{
					"app":         "game-server",
					"profile-id":  profileID,
					"revision-id": revision.RevisionID,
					"config-type": "game-file",
				},
			},
			Data: map[string]string{
				"content": fileCfg.Content,
			},
		}

		configMaps = append(configMaps, cm)
	}

	return configMaps
}

func CreateGameServerSetV1(profileID string) (*gamev1alpha1.GameServerSet, error) {
	// 1. 定义容器端口
	ports := []apiv1.ContainerPort{
		{Name: "tcpport", ContainerPort: 9000, HostPort: 9000, Protocol: apiv1.ProtocolTCP},
		{Name: "udpport", ContainerPort: 9001, HostPort: 9001, Protocol: apiv1.ProtocolUDP},
		{Name: "httpport", ContainerPort: 8080, HostPort: 8080, Protocol: apiv1.ProtocolTCP},
	}
	// 2. 定义 Pod 模板
	podTemplate := apiv1.PodTemplateSpec{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{"app": "game-simple-server"},
		},
		Spec: apiv1.PodSpec{
			HostNetwork: true,
			Containers: []apiv1.Container{
				{
					Name:            "server",
					Image:           "crpi-9ctxcivq4qir72h2.cn-guangzhou.personal.cr.aliyuncs.com/mgrc-ros/ros:gamesever1.0",
					ImagePullPolicy: apiv1.PullAlways,
					Ports:           ports,
				},
			},
			ImagePullSecrets: []apiv1.LocalObjectReference{
				{Name: "aliyun-cr-auth"},
			},
		},
	}
	// 3. 创建 GameServerSet 对象（已修正原地升级策略）
	return &gamev1alpha1.GameServerSet{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "game.kruise.io/v1alpha1",
			Kind:       "GameServerSet",
		},
		ObjectMeta: metav1.ObjectMeta{
			//Name:      "game-simple-server-" + profileID, // 使用 profileID 确保名称唯一
			Name:      "game-simple-server-" + utils.RandStringV2(8),
			Namespace: "default",
		},
		Spec: gamev1alpha1.GameServerSetSpec{
			Replicas: pointer.Int32(1),
			UpdateStrategy: gamev1alpha1.UpdateStrategy{
				Type: v1.RollingUpdateStatefulSetStrategyType,
				RollingUpdate: &gamev1alpha1.RollingUpdateStatefulSetStrategy{
					PodUpdatePolicy: "InPlaceIfPossible",
				},
			},
			GameServerTemplate: gamev1alpha1.GameServerTemplate{
				PodTemplateSpec: podTemplate,
			},
		},
	}, nil
}

var (
	scheme = runtime.NewScheme()
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(gamev1alpha1.AddToScheme(scheme))
}

// DeployGameServerV2 创建并部署GameServerSet TODO 待验证
func DeployGameServerV2(ctx context.Context, profileID, allocationID string) error {
	profile, err := GetProfile(profileID)
	if err != nil {
		return err
	}
	if profile.ReleasedRevision == nil || profile.ReleasedRevision.RevisionID == "" {
		return fmt.Errorf("profile %s has no released revision", profileID)
	}
	revision := profile.ReleasedRevision

	allocation, err := GetAllocation(allocationID)
	if err != nil {
		return err
	}

	// 创建客户端
	cfg, err := k8s.GetRestConfig()
	if err != nil {
		return err
	}
	cli, err := client.New(cfg, client.Options{Scheme: scheme})
	if err != nil {
		return err
	}

	// 1. 创建所有配置ConfigMap
	//configMaps := CreateConfigMaps(profileID, revision, allocation.Namespace)
	//for _, cm := range configMaps {
	//	if err := cli.Create(ctx, cm); err != nil {
	//		log.Printf("创建ConfigMap失败: %v", err)
	//	}
	//}

	// 2. 创建GameServerSet
	gss, err := CreateGameServerSetV2(profileID, revision, allocation)
	if err != nil {
		return fmt.Errorf("创建GameServerSet失败: %w", err)
	}

	if err := cli.Create(ctx, gss); err != nil {
		return fmt.Errorf("部署GameServerSet失败: %w", err)
	}

	// 3. 等待服务就绪
	// 设置轮询参数：间隔5秒，总超时5分钟
	err = wait.PollUntilContextTimeout(ctx, 5*time.Second, 5*time.Minute, true, func(ctx context.Context) (bool, error) {
		currentGSS := &gamev1alpha1.GameServerSet{}
		err := cli.Get(ctx, client.ObjectKey{
			Namespace: gss.Namespace,
			Name:      gss.Name,
		}, currentGSS)
		if err != nil {
			if apierrors.IsNotFound(err) {
				// 如果资源不存在，返回false和nil错误，继续等待
				return false, nil
			}
			// 对于其他错误，返回错误
			return false, err
		}

		if currentGSS.Status.ReadyReplicas == *currentGSS.Spec.Replicas {
			return true, nil
		}

		return false, nil
	})

	return err
}

func DeployGameServer(ctx context.Context, profileID string) error {
	gss, err := CreateGameServerSetV1(profileID)
	if err != nil {
		return err
	}
	// 创建客户端
	cfg, err := k8s.GetRestConfig()
	if err != nil {
		return err
	}
	cli, err := client.New(cfg, client.Options{Scheme: scheme})
	if err != nil {
		return err
	}
	// 提交到 Kubernetes
	if err = cli.Create(ctx, gss); err != nil {
		return err
	}

	return nil
}

func GetImage(ctx context.Context, appID, imageID string) (*Image, error) {
	image, err := models.GetImageByID(ctx, imageID)
	if err != nil {
		return nil, err
	}
	fileList, _ := image.GetFilelist()
	return &Image{
		ImageId:            image.ImageID,
		AppId:              image.AppID,
		ImageTag:           image.ImageTag,
		ImageStatus:        image.ImageStatus.GetStatus(),
		OsId:               image.OSId,
		OsName:             image.OSName,
		Description:        utils.GetValueByPointer(image.Description),
		CreatedAt:          image.CreatedAt.Format(time.DateTime),
		ImageUrl:           utils.GetValueByPointer(image.ObjectURL),
		DockerFile:         utils.GetValueByPointer(image.DockerFile),
		ImageObjectName:    utils.GetValueByPointer(image.ObjectName),
		ExecutableFilelist: fileList,
	}, nil
}

func GetImageList(ctx context.Context, appID string, offset, limit int) (int64, []Image, error) {
	images := make([]Image, 0)

	records, err := models.GetImagesByAppID(ctx, appID, offset, limit)
	if err != nil {
		return 0, nil, err
	}
	for _, record := range records {
		fileList, _ := record.GetFilelist()
		images = append(images, Image{
			ImageId:            record.ImageID,
			AppId:              record.AppID,
			ImageTag:           record.ImageTag,
			ImageStatus:        record.ImageStatus.GetStatus(),
			OsId:               record.OSId,
			OsName:             record.OSName,
			Description:        utils.GetValueByPointer(record.Description),
			CreatedAt:          record.CreatedAt.Format(time.DateTime),
			ImageUrl:           utils.GetValueByPointer(record.ObjectURL),
			DockerFile:         utils.GetValueByPointer(record.DockerFile),
			ImageObjectName:    utils.GetValueByPointer(record.ObjectName),
			ExecutableFilelist: fileList,
		})
	}
	total, err := models.CountImagesByAppID(ctx, appID)
	if err != nil {
		return 0, nil, err
	}
	return total, images, nil
}

func DeleteImage(ctx context.Context, appID, imageID string) error {
	image, err := models.GetImageByID(ctx, imageID)
	if err != nil {
		if strings.Contains(err.Error(), "record not found") {
			return fmt.Errorf("镜像不存在")
		}
		return err
	}
	if image.AppID != appID {
		return fmt.Errorf("参数不匹配")
	}
	// todo 是否删除构建并上传的镜像
	return models.SoftDeleteImage(ctx, imageID)
}

type Image struct {
	ImageId            string   `json:"image_id"`
	AppId              string   `json:"app_id"`
	ImageTag           string   `json:"image_tag"`
	ImageStatus        string   `json:"image_status"`
	OsId               int      `json:"os_id"`
	OsName             string   `json:"os_name"`
	Description        string   `json:"description"`
	CreatedAt          string   `json:"created_at"`
	ImageUrl           string   `json:"image_url"`
	DockerFile         string   `json:"docker_file"`
	ImageObjectName    string   `json:"image_object_name"`
	ExecutableFilelist []string `json:"executable_filelist"` // 为镜像添加需要增加执行权限的文件列表
}

type CreateImageReq struct {
	AppId              string   `json:"app_id"`              // appid
	ImageTag           string   `json:"image_tag"`           // 镜像标签
	OsId               int      `json:"os_id"`               // 操作系统
	Description        *string  `json:"description"`         // 描述
	ExecutableFilelist []string `json:"executable_filelist"` // 为镜像添加需要增加执行权限的文件列表
	// ImageObjectName -> ImageObjectName -> 上传到OSS的文件
	// ImageUrl -> 添加服务器程序 -> URL
	// DockerFile -> 添加服务器程序 -> DOCKER
	// 以上三选一
	ImageUrl        *string `json:"image_url"`
	DockerFile      *string `json:"docker_file"`
	ImageObjectName *string `json:"image_object_name"`
}

func CreateImage(ctx context.Context, req CreateImageReq) error {
	// 暂时只支持从oss获取
	if req.ImageObjectName == nil || *req.ImageObjectName == "" {
		return fmt.Errorf("请填写ImageObjectName")
	}
	// 1. 保存镜像文件
	image := &models.Image{
		AppID:       req.AppId,
		ImageTag:    req.ImageTag,
		OSId:        req.OsId,
		Description: req.Description,
		ObjectName:  req.ImageObjectName,
		DockerFile:  req.DockerFile,
		ObjectURL:   req.ImageUrl,
	}
	image.ImageID = utils.GetUUID()
	if err := image.SetFilelist(req.ExecutableFilelist); err != nil {
		return err
	}
	err := models.CreateImage(ctx, image)

	// 2. 推送构建任务到队列 or 异步执行
	go func() {
		// 构建镜像
		// 需要获取
		//zipOSSPath, err := oss_util.GetSignedURL("user-dirgame-server.zip", 3600)
		zipOSSPath, err := oss_util.GetSignedURL(utils.GetValueByPointer(req.ImageObjectName), 3600)
		if err != nil {
			fmt.Println(err)
			return
		}
		buildReq := k8s.BuildRequest{
			BaseImage:  "ubuntu:20.04",
			ImageTag:   req.ImageTag, // gameserver:v1
			ZipOSSPath: zipOSSPath,
			ExecFile:   "gameserver",
		}
		token, err := oss_util.GetSecurityToken()
		if err != nil {
			fmt.Println(err)
			return
		}
		buildReq.AccessKeyID = token.AccessKeyId
		buildReq.AccessKeySecret = token.AccessKeySecret
		_ = k8s.StartImageBuild(buildReq)
	}()
	return err
}

apiVersion: game.kruise.io/v1alpha1
  kind: GameServerSet
  metadata:
    name: bossroom-set-v1
    namespace: game-build
  spec:
    replicas: 1
    gameServerTemplate:
      metadata:
        labels:
          app: bossroom
      spec:
        containers:
          - name: bossroom
            image: 23234242
            imagePullPolicy: Always
            command: [ "BoosRoom" ]
            args:
              - ""
            ports:
              - name: udp-9998
                containerPort: 9998
                protocol: UDP
              - name: tcp-9999
                containerPort: 9999
                protocol: TCP
            resources:
              limits:
                cpu: "1000m"
                memory: "0Mi"
              requests:
                cpu: ""
                memory: ""
            env:
              - name: UNITY_HEADLESS
                value: "1"
        imagePullSecrets:
          - name: harbor-auth

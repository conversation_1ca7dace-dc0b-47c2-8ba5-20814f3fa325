package service

type MatchmakingServerSDK interface {
	// GetMatchInfo 获取匹配数据，包括AppId, RoomId 和玩家数据等信息（适用于按需模式）
	// 该方法用于房间启动后获取一次当前房间的基本配置和玩家数据，若开启BackFill，后续推荐使用GetNewPlayersFromBackFill获取最新的玩家数据
	// 可通过 multiverse local sdk server进行本地测试拿到mock 数据
	GetMatchInfo() (*Match, error)
	// WatchMatchInfo 舰队模式下需通过该方法Watch server, 直到game server为allocated状态，方能获取到匹配玩家数据
	// 当拿到匹配数据后，会自动停止Watch
	// 不可通过 multiverse local sdk server进行本地测试
	WatchMatchInfo(MatchInfoCallback) error
	// StartBackFill 为当前房间开启BackFill以匹配到更多的玩家
	// 不可通过 multiverse local sdk server进行本地测试
	StartBackFill([]*Team) error
	// StopBackFill 停止BackFill，会返回停止时间点该房间所有的玩家数据
	// 不可通过 multiverse local sdk server进行本地测试
	StopBackFill() ([]*Team, error)
	// GetNewPlayersFromBackFill BackFill进行过程中，可通过该方法拿到最新的玩家数据
	// 不可通过 multiverse local sdk server进行本地测试
	GetNewPlayersFromBackFill() ([]*Team, error)
	// WatchBackFill BackFill进行过程中， 可通过该方法Watch BackFill, 持续拿到最新抓取到房间的玩家数据
	// 当抓取玩家数量达到当前配置最大人数时，会自动停止Watch
	// 不可通过 multiverse local sdk server进行本地测试
	WatchBackFill(BackFillCallback) error
	// IsBackFillStarted 判断当前是否开启BackFill
	// 不可通过 multiverse local sdk server进行本地测试
	IsBackFillStarted() (bool, error)
}

type Match struct {
	AppId      string  `json:"app_id"`
	RoomId     string  `json:"room_id"`
	Teams      []*Team `json:"teams"`
	BackFill   bool    `json:"back_fill"`
	BackFillId string  `json:"back_fill_id"`
}

type Team struct {
	TeamId      string            `json:"team_id"`
	TeamPlayers []*TeamPlayerInfo `json:"team_players"`
}

type TeamPlayerInfo struct {
	PlayerId      string `json:"player_id"`
	PlayerName    string `json:"player_name"`
	PlayerAddress string `json:"player_address"`
}

type MatchInfoCallback struct {
}

type BackFillCallback struct {
}

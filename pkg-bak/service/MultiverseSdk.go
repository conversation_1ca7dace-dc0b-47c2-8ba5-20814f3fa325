package service

type MultiverseSDK interface {
	// Ready 标记 game server 为 ready 状态
	Ready() error
	// GetAllocationEnvs 获取启动配置里配置的和Allocation 接口传过来的环境变量
	GetAllocationEnvs() (map[string]string, error)
	// GetServerInfo 获取server 基础配置信息，如 ip, port, allocationId等信息
	GetServerInfo() (*ServerInfo, error)
	// GetExpireAt 获取当前 game server 到期时间
	GetExpireAt() (int64, error)
	// Shutdown 关闭 game server
	Shutdown() error
	// Allocate 标记 game server 为 allocated 状态, 舰队模式可用该方法来分配 game server
	Allocate() error
	// WatchGameServer watch game server status, 舰队模式可用该方法监听 game server到 allocated 状态
	WatchGameServer(GameServerCallback) error
}

type GameServerCallback struct {
}

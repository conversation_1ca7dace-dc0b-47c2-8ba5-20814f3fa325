package service

import (
	"testing"
	"time"
)

func TestToGameServerSetYAML(t *testing.T) {
	profile := &ProfileRevisionV2{
		RevisionID:    "2121",
		ProfileID:     "ewewe2",
		CPULimit:      "100m",
		CPURequest:    "100m",
		MemoryLimit:   "153Mi",
		MemoryRequest: "153Mi",
		GameServerPorts: []ServerPort{
			{
				Name:       "udp-9998",
				Port:       9998,
				Protocol:   "UDP",
				PortPolicy: "Dynamic",
			},
			{
				Name:       "tcp-9999",
				Port:       9999,
				Protocol:   "TCP",
				PortPolicy: "Dynamic",
			},
		},
		GameServerEntryPoint: []string{
			"BoosRoom", "",
		},
		GameStartDuration: 30,
		GameImage: GameImageV2{
			ImageID:   "hb.mg.xyz/multiverse/gameserver:v1",
			ImageTag:  "V2",
			ImageUrl:  "",
			CreatedAt: time.Now().Format(time.DateTime),
		},
		EnvironmentVariables: map[string]string{
			"test-var1": "1",
			"test-var2": "2",
			"test-var3": "3",
		},
		FileConfigs: []FileConfigV2{
			{
				MountPath: "/home/<USER>",
				Filename:  "test.txt",
				Content:   "test",
			},
		},
		CreatedAt: time.Now().Format(time.DateTime),
	}
	yaml, err := profile.ToGameServerSetYAML()
	if err != nil {
		t.Errorf("ToGameServerSetYAML() error = %v", err)
		return
	}
	t.Logf("yaml = %s", yaml)
}

package service

import (
	"context"
	"fmt"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
)

type Project struct {
	ProjectID             string `json:"project_id"`
	ProjectName           string `json:"project_name"`
	AllocationTTL         int64  `json:"allocation_ttl"`
	OrgID                 string `json:"org_id"`
	OsID                  int64  `json:"os_id"`
	CreatedAt             string `json:"created_at"`
	AllocationCallbackUrl string `json:"allocation_callback_url"`
}

// GetProject 项目详情
func GetProject(projectId string) (*Project, error) {
	if projectId == "" {
		return nil, fmt.Errorf("project_id is empty")
	}
	record, err := models.GetByProjectID(projectId)
	if err != nil {
		return nil, err
	}
	project := &Project{
		ProjectID:             record.ProjectID,
		ProjectName:           record.Name,
		AllocationTTL:         record.AllocationTTL,
		OrgID:                 record.OrganizationID,
		OsID:                  record.OsID,
		CreatedAt:             record.CreatedAt.Format(time.DateTime),
		AllocationCallbackUrl: record.AllocationCallbackURL,
	}
	return project, nil
}

// GetProjectList 项目列表
func GetProjectList(ctx context.Context, appID string, offset, limit int) ([]*Project, int64, error) {
	project := &Project{
		ProjectID: "11123",
	}
	var projects []*Project

	total, err := models.CountProjectsByAppID(ctx, appID)
	if err != nil {
		return nil, 0, err
	}

	records, err := models.GetProjectsByAppID(ctx, appID, offset, limit)
	for _, record := range records {
		projects = append(projects, &Project{
			ProjectID:             record.ProjectID,
			ProjectName:           record.Name,
			AllocationTTL:         record.AllocationTTL,
			OrgID:                 record.OrganizationID,
			OsID:                  record.OsID,
			CreatedAt:             record.CreatedAt.Format(time.DateTime),
			AllocationCallbackUrl: record.AllocationCallbackURL,
		})
	}
	projects = append(projects, project)
	return projects, total, nil
}

type UpdProjectReq struct {
	AppID                 string `json:"app_id"`
	ProjectID             string `json:"project_id"`
	Name                  string `json:"name"`
	AllocationTTL         int64  `json:"allocation_ttl"`
	AllocationCallbackUrl string `json:"allocation_callback_url"`
	OsID                  int64  `json:"os_id"`
}

type CreateProjectReq struct {
	AppID                 string `json:"app_id"`
	ProjectName           string `json:"project_name"`
	AllocationTTL         int64  `json:"allocation_ttl"`
	OrgID                 string `json:"org_id"`
	OsID                  int64  `json:"os_id"`
	AllocationCallbackUrl string `json:"allocation_callback_url"`
}

// UpdProject 修改项目
// PUT /multiverse/v1/game/games/df139cac-28d0-4a22-9d58-05358feca9b0
func UpdProject(req *UpdProjectReq) error {
	project, err := GetProject(req.ProjectID)
	if err != nil {
		return err
	}
	err = models.UpdateProject(project.ProjectID, models.Project{
		Name:                  req.Name,
		AllocationTTL:         req.AllocationTTL,
		AllocationCallbackURL: req.AllocationCallbackUrl,
		OsID:                  req.OsID,
	})
	return err
}

type ProjectEnableReq struct {
	Enable              bool `json:"enable"`
	EnableMatchmakingMv bool `json:"enable_matchmaking_mv"`
}

// EnableRoomManager 开启房间管理
// POST https://uos.unity.cn/api/app/df139cac-28d0-4a22-9d58-05358feca9b0/service
func EnableRoomManager(projectID string) error {
	return models.UpdateDedicatedRoom(projectID, true)
}

// DisableRoomManager 关闭房间管理
func DisableRoomManager(projectID string) error {
	return models.UpdateDedicatedRoom(projectID, false)
}

// EnableMatchmaking 开启匹配
func EnableMatchmaking(projectID string) error {
	return models.UpdateMatchmaking(projectID, true)
}

// DisableMatchmaking 关闭匹配
func DisableMatchmaking(projectID string) error {
	return models.UpdateMatchmaking(projectID, false)
}

func ValidateCreateProjectReq(req *CreateProjectReq) error {
	if req.AppID == "" {
		return fmt.Errorf("app_id is empty")
	}
	if req.ProjectName == "" {
		return fmt.Errorf("project_name is empty")
	}
	if req.OrgID == "" {
		return fmt.Errorf("org_id is empty")
	}
	if req.OsID == 0 {
		return fmt.Errorf("os_id is empty")
	}
	return nil
}

// CreateProject 创建项目
func CreateProject(ctx context.Context, req *CreateProjectReq) error {
	if err := ValidateCreateProjectReq(req); err != nil {
		return err
	}
	record := &models.Project{
		AppID:                 req.AppID,
		ProjectID:             utils.GetUUID(),
		OrganizationID:        req.OrgID,
		Name:                  req.ProjectName,
		IconURL:               "",
		Archived:              false,
		Active:                false,
		AllocationTTL:         req.AllocationTTL,
		AllocationCallbackURL: req.AllocationCallbackUrl,
		OsID:                  req.OsID,
	}
	err := models.CreateProject(record)
	if err != nil {
		return err
	}
	return nil
}

// DeleteProject 删除项目 软删除
func DeleteProject(projectID string) error {
	if projectID == "" {
		return fmt.Errorf("project_id is empty")
	}
	project, err := GetProject(projectID)
	if err != nil {
		return err
	}
	return models.SoftDelete(project.ProjectID)
}

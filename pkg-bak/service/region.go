package service

import (
	"fmt"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
)

type GameRegion struct {
	AppID      string `json:"app_id"`
	RegionID   string `json:"region_id"`
	RegionName string `json:"region_name"`
	MaxServers int    `json:"max_servers"`
	CreatedAt  string `json:"created_at"`
}

func GetRegionByID(regionID string) (*GameRegion, error) {
	if regionID == "" {
		return nil, fmt.Errorf("region_id is empty")
	}
	record, err := models.NewRegionRepo(dao.Db).GetByRegionID(regionID)
	if err != nil {
		return nil, err
	}
	return &GameRegion{
		AppID:      record.AppID,
		RegionID:   record.RegionID,
		RegionName: record.Name,
		MaxServers: 0,
		CreatedAt:  record.CreatedAt.Format(time.DateTime),
	}, nil
}

func GetAllRegions(page, pageSize int) ([]*GameRegion, int64, error) {
	records, total, err := models.NewRegionRepo(dao.Db).FindAll(page, pageSize)
	if err != nil {
		return nil, 0, err
	}
	var result []*GameRegion
	for _, record := range records {
		result = append(result, &GameRegion{
			AppID:      record.AppID,
			RegionID:   record.RegionID,
			RegionName: record.Name,
			MaxServers: 0,
			CreatedAt:  record.CreatedAt.Format(time.DateTime),
		})
	}
	return result, total, nil
}

func UpdateRegion(regionID string, maxServers int64) error {
	record, err := models.NewRegionRepo(dao.Db).GetByRegionID(regionID)
	if err != nil {
		return err
	}
	record.MaxServers = maxServers
	return models.NewRegionRepo(dao.Db).Update(record)
}

func SetActive(regionID string, active bool) error {
	return models.NewRegionRepo(dao.Db).SetActive(regionID, active)
}

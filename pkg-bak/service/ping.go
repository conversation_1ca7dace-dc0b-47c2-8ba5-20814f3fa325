package service

// GET https://multiverse.scaling.unity.cn/v1/game/games/{appId}/ping-services
// 获取一个游戏下所有启用的游戏地域的端口信息，用于ping测试延迟。
// 输出示例
/**
{
  "gameId": "string",
  "pingServiceEndpoints": [
    {
      "regionId": "string",
      "httpEndpoint": "string",
      "httpPort": 0,
      "udpEndpoint": "string",
      "udpPort": 0
    }
  ]
}
*/

func GetPingServers(gameId string) ([]string, error) {
	var servers []string

	// 查询数据库获取游戏下所有启用的游戏地域的端口信息

	return servers, nil
}

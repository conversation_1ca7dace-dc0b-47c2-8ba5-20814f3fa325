package match_service

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"
	"github.com/goccy/go-json"
	"github.com/pkg/errors"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/anypb"

	om "open-match.dev/open-match/pkg/pb"
)

// 匹配demo
// https://github.com/CloudNativeGame/kruise-game-open-match-example
// https://github.com/CloudNativeGame/kruise-game-open-match-director

type MatchFunctionServer struct {
	om.UnimplementedMatchFunctionServer
}

// Run 注意：这里使用 GetExtensions() 是伪代码，真实 OpenMatch Profile 里 Tickets 是由 Backend 分批推送。正式实现见下面备注。注意：这里使用 GetExtensions() 是伪代码，真实 OpenMatch Profile 里 Tickets 是由 Backend 分批推送。正式实现见下面备注。
func (s *MatchFunctionServer) Run(ctx context.Context, req *om.RunRequest) (*om.RunResponse, error) {
	tickets := req.GetProfile().GetExtensions()["ticket_list"].GetValue()
	var proposals []*om.Match

	for i := 0; i+1 < len(tickets); i += 2 {
		match := &om.Match{
			MatchId:      fmt.Sprintf("match-%d", time.Now().UnixNano()),
			MatchProfile: req.Profile.Name,
			Tickets: []*om.Ticket{
				{Id: strconv.Itoa(int(tickets[i]))},
				{Id: strconv.Itoa(int(tickets[i+1]))},
			},
		}
		proposals = append(proposals, match)
	}

	return &om.RunResponse{}, nil
}

var frontendClient om.FrontendServiceClient

func initFrontendClient() {
	conn, err := grpc.Dial("open-match-frontend.open-match.svc.cluster.local:50504", grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to frontend: %v", err)
	}
	frontendClient = om.NewFrontendServiceClient(conn)
}

/*
	{
	  "ticket_id": "auto-generated",
	  "match_mode": "1v1",               // 或 "5v5"、"3v3"、"party"
	  "is_party": false,                 // 是否是组队匹配，true=整个 Ticket 是一队
	  "party_id": "optional-party-id",   // 如果是组队，这里要填唯一 Party ID
	  "players": [
	    {
	      "player_id": "p1",
	      "level": 12,
	      "region": "CN",
	      "custom_attributes": {
	        "role": "tank",
	        "score": 1337
	      }
	    }
	    // 若为单人匹配，数组里只放 1 个玩家
	    // 若为组队匹配，放多个玩家
	  ],
	  "properties": {
	    "preferred_region": "CN",
	    "team_size": 3,
	    "game_mode": "ranked"
	  }
	}
*/
type TicketRequest struct {
	MatchMode  string         `json:"match_mode"`           // 匹配模式标签，比如 "1v1"、"2v2" 等
	IsParty    bool           `json:"is_party"`             // 是否组队
	PartyID    string         `json:"party_id,omitempty"`   // 玩家唯一标识
	Players    []Player       `json:"players"`              // 玩家列表
	Properties map[string]any `json:"properties,omitempty"` // 可选的匹配属性，比如等级、地域等
}

type Player struct {
	PlayerID         string         `json:"player_id"`         // 玩家ID
	Level            int            `json:"level"`             // 玩家等级
	Region           string         `json:"region"`            // 玩家地区
	CustomAttributes map[string]any `json:"custom_attributes"` // 玩家自定义属性
}

type TicketExtensionPayload struct {
	PartyID    string         `json:"party_id,omitempty"`
	IsParty    bool           `json:"is_party"`
	Players    []Player       `json:"players"`
	Properties map[string]any `json:"properties,omitempty"`
}

/*
CreateTicket

提供 REST 接口 /match
•	创建 Ticket 并发送到 OpenMatch Frontend
•	返回 Ticket ID，供后续状态轮询或匹配监听使用（可选）
*/
func CreateTicket(req TicketRequest) (string, error) {
	if req.PartyID == "" {
		req.PartyID = utils.GetUUID()
	}
	// 打包 Extension Payload
	payload := TicketExtensionPayload{
		PartyID:    req.PartyID,
		IsParty:    req.IsParty,
		Players:    req.Players,
		Properties: req.Properties,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}

	// 设置 Extensions 字段，key 可以自定义 eg. "ticket_payload"
	ticket := &om.Ticket{
		SearchFields: &om.SearchFields{
			Tags: []string{req.MatchMode},
		},
		Extensions: map[string]*anypb.Any{
			"ticket_payload": {
				TypeUrl: "type.googleapis.com/json",
				Value:   payloadBytes,
			},
		},
	}

	request := om.CreateTicketRequest{Ticket: ticket}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	res, err := frontendClient.CreateTicket(ctx, &request)
	if err != nil {
		return "", errors.Wrap(err, "CreateTicket")
	}
	return res.Id, nil
}

// DeleteTicket 删除Ticket
func DeleteTicket(ticketId string) error {
	request := om.DeleteTicketRequest{TicketId: ticketId}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	_, err := frontendClient.DeleteTicket(ctx, &request)
	if err != nil {
		return errors.Wrap(err, "DeleteTicket")
	}
	return nil
}

// GetTicketStatus 获取Ticket状态
func GetTicketStatus(ticketId string) (connection string, err error) {
	request := om.GetTicketRequest{TicketId: ticketId}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	ticket, err := frontendClient.GetTicket(ctx, &request)
	if err != nil {
		return "", errors.Wrap(err, "GetTicket")
	}
	assignment := ticket.GetAssignment()
	if assignment != nil && assignment.Connection != "" {
		return assignment.Connection, nil
	}
	return "", nil
}

// GetTicket 获取Ticket
func GetTicket(ticketId string) (string, error) {
	request := om.GetTicketRequest{TicketId: ticketId}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	res, err := frontendClient.GetTicket(ctx, &request)
	if err != nil {
		return "", errors.Wrap(err, "GetTicket")
	}
	return res.Id, nil
}

// CreateBackFill 创建BackFill 向 Open Match 提交 Backfill 实体
// 1. 使用 BackendService.CreateBackfill()
// 2. 并将其与现有 Match 绑定
func CreateBackFill() (string, error) {
	request := om.CreateBackfillRequest{Backfill: &om.Backfill{
		Id:              "",
		SearchFields:    nil,
		Extensions:      nil,
		PersistentField: nil,
		CreateTime:      nil,
		Generation:      0,
	}}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	res, err := frontendClient.CreateBackfill(ctx, &request)
	if err != nil {
		return "", err
	}
	return res.Id, nil
}

// GetBackFill 获取回填
func GetBackFill(backFillID string) (string, error) {
	request := om.GetBackfillRequest{BackfillId: backFillID}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	res, err := frontendClient.GetBackfill(ctx, &request)
	if err != nil {
		return "", err
	}
	return res.Id, nil
}

// DeleteBackFill 删除回填
func DeleteBackFill(backFillID string) error {
	request := om.DeleteBackfillRequest{BackfillId: backFillID}
	_, err := frontendClient.DeleteBackfill(context.Background(), &request)
	if err != nil {
		return err
	}
	return nil
}

func SelectGameServer() string {
	// 假设直接返回固定 GameServer 地址
	// TODO: 调用 KruiseGame API，查找 status == Ready 的 GameServer 实例
	return "192.168.1.222:7777"
}

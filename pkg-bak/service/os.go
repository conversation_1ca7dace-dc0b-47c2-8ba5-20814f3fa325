package service

import (
	"context"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
)

type Os struct {
	OsId     int64  `json:"os_id"`
	OsName   string `json:"os_name"`
	OsFamily string `json:"os_family"`
}

// GetOsList .
func GetOsList(ctx context.Context, offset, limit int) (int64, []Os, error) {
	osList := make([]Os, 0)

	list, err := models.GetOsList(ctx, offset, limit)
	if err != nil {
		return 0, nil, err
	}
	for _, os := range list {
		osList = append(osList, Os{
			OsId:     int64(os.ID),
			OsName:   os.OSName,
			OsFamily: os.OSFamily,
		})
	}

	total, err := models.CountOs(ctx)
	if err != nil {
		return 0, nil, err
	}
	return total, osList, nil
}

func GetOsByID(ctx context.Context, osID uint) (*Os, error) {
	os, err := models.GetOsByID(ctx, osID)
	if err != nil {
		return nil, err
	}
	return &Os{
		OsId:     int64(os.ID),
		OsName:   os.OSName,
		OsFamily: os.OSFamily,
	}, nil
}

type CreateOsReq struct {
	OsName   string `json:"os_name"`
	OsFamily string `json:"os_family"`
}

func CreateOs(ctx context.Context, osName, osFamily string) error {
	os := &models.Os{
		OSName:   osName,
		OSFamily: osFamily,
	}
	return models.CreateOs(ctx, os)
}

package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"text/template"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
	"github.com/google/uuid"
)

/**
参考：
https://uos.unity.cn/doc/multiverse/image

1. 创建镜像配置
2. 查询镜像配置状态
需要主动轮询此 API, 直到 status 为 success, 才可调用「应用镜像配置API」来发布镜像版本（一般轮询时长在2到5分钟之间）。
3. 获取镜像创建的日志
4. 查看测试server的运行日志
5. 应用镜像配置
*/

// CreateProfileRevision 创建启动配置版本
// POST https://multiverse.scaling.unity.cn/v1/game/profiles/{profileId}/ci/profile-Revision
func CreateProfileRevision(ctx context.Context, req *CreateProfileRevisionReq) (*CreateProfileRevisionResp, error) {
	now := time.Now()
	revisionID := utils.GetUUID()

	environmentVariables, err := json.Marshal(req.EnvironmentVariables)
	if err != nil {
		return nil, err
	}
	gameServerPorts, err := json.Marshal(req.GameServerPorts)
	if err != nil {
		return nil, err
	}
	entryPoints, err := json.Marshal(req.GameServerEntryPoint)
	if err != nil {
		return nil, err
	}
	fileConfigs, err := json.Marshal(req.FileConfigs)
	if err != nil {
		return nil, err
	}
	revision := &models.ProfileRevision{
		RevisionID:           revisionID,
		ProfileID:            req.ProfileID,
		GameImageID:          req.GameImageID,
		RevisionStatus:       models.RevisionStatusDraft,
		ReleaseVersion:       1, //todo 获取当前启动配置版本号+1
		EnvironmentVariables: environmentVariables,
		GameServerPorts:      gameServerPorts,
		CPULimit:             req.CPULimit,
		CPURequest:           req.CPURequest,
		MemoryLimit:          req.MemoryLimit,
		MemoryRequest:        req.MemoryRequest,
		GPULimit:             req.GPULimit,
		GPURequest:           req.GPURequest,
		GameStartDuration:    req.GameStartDuration,
		EntryPoints:          entryPoints,
		ReleasedBy:           "",
		ReleasedAt:           &now,
		FileConfigs:          fileConfigs,
	}
	err = models.CreateProfileRevision(ctx, revision)
	if err != nil {
		return nil, err
	}
	return &CreateProfileRevisionResp{
		ProfileRevisionId: revisionID,
		Status:            models.RevisionStatusDraft.String(),
		TestId:            "",
		Msg:               "",
		Reason:            "",
	}, nil
}

// DeleteProfile 删除启动配置
func DeleteProfile(ctx context.Context, profileID string) error {
	err := models.DeleteProfile(profileID)
	if err != nil {
		return err
	}
	err = models.DeleteProfileRevisionByProfileID(ctx, profileID)
	if err != nil {
		return err
	}
	return nil
}

func UpdProfileRevision(ctx context.Context, req *UpdProfileRevisionReq) error {
	if req.RevisionID == "" {
		req.RevisionID = uuid.New().String()
	}
	revision, err := models.GetProfileRevision(req.RevisionID)
	if err != nil {
		return err
	}
	//if revision.RevisionStatus != models.RevisionStatusDraft {
	//	return fmt.Errorf("revision status is not draft")
	//}

	environmentVariablesBytes, err := json.Marshal(req.EnvironmentVariables)
	if err != nil {
		return err
	}
	gameServerPortsBytes, err := json.Marshal(req.GameServerPorts)
	if err != nil {
		return err
	}
	gameServerEntryPointBytes, err := json.Marshal(req.GameServerEntryPoint)
	if err != nil {
		return err
	}
	fileConfigsBytes, err := json.Marshal(req.FileConfigs)
	if err != nil {
		return err
	}
	profileRevision := &models.ProfileRevision{
		RevisionID:           req.RevisionID,
		ProfileID:            req.ProfileID,
		GameImageID:          req.GameImageID,
		ReleaseVersion:       revision.ReleaseVersion + 1,
		EnvironmentVariables: environmentVariablesBytes,
		GameServerPorts:      gameServerPortsBytes,
		CPULimit:             req.CPULimit,
		CPURequest:           req.CPURequest,
		MemoryLimit:          req.MemoryLimit,
		MemoryRequest:        req.MemoryRequest,
		GPULimit:             req.GPULimit,
		GPURequest:           req.GPURequest,
		GameStartDuration:    req.GameStartDuration,
		EntryPoints:          gameServerEntryPointBytes,
		ModifiedBy:           "",
		FileConfigs:          fileConfigsBytes,
	}
	if err := models.UpdProfileRevision(ctx, profileRevision, revision.ReleaseVersion); err != nil {
		return err
	}
	return nil
}

// GetProfileRevisionStatus 查询镜像配置状态
func GetProfileRevisionStatus(revisionId string) {
	//revision, err := GetProfileRevision(revisionId)
	//if err != nil {
	//	return
	//}

}

// GetCreateImageLogs 若在 「查询镜像配置状态API」 中查询到的镜像配置状态为 imageBuildingFailure ，则可调用该API来查询日志。
func GetCreateImageLogs(imageId string) {

}

// GetTestServerLogs 若在 「查询镜像配置状态API」 中查询到的镜像配置状态为 testFailure ，则可调用该API来查询日志。
func GetTestServerLogs(testIds string) {

}

// ProfileRevisionRelease 若在 「查询镜像配置状态API」 中查询到的镜像配置状态为 success ，则可调用该API来应用该镜像配置。
func ProfileRevisionRelease(ctx context.Context, profileRevisionId string) error {
	revision, err := models.GetProfileRevision(profileRevisionId)
	if err != nil {
		fmt.Println("GetProfileRevision error:", err)
		return err
	}
	profile, err := models.GetProfile(revision.ProfileID)
	if err != nil {
		fmt.Println("GetProfile error:", err)
		return err
	}
	err = models.SetProfileRevisionID(profile.ProfileID, profileRevisionId)
	fmt.Println("SetProfileRevisionID error:", err)
	if err != nil {
		return err
	}
	return nil
}

type Profile struct {
	ProfileId          string `json:"profile_id"`
	AppId              string `json:"app_id"`
	ProfileName        string `json:"profile_name"`
	CreatedAt          string `json:"created_at"`
	MinServers         uint64 `json:"min_servers"`
	BufferSize         uint8  `json:"buffer_size"`
	GameImageTag       string `json:"game_image_tag"`
	GameImageCreatedAt string `json:"game_image_created_at"`
}

type ProfileDetail struct {
	ProfileID        string             `json:"profile_id"`
	AppID            string             `json:"app_id"`
	ProfileName      string             `json:"profile_name"`
	ReleasedRevision *ProfileRevisionV2 `json:"released_Revision"`
	CreatedAt        string             `json:"created_at"`
	MinServers       uint64             `json:"min_servers"`
	BufferSize       uint8              `json:"buffer_size"`
}

type ProfileRevisionV2 struct {
	ID                   uint64            `json:"id"`
	RevisionID           string            `json:"Revision_id"`
	ProfileID            string            `json:"profile_id"`
	CPULimit             string            `json:"cpu_limit"`
	CPURequest           string            `json:"cpu_request"`
	MemoryLimit          string            `json:"memory_limit"`
	MemoryRequest        string            `json:"memory_request"`
	GameServerPorts      []ServerPort      `json:"game_server_ports"`
	GameServerEntryPoint []string          `json:"game_server_entry_point"`
	GameStartDuration    int64             `json:"game_start_duration"`
	GameImage            GameImageV2       `json:"game_image"`
	EnvironmentVariables map[string]string `json:"environment_variables"`
	FileConfigs          []FileConfigV2    `json:"file_configs"`
	CreatedAt            string            `json:"created_at"`
	Namespace            string            `json:"namespace"`
}

func (p *ProfileRevisionV2) GetGssTemplate() string {
	return fmt.Sprintf("game-server-%d", p.ID)
}

type ServerPort struct {
	Name       string `json:"name"`
	Port       int    `json:"port"`
	Protocol   string `json:"protocol"`
	PortPolicy string `json:"port_policy"`
}

type GameImageV2 struct {
	ImageID   string `json:"image_id"`
	ImageTag  string `json:"image_tag"`
	ImageUrl  string `json:"image_url"`
	CreatedAt string `json:"created_at"`
}

type FileConfigV2 struct {
	MountPath string `json:"mountPath"`
	Filename  string `json:"filename"`
	Content   string `json:"content"`
}

// Validate 校验格式
func (p *ProfileRevisionV2) Validate() error {
	// 校验必填字段
	if p.RevisionID == "" {
		return errors.New("RevisionID 不能为空")
	}
	if p.ProfileID == "" {
		return errors.New("ProfileID 不能为空")
	}
	if p.GameImage.ImageID == "" {
		return errors.New("GameImageID 不能为空")
	}
	// todo
	return nil
}

// ToGameServerSetYAML 生成YAML文件
func (p *ProfileRevisionV2) ToGameServerSetYAML() (string, error) {
	// 必要校验
	if err := p.Validate(); err != nil {
		return "", err
	}
	// 解析 EntryPoints 和 Ports
	var entryPoints []string
	entryPoints = p.GameServerEntryPoint

	//var ports []struct {
	//	Name     string `json:"name"`
	//	Port     int    `json:"port"`
	//	Protocol string `json:"protocol"`
	//}
	//if err := json.Unmarshal(p.GameServerPorts, &ports); err != nil {
	//	return "", fmt.Errorf("GameServerPorts JSON 无效: %v", err)
	//}

	// 模板上下文
	data := struct {
		Name            string
		Namespace       string
		Image           string
		Command         string
		Args            []string
		Ports           []map[string]interface{}
		CPULimit        string
		CPURequest      string
		MemoryLimit     string
		MemoryRequest   string
		ImagePullSecret string
	}{
		Name:            fmt.Sprintf("bossroom-set-v%d", 1), // todo
		Namespace:       "game-build",                       // 可改为参数
		Image:           p.GameImage.ImageID,
		Command:         entryPoints[0],
		Args:            entryPoints[1:],
		CPULimit:        p.CPULimit,
		CPURequest:      p.CPURequest,
		MemoryLimit:     p.MemoryLimit,
		MemoryRequest:   p.MemoryRequest,
		ImagePullSecret: "harbor-auth",
	}

	// 生成端口列表
	for _, port := range p.GameServerPorts {
		data.Ports = append(data.Ports, map[string]interface{}{
			"name":     port.Name,
			"port":     port.Port,
			"protocol": port.Protocol,
		})
	}

	// 定义 YAML 模板
	const tpl = `
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: {{ .Name }}
  namespace: {{ .Namespace }}
spec:
  replicas: 1
  gameServerTemplate:
    metadata:
      labels:
        app: bossroom
    spec:
      containers:
        - name: bossroom
          image: {{ .Image }}
          imagePullPolicy: Always
          command: ["{{ .Command }}"]
{{- if .Args }}
          args:
{{- range .Args }}
            - "{{ . }}"
{{- end }}
{{- end }}
          ports:
{{- range .Ports }}
            - name: {{ .name }}
              containerPort: {{ .port }}
              protocol: {{ .protocol }}
{{- end }}
          resources:
            limits:
              cpu: "{{ .CPULimit }}"
              memory: "{{ .MemoryLimit }}"
            requests:
              cpu: "{{ .CPURequest }}"
              memory: "{{ .MemoryRequest }}"
          env:
            - name: UNITY_HEADLESS
              value: "1"
      imagePullSecrets:
        - name: {{ .ImagePullSecret }}
`

	// 渲染模板
	t, err := template.New("gss").Parse(tpl)
	if err != nil {
		return "", err
	}
	var buf bytes.Buffer
	if err := t.Execute(&buf, data); err != nil {
		return "", err
	}
	return buf.String(), nil
}

func GetProfile(profileID string) (*ProfileDetail, error) {
	if profileID == "" {
		return nil, fmt.Errorf("profileID is empty")
	}
	profile, err := models.GetProfile(profileID)
	if err != nil {
		return nil, err
	}
	var releasedRevision *ProfileRevisionV2

	if profile.RevisionID != "" {
		releasedRevision, err = GetProfileRevision(profile.RevisionID)
		if err != nil {
			return nil, err
		}
	}
	detail := &ProfileDetail{
		ProfileID:        profile.ProfileID,
		AppID:            profile.AppID,
		ProfileName:      profile.ProfileName,
		CreatedAt:        profile.CreatedAt.Format(time.DateTime),
		MinServers:       profile.MinServers,
		BufferSize:       profile.BufferSize,
		ReleasedRevision: releasedRevision,
	}
	return detail, nil
}

func GetProfileList(ctx context.Context, appID string, offset, limit int) (int64, []Profile, error) {
	if len(appID) == 0 {
		return 0, []Profile{}, nil
	}
	profiles := make([]Profile, 0)

	count, err := models.CountProfileByAppID(ctx, appID)
	if err != nil {
		return 0, nil, err
	}
	records, err := models.GetProfileList(appID, offset, limit)
	if err != nil {
		return 0, nil, err
	}
	for _, record := range records {
		profiles = append(profiles, Profile{
			ProfileId:          record.ProfileID,
			AppId:              record.AppID,
			ProfileName:        record.ProfileName,
			CreatedAt:          record.CreatedAt.Format(time.DateTime),
			MinServers:         record.MinServers,
			BufferSize:         record.BufferSize,
			GameImageTag:       "tag:v2",
			GameImageCreatedAt: time.Now().Format(time.DateTime),
		})
	}

	return count, profiles, nil
}

// GetProfileRevision .
func GetProfileRevision(RevisionId string) (*ProfileRevisionV2, error) {
	revision, err := models.GetProfileRevision(RevisionId)
	if err != nil {
		return nil, err
	}

	entryPoints := make([]string, 0)
	err = json.Unmarshal(revision.EntryPoints, &entryPoints)
	if err != nil {
		return nil, err
	}
	Revision := ProfileRevisionV2{
		ID:                   revision.ID,
		RevisionID:           revision.RevisionID,
		ProfileID:            revision.ProfileID,
		CPULimit:             revision.CPULimit,
		CPURequest:           revision.CPURequest,
		MemoryLimit:          revision.MemoryLimit,
		MemoryRequest:        revision.MemoryRequest,
		GameServerPorts:      GetGameServerPorts(revision.GameServerPorts),
		GameServerEntryPoint: entryPoints,
		GameStartDuration:    revision.GameStartDuration,
		GameImage: GameImageV2{
			ImageID:   "2d3d855c-c1a0-4df7-a562-bc87f28e4a2b",
			ImageTag:  "tag111",
			ImageUrl:  "crpi-9ctxcivq4qir72h2.cn-guangzhou.personal.cr.aliyuncs.com/mgrc-ros/ros:gamesever1.0",
			CreatedAt: "2025-04-15T07:45:03",
		},
		EnvironmentVariables: GetEnvironmentVariables(revision.EnvironmentVariables),
		FileConfigs:          GetFileConfigs(revision.FileConfigs),
		CreatedAt:            revision.CreatedAt.Format(time.DateTime),
	}
	return &Revision, nil
}

// GetGameServerPorts .
func GetGameServerPorts(bytes []byte) []ServerPort {
	if len(bytes) == 0 {
		return []ServerPort{}
	}

	var list []ServerPort
	err := json.Unmarshal(bytes, &list)
	if err != nil {
		return []ServerPort{}
	}
	return list
}

func GetEnvironmentVariables(bytes []byte) map[string]string {
	if len(bytes) == 0 {
		return map[string]string{}
	}
	var result map[string]string
	err := json.Unmarshal(bytes, &result)
	if err != nil {
		return map[string]string{}
	}
	return result
}

func GetFileConfigs(bytes []byte) []FileConfigV2 {
	if len(bytes) == 0 {
		return []FileConfigV2{}
	}
	var result []FileConfigV2
	err := json.Unmarshal(bytes, &result)
	if err != nil {
		return []FileConfigV2{}
	}
	return result
}

func GetGameServerEntryPoint(bytes []byte) []string {
	if len(bytes) == 0 {
		return []string{}
	}
	var result []string
	err := json.Unmarshal(bytes, &result)
	if err != nil {
		return []string{}
	}
	return result
}

// GetProfileRevisionList .
func GetProfileRevisionList(ctx context.Context, profileID string, offset, limit int) ([]*ProfileRevisionV2, int64, error) {
	if profileID == "" {
		return nil, 0, fmt.Errorf("profileID is empty")
	}

	total, err := models.CountProfileRevisionByProfileID(ctx, profileID)
	if err != nil {
		return nil, 0, err
	}

	records, err := models.GetProfileRevisionList(profileID, offset, limit)
	if err != nil {
		return nil, 0, err
	}
	var result []*ProfileRevisionV2
	var imageIDs []string
	for _, record := range records {
		imageIDs = append(imageIDs, record.GameImageID)
	}

	_, imageMap, err := models.BatchGetImageIDs(ctx, imageIDs)
	if err != nil {
		return nil, 0, err
	}
	for _, record := range records {
		image := imageMap[record.GameImageID]
		gameImage := GameImageV2{}
		if image.ImageID != "" {
			gameImage = GameImageV2{
				ImageID:   image.ImageID,
				ImageTag:  image.ImageTag,
				ImageUrl:  utils.GetValueByPointer(image.ObjectURL),
				CreatedAt: image.CreatedAt.Format(time.DateTime),
			}
		}
		result = append(result, &ProfileRevisionV2{
			RevisionID:           record.RevisionID,
			ProfileID:            record.ProfileID,
			CPULimit:             record.CPULimit,
			CPURequest:           record.CPURequest,
			MemoryLimit:          record.MemoryLimit,
			MemoryRequest:        record.MemoryRequest,
			GameServerPorts:      GetGameServerPorts(record.GameServerPorts),
			GameServerEntryPoint: GetGameServerEntryPoint(record.EntryPoints),
			GameStartDuration:    record.GameStartDuration,
			GameImage:            gameImage,
			EnvironmentVariables: GetEnvironmentVariables(record.EnvironmentVariables),
			FileConfigs:          GetFileConfigs(record.FileConfigs),
			CreatedAt:            record.CreatedAt.Format(time.DateTime),
		})
	}
	return result, total, nil
}

type ProfileRevisionStatus uint8

const (
	ProfileRevisionStatusBuildingImage        ProfileRevisionStatus = 1 // 正在将你的游戏服务器包build为docker镜像
	ProfileRevisionStatusTesting              ProfileRevisionStatus = 2 // 正在测试你的镜像配置
	ProfileRevisionStatusSuccess              ProfileRevisionStatus = 3 // 成功创建新的镜像配置 只有在此状态下, 才可调用 「应用镜像配置API」 来应用该镜像配置
	ProfileRevisionStatusImageBuildingFailure ProfileRevisionStatus = 4 // 构建镜像失败 可以通过返回的 imageId 调用下方 「获取镜像创建的日志API」 查看镜像创建日志
	ProfileRevisionStatusTestFailure          ProfileRevisionStatus = 5 // 测试镜像配置失败 可以通过返回的 testId 调用下方「查看测试server的运行日志API」 查看测试server日志
	ProfileRevisionStatusImageBuildError      ProfileRevisionStatus = 6 // 镜像构建错误 请稍后重试或者联系我们
)

func CreateProfile(ctx context.Context, req CreateProfileReq) error {
	// 1. 保存profile
	profile := &models.Profile{
		AppID:       req.AppID,
		ProfileName: req.ProfileName,
		MinServers:  uint64(req.MiniServers),
		BufferSize:  req.BufferSize,
	}
	profile.ProfileID = uuid.New().String()
	err := models.CreateProfile(ctx, profile)
	return err
}

type (
	CreateProfileReq struct {
		AppID       string `json:"app_id"`
		ProfileName string `json:"profile_name"`
		MiniServers int    `json:"min_servers"`
		BufferSize  uint8  `json:"buffer_size"` // 取值1~100
	}

	// CreateProfileRevisionResp .
	CreateProfileRevisionResp struct {
		ProfileRevisionId string `json:"profileRevisionId"`
		Status            string `json:"status"`
		TestId            string `json:"testId"`
		Msg               string `json:"msg"`
		Reason            string `json:"reason"`
	}

	GetProfileRevisionStatusResp struct {
		Status  string `json:"status"`
		ImageId string `json:"imageId"`
		TestId  string `json:"testId"`
		Msg     string `json:"msg"`
		Reason  string `json:"reason"`
	}

	/**
	CPU 资源表示
	在 Kubernetes 中，CPU 资源通常以“核心”为单位进行描述，但可以精确到小数点后几位。具体表示方法如下：

	1 核心：可以直接写作 1 或者 1.0。
	0.5 核心（即 500 毫核）：写作 500m，这里的 m 表示 milli（毫），即千分之一。因此 500m 等于 0.5 核心。
	2 核心：写作 2。
	10 核心：写作 10。
	示例：
	500m = 0.5 核心
	1 = 1 核心
	2 = 2 核心
	10 = 10 核心
	📊 内存资源表示
	对于内存资源，Kubernetes 支持多种单位，包括 Ei、Pi、Ti、Gi、Mi、Ki（二进制单位），以及 E、P、T、G、M、K（十进制单位）。不过，在实际应用中，最常用的是 Mi（兆字节）和 Gi（吉字节）。

	256Mi：表示 256 Mebibytes (MiB)，等于
	2
	20
	2
	20
	  字节，约等于 268,435,456 字节。
	注意：Mi 是二进制单位，而 MB 是十进制单位。虽然它们非常接近，但在技术上有所不同：

	1 MB =
	10
	6
	10
	6
	  字节 = 1,000,000 字节
	1 MiB =
	2
	20
	2
	20
	  字节 = 1,048,576 字节
	因此，当你看到 256Mi，它实际上指的是 256 Mebibytes，而不是 Megabytes。如果你想要使用更常见的 MB 单位，你应该使用 256M，但这并不是 Kubernetes 推荐的做法，因为这可能会导致一些混淆。

	示例：
	256Mi = 256 Mebibytes ≈ 268.4 Megabytes
	若要明确表达 256 Megabytes，则应使用 256M，但建议尽量使用二进制单位如 Mi 来避免混淆。
	*/

	// CreateProfileRevisionReq 顶层结构
	CreateProfileRevisionReq struct {
		ProfileID            string            `json:"profile_id"`     //
		CPULimit             string            `json:"cpu_limit"`      // 0.1核
		CPURequest           string            `json:"cpu_request"`    //
		MemoryLimit          string            `json:"memory_limit"`   // 153Mi
		MemoryRequest        string            `json:"memory_request"` //
		GPULimit             string            `json:"gpu_limit"`
		GPURequest           string            `json:"gpu_request"`
		GameServerPorts      []ServerPort      `json:"game_server_ports"`       //
		GameServerEntryPoint []string          `json:"game_server_entry_point"` //
		GameStartDuration    int64             `json:"game_start_duration"`     //
		GameImageID          string            `json:"game_image_id"`
		EnvironmentVariables map[string]string `json:"environment_variables"` //
		FileConfigs          []FileConfigV2    `json:"file_configs"`
	}

	// UpdProfileRevisionReq .
	UpdProfileRevisionReq struct {
		RevisionID string `json:"revision_id"`
		CreateProfileRevisionReq
	}
)

package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/k8s"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type CreateAllocationReq struct {
	AppID         string `json:"app_id"`
	UUID          string `json:"uuid"`           // 用于识别该分配的UUID，这个UUID由服务开发者生成，用于之后调取服务分配的细节或移除分配，
	ProfileID     string `json:"profile_id"`     // 待分配服务所属的profileID
	RegionID      string `json:"region_id"`      // 待分配服务所属的regionID
	AllocationTTL int64  `json:"allocation_ttl"` // 服务器超时时长（默认1小时）
	Envs          []struct {
		Key   string `json:"key"`   // 环境变量key
		Value string `json:"value"` // 环境变量value
	} `json:"envs"` // 传递给服务的变量
}

type Allocation struct {
	ID              uint64            `json:"id"`
	AllocationID    string            `json:"allocation_id"`
	AppID           string            `json:"app_id"`
	ProfileID       string            `json:"profile_id"`
	RevisionID      string            `json:"revision_id"`
	RegionID        string            `json:"region_id"`
	Ip              string            `json:"ip"`
	GameServerName  string            `json:"game_server_name"`
	CreatedAt       string            `json:"created_at"`
	Status          string            `json:"status"`
	Msg             string            `json:"msg"`
	AllocationTTL   int64             `json:"allocationTTL"`
	ProfileRevision ProfileRevisionV2 `json:"profile_revision"`
	RegionName      string            `json:"region_name"`
	ProfileName     string            `json:"profile_name"`
	Namespace       string            `json:"namespace"`
	GssTemplate     string            `json:"gss_template"`
}

type ServerInfo struct {
	ID             uint64 `json:"id"`
	AllocationID   string `json:"allocation_id"`
	AppID          string `json:"app_id"`
	ProfileID      string `json:"profile_id"`
	RevisionID     string `json:"revision_id"`
	RegionID       string `json:"region_id"`
	Ip             string `json:"ip"`
	GameServerName string `json:"game_server_name"`
	CreatedAt      string `json:"created_at"`
	Status         string `json:"status"`
	AllocationTTL  int64  `json:"allocationTTL"`
	RegionName     string `json:"region_name"`
	ProfileName    string `json:"profile_name"`
}

func (a *Allocation) GetGssName() string {
	return fmt.Sprintf("%s-%d", a.GssTemplate, a.ID)
}

// CreateAllocation 创建allocation
func CreateAllocation(ctx context.Context, req CreateAllocationReq) error {
	if req.AllocationTTL == 0 {
		req.AllocationTTL = 3600
	}
	envsBytes, err := json.Marshal(req.Envs)
	if err != nil {
		return err
	}
	profile, err := GetProfile(req.ProfileID)
	if err != nil {
		return err
	}
	if profile.ReleasedRevision == nil {
		return fmt.Errorf("profile %s has no released revision", req.ProfileID)
	}
	revisionBytes, err := json.Marshal(profile.ReleasedRevision)
	if err != nil {
		return err
	}
	allocationID := utils.GetUUID()
	namespace := profile.ReleasedRevision.Namespace
	if namespace == "" {
		namespace = "default"
	}
	allocation := &models.Allocation{
		AppID:           req.AppID,
		AllocationID:    allocationID,
		ProfileID:       req.ProfileID,
		RevisionID:      profile.ReleasedRevision.RevisionID,
		ProfileRevision: revisionBytes,
		RegionID:        req.RegionID,
		Envs:            envsBytes,
		AllocationTTL:   req.AllocationTTL,
		Status:          models.AllocationStatusCreated,
		Namespace:       namespace,
		GssTemplate:     profile.ReleasedRevision.GetGssTemplate(),
	}
	_, err = models.CreateAllocation(allocation)
	if err != nil {
		return err
	}
	//err = DeployGameServer(ctx, profile.ProfileID)
	err = DeployGameServerV2(ctx, profile.ProfileID, allocationID)
	if err != nil {
		// 创建服务器失败 修改状态
		_ = models.UpdateAllocationStatus(allocationID, models.AllocationStatusFailed)
		return err
	}
	return nil
}

// BindRoom 绑定房间
func BindRoom(ctx context.Context, allocationID, roomID string) error {
	if allocationID == "" || roomID == "" {
		return errors.New("allocationID or roomID is empty")
	}
	err := models.BindRoom(ctx, allocationID, roomID)
	return err
}

// ListUnbindAllocations 获取未绑定的分配服务器
func ListUnbindAllocations(ctx context.Context, appID string, offset, limit int) ([]*Allocation, error) {
	allocations := make([]*Allocation, 0)

	records, err := models.ListAllocations(appID, offset, limit)
	if err != nil {
		return nil, err
	}
	for _, record := range records {
		if record.RoomID != "" {
			continue
		}
		revision := ProfileRevisionV2{}
		err = json.Unmarshal(record.ProfileRevision, &revision)
		if err != nil {
			log.Errorf("unmarshal profile revision error: %v", err)
			continue
		}
		allocations = append(allocations, &Allocation{
			ID:              record.ID,
			AllocationID:    record.AllocationID,
			AppID:           record.AppID,
			ProfileID:       record.ProfileID,
			RevisionID:      record.RevisionID,
			RegionID:        record.RegionID,
			Ip:              "", // TODO 获取ip
			GameServerName:  "", // TODO 获取游戏服名称
			CreatedAt:       record.CreatedAt.Format(time.DateTime),
			Status:          models.GetAllocationStatus(record.Status),
			Msg:             "", // TODO 获取错误信息
			AllocationTTL:   record.AllocationTTL,
			ProfileRevision: revision,
			RegionName:      "todo",
			ProfileName:     "todo",
			Namespace:       record.Namespace,
			GssTemplate:     record.GssTemplate,
		})
	}
	if err != nil {
		return nil, err
	}
	return allocations, nil
}

func ListAllocations(ctx context.Context, appID string, offset, limit int) (int64, []*Allocation, error) {
	allocations := make([]*Allocation, 0)

	records, err := models.ListAllocations(appID, offset, limit)
	if err != nil {
		return 0, nil, err
	}
	//_, regionMap := GetAllRegions()
	for _, record := range records {
		revision := ProfileRevisionV2{}
		err = json.Unmarshal(record.ProfileRevision, &revision)
		if err != nil {
			log.Errorf("unmarshal profile revision error: %v", err)
			continue
		}
		allocations = append(allocations, &Allocation{
			ID:              record.ID,
			AllocationID:    record.AllocationID,
			AppID:           record.AppID,
			ProfileID:       record.ProfileID,
			RevisionID:      record.RevisionID,
			RegionID:        record.RegionID,
			Ip:              "", // TODO 获取ip
			GameServerName:  "", // TODO 获取游戏服名称
			CreatedAt:       record.CreatedAt.Format(time.DateTime),
			Status:          models.GetAllocationStatus(record.Status),
			Msg:             "", // TODO 获取错误信息
			AllocationTTL:   record.AllocationTTL,
			ProfileRevision: revision,
			RegionName:      "todo",
			ProfileName:     "todo",
			Namespace:       record.Namespace,
			GssTemplate:     record.GssTemplate,
		})
	}
	total, err := models.CountAllocations(ctx, appID)
	if err != nil {
		return 0, nil, err
	}
	return total, allocations, nil
}

func GetAllocation(allocationID string) (*Allocation, error) {
	if allocationID == "" {
		return nil, errors.New("allocationID is empty")
	}
	record, err := models.GetAllocationByID(allocationID)
	if err != nil {
		return nil, err
	}
	revision := ProfileRevisionV2{}
	err = json.Unmarshal(record.ProfileRevision, &revision)
	if err != nil {
		return nil, err
	}
	profile, err := GetProfile(revision.ProfileID)
	if err != nil {
		return nil, err
	}
	allocation := &Allocation{
		ID:              record.ID,
		AllocationID:    record.AllocationID,
		AppID:           record.AppID,
		ProfileID:       record.ProfileID,
		RevisionID:      record.RevisionID,
		RegionID:        record.RegionID,
		Ip:              "", // TODO: 获取ip
		GameServerName:  "", // TODO: 获取游戏服务器名称
		CreatedAt:       record.CreatedAt.Format(time.DateTime),
		Status:          models.GetAllocationStatus(record.Status),
		Msg:             "", // TODO: 获取错误信息
		AllocationTTL:   record.AllocationTTL,
		ProfileRevision: revision,
		RegionName:      record.RegionID, // TODO: 获取regionName
		ProfileName:     profile.ProfileName,
		Namespace:       record.Namespace,
		GssTemplate:     record.GssTemplate,
	}
	return allocation, nil
}

/**
{
  "allocation": {
    "uuid": "string",
    "gameId": "string",
    "profileId": "string",
    "regionId": "string",
    "ip": "string",
    "gameServerPorts": [
      {
        "port": 9998,
        "protocol": "UDP",
        "name": "rest"
      }
    ],
    "gameServerName": "string",
    "createdAt": "2023-01-04T03:06:13.107Z",
    "createdByUser": "string",
    "modifiedAt": "2023-01-04T03:06:13.107Z",
    "modifiedByUser": "string",
    "fulfilledAt": "2023-01-04T03:06:13.107Z",
    "deletedAt": "2023-01-04T03:06:13.107Z",
    "deletedByUser": "string",
    "status": "string",
    "msg": "string",
    "allocationTTL": "string",
    "profileName": "string",
    "regionName": "string"
  }
}
*/

// AllocationCallbackUrl 一旦你配置了allocationCallbackUrl，分配完成时，就会回调你配置的 allocationCallbackUrl, 将 allocation 信息发送过去。
func AllocationCallbackUrl() {

}

// RemoveAllocation 删除 Allocation
func RemoveAllocation(ctx context.Context, allocationID string) error {
	allocation, err := GetAllocation(allocationID)
	if err != nil {
		return err
	}
	err = models.DeleteAllocation(allocationID)
	if err != nil {
		return err
	}

	// 创建客户端
	cfg, err := k8s.GetRestConfig()
	if err != nil {
		return err
	}
	cli, err := client.New(cfg, client.Options{Scheme: scheme})
	if err != nil {
		return err
	}

	err = DeleteAllocation(ctx, cli, allocation)
	if err != nil {
		return err
	}
	_ = models.UpdateAllocationStatus(allocationID, models.AllocationStatusDeallocated)
	return nil
}

// MarkReady 标记 Allocation 为就绪
func MarkReady(allocationID string) error {
	return models.MarkReady(allocationID)
}

// GetEnvs 获取 Allocation 的环境变量
func GetEnvs(allocationID string) (string, error) {
	if allocationID == "" {
		return "", errors.New("allocationID is empty")
	}
	allocation, err := models.GetAllocationByID(allocationID)
	if err != nil {
		return "", err
	}

	return string(allocation.Envs), nil
}

type ExpiredAt struct {
	ExpiredAt string `json:"expired_at"`
	Expired   bool   `json:"expired"`
}

func Shutdown(allocationID string) error {
	if allocationID == "" {
		return errors.New("allocationID is empty")
	}
	allocation, err := models.GetAllocationByID(allocationID)
	fmt.Println(allocation.AllocationID)
	if err != nil {
		return err
	}
	_ = models.UpdateAllocationStatus(allocationID, models.AllocationStatusDeallocated)
	// TODO 删除相关的服务器, 关机操作

	return nil
}

// GetExpiredAt 获取 Allocation 的过期时间
func GetExpiredAt(allocationID string) (*ExpiredAt, error) {
	if allocationID == "" {
		return nil, errors.New("allocationID is empty")
	}
	allocation, err := models.GetAllocationByID(allocationID)
	if err != nil {
		return nil, err
	}
	check, _ := utils.InArray2[uint8](uint8(allocation.Status), []uint8{
		uint8(models.AllocationStatusCanceled),
		uint8(models.AllocationStatusUnhealthy),
		uint8(models.AllocationStatusFailed),
		uint8(models.AllocationStatusDeallocated),
	})
	if check {
		return &ExpiredAt{
			Expired:   true,
			ExpiredAt: allocation.ExpiredAt.Format(time.DateTime),
		}, nil
	}

	// 自动延期逻辑：距离过期<10分钟时自动续期 TODO CHECK
	if time.Until(allocation.ExpiredAt) < 10*time.Minute {
		newExpiry := time.Now().Add(1 * time.Hour)
		_ = models.SetExpiredAt(allocationID, newExpiry)
	}

	return &ExpiredAt{
		Expired:   true,
		ExpiredAt: allocation.ExpiredAt.Format(time.DateTime),
	}, nil
}

// GetServerInfo 获取 Allocation 的服务器信息
func GetServerInfo(allocationID string) (*ServerInfo, error) {
	if allocationID == "" {
		return nil, errors.New("allocationID is empty")
	}
	record, err := models.GetAllocationByID(allocationID)
	if err != nil {
		return nil, err
	}
	revision := ProfileRevisionV2{}
	err = json.Unmarshal(record.ProfileRevision, &revision)
	if err != nil {
		return nil, err
	}
	profile, err := GetProfile(revision.ProfileID)
	if err != nil {
		return nil, err
	}
	allocation := &ServerInfo{
		ID:             record.ID,
		AllocationID:   record.AllocationID,
		AppID:          record.AppID,
		ProfileID:      record.ProfileID,
		RevisionID:     record.RevisionID,
		RegionID:       record.RegionID,
		Ip:             record.IP,
		GameServerName: record.GameServerName,
		CreatedAt:      record.CreatedAt.Format(time.DateTime),
		Status:         models.GetAllocationStatus(record.Status),
		AllocationTTL:  record.AllocationTTL,
		RegionName:     record.RegionID,
		ProfileName:    profile.ProfileName,
	}
	return allocation, nil
}

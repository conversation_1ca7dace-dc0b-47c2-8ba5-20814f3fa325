package service

import (
	"context"
	"fmt"
	"github.com/sirupsen/logrus"

	gamev1alpha1 "github.com/openkruise/kruise-game/apis/v1alpha1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
)

func init() {
	log.SetLogger(zap.New(zap.UseDevMode(true))) // 开启开发模式日志输出
}

// DeleteGameServerSet 简单删除游戏服务器
func DeleteGameServerSet(ctx context.Context, cli client.Client, allocation *Allocation) error {
	// 1. 删除GameServerSet主资源
	gssName := allocation.GetGssName()
	gss := &gamev1alpha1.GameServerSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      gssName,
			Namespace: allocation.Namespace,
		},
	}

	err := cli.Delete(ctx, gss)
	if err != nil {
		// 如果资源不存在则忽略错误
		if !apierrors.IsNotFound(err) {
			return fmt.Errorf("删除GameServerSet失败: %w", err)
		}
	}

	// 2. 删除关联配置文件（可选，建议执行）
	if err := deleteConfigMaps(ctx, cli, allocation.Namespace, gssName); err != nil {
		logrus.Printf("警告: 删除配置文件失败，忽略: %v", err)
	}

	return nil
}

// DeleteAllocation 删除服务器
func DeleteAllocation(ctx context.Context, cli client.Client, allocation *Allocation) error {
	return DeleteGameServerSet(ctx, cli, allocation)
}

// 简单删除配置文件
func deleteConfigMaps(ctx context.Context, cli client.Client, namespace, gssName string) error {
	// 通过标签匹配选择配置文件
	configMaps := &corev1.ConfigMapList{}
	err := cli.List(ctx, configMaps, client.InNamespace(namespace),
		client.MatchingLabels{"owner": gssName})

	if err != nil {
		return fmt.Errorf("获取配置文件失败: %w", err)
	}

	// 批量删除配置文件
	for i := range configMaps.Items {
		if err := cli.Delete(ctx, &configMaps.Items[i]); err != nil {
			logrus.Printf("删除ConfigMap %s 失败: %v", configMaps.Items[i].Name, err)
		}
	}

	return nil
}

package service

import (
	"context"
	"fmt"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
	"github.com/pkg/errors"
)

/**
可参考下方流程在 Multiverse 服务中集成房间管理的相关功能:

1. 在 游戏客户端/游戏大厅 调用 「创建房间」
	1.1 房间创建成功后可在 Multiverse 服务端SDK中调用 GetServerInfoAsync 方法获取房间ID
2. 房主调用「加入房间」进行预加入房间
3. Multiverse Server 等待房主加入服务器后， 调用「确认房间加入」确认房主加入成功
4. Multiverse Server 程序等房主加入成功以及服务器程序初始化成功后，调用「更新房间状态」将房间状态更新成已就绪("READY")
5. 游戏客户端/游戏大厅 可调用「查询房间列表」查询所有状态为已就绪("READY")房间
6. 玩家调用「加入房间」进行预加入房间
7. Multiverse Server 等待玩家加入服务器后， 调用 「确认房间加入」确认玩家加入成功
8. 待房间满员或无法加入新玩家后， Multiverse Server 调用「更新房间状态」将房间状态更新成运行中("RUNNING")
9. Multiverse Server 或 游戏客户端 在有游戏玩家离开房间时调用「离开房间」
10. Multiverse Server 可以调用「更新房间」来更换房主或更新一些私有的Properties
*/

type Room struct {
	RoomID           string            `json:"room_id"`
	ProfileID        string            `json:"profile_id"`
	AppID            string            `json:"app_id"`
	Type             string            `json:"type"`
	OwnerID          string            `json:"owner_id"`
	Namespace        string            `json:"namespace"`
	Name             string            `json:"name"`
	JoinCode         string            `json:"join_code"`
	Visibility       string            `json:"visibility"`
	MaxPlayers       uint64            `json:"max_players"`
	RoomTTLInMinutes uint64            `json:"room_ttl_in_in_minutes"`
	ExpirationTime   uint64            `json:"expiration_time"` //时间戳
	ExternalUniqueID string            `json:"external_unique_id"`
	CreatedAt        string            `json:"created_at"`
	PlayerCount      uint64            `json:"player_count"`
	CustomProperties map[string]string `json:"custom_properties"`
}

// ListRooms 查询房间列表
func ListRooms(ctx context.Context, page int, pageSize int, roomStatus int64) ([]*Room, int64, error) {
	records, total, err := models.ListRooms(page, pageSize, models.RoomStatus(roomStatus))
	if err != nil {
		return nil, 0, err
	}
	rooms := make([]*Room, 0, len(records))
	for _, record := range records {
		rooms = append(rooms, &Room{
			RoomID:           record.RoomID,
			ProfileID:        record.ProfileRevisionID, // TODO
			AppID:            record.AppID,
			Type:             "", // TODO
			OwnerID:          record.OwnerID,
			Namespace:        record.Namespace,
			Name:             record.RoomName,
			JoinCode:         record.RoomCode,
			Visibility:       string(record.Visibility),
			MaxPlayers:       record.MaxPlayers,
			RoomTTLInMinutes: record.TTLMinutes,
			ExpirationTime:   record.ExpirationTime,
			ExternalUniqueID: "", // todo
			CreatedAt:        record.CreatedAt.Format(time.DateTime),
			PlayerCount:      record.CurrentPlayers,
			CustomProperties: nil,
		})
	}
	return rooms, total, nil
}

// CreateRoom 创建房间
func CreateRoom(ctx context.Context, req *Room) error {
	lockKey := fmt.Sprintf("lock:room:%s", req.AppID)
	lockVal, err := dao.AcquireLock(ctx, lockKey, 10*time.Second)
	if err != nil {
		return err
	}
	defer dao.ReleaseLock(ctx, lockKey, lockVal)
	// TODO 房间依赖于服务器，如果没有可用的服务器，是否主动先创建服务器
	// 查找可用服务器
	allocations, err := ListUnbindAllocations(ctx, req.AppID, 0, 100)
	if err != nil {
		return err
	}
	if len(allocations) == 0 {
		return errors.New("no available allocations")
	}

	visibility := models.Visibility(req.Visibility)
	if visibility != models.VisibilityPublic && visibility != models.VisibilityPrivate {
		return errors.New("invalid visibility")
	}
	if req.MaxPlayers <= 0 || req.MaxPlayers > 1000 {
		return errors.New("invalid max players")
	}

	roomID := utils.GetUUID()
	room := &models.Room{
		RoomID:            roomID,
		ProfileRevisionID: "",
		AppID:             req.AppID,
		OwnerID:           req.OwnerID,
		Namespace:         req.Namespace,
		RoomName:          req.Name,
		RoomCode:          req.JoinCode,
		Visibility:        visibility,
		MaxPlayers:        req.MaxPlayers,
		CurrentPlayers:    0,
		Status:            models.RoomStatusCreated,
		TTLMinutes:        req.RoomTTLInMinutes,
		ExpirationTime:    req.ExpirationTime,
		AllocationEnvs:    nil,
	}
	err = models.CreateRoom(room)
	if err != nil {
		return err
	}
	// 关联绑定服务器和房间
	// TODO 需要加锁 保证绑定数据正常
	err = BindRoom(ctx, allocations[0].AllocationID, roomID)
	if err != nil {
		return err
	}
	return nil
}

// GetRoom 查询房间
func GetRoom(ctx context.Context, roomID string) (*Room, error) {
	record, err := models.GetRoomByID(roomID)
	if err != nil {
		return nil, err
	}
	room := &Room{
		RoomID:           record.RoomID,
		ProfileID:        record.ProfileRevisionID, // TODO
		AppID:            record.AppID,
		Type:             "", // TODO
		OwnerID:          record.OwnerID,
		Namespace:        record.Namespace,
		Name:             record.RoomName,
		JoinCode:         record.RoomCode,
		Visibility:       string(record.Visibility),
		MaxPlayers:       record.MaxPlayers,
		RoomTTLInMinutes: record.TTLMinutes,
		ExpirationTime:   record.ExpirationTime,
		ExternalUniqueID: "", // todo
		CreatedAt:        record.CreatedAt.Format(time.DateTime),
		PlayerCount:      record.CurrentPlayers,
		CustomProperties: nil,
	}
	return room, nil
}

// UpdRoom 更新房间(推荐服务端调用)
func UpdRoom() {

}

// ConfirmJoinRoom 确认加入房间(推荐服务端调用)
func ConfirmJoinRoom(ctx context.Context, userID, roomID string) error {
	room, err := GetRoom(ctx, roomID)
	if err != nil {
		return err
	}
	if err = CanJoinRoom(userID, roomID); err != nil {
		return err
	}
	// 房间是否超员
	if room.PlayerCount >= room.MaxPlayers {
		return errors.New("房间已满员")
	}

	return nil
}

func CanJoinRoom(userID, roomID string) error {
	return nil
}

// DynamicInfo 上报房间信息（该功能需联系后台开通）
func DynamicInfo() {

}

// JoinRoom 加入房间(推荐客户端调用) todo 事务
func JoinRoom(ctx context.Context, playerID, roomID, joinCode string) error {
	room, err := GetRoom(ctx, roomID)
	if err != nil {
		return err
	}
	// 房间是否超员
	if room.PlayerCount >= room.MaxPlayers {
		return errors.New("房间已满员")
	}
	if room.JoinCode != "" && room.JoinCode != joinCode {
		return errors.New("invalid join code")
	}
	// 添加到房间成员
	member := &models.RoomMember{
		UserID:      playerID,
		RoomID:      roomID,
		JoinTime:    time.Now(),
		Status:      models.RoomMemberStatusJoin,
		DynamicInfo: nil,
	}
	// 更新房间成员数量
	err = models.IncrRoomPlayers(roomID, 1)
	if err != nil {
		return err
	}
	return models.AddRoomMember(member)
}

// LeaveRoom 离开房间 todo 事务
func LeaveRoom(ctx context.Context, playerID, roomID string) error {
	_, err := GetRoom(ctx, roomID)
	if err != nil {
		return err
	}
	exist, err := models.InRoom(playerID, roomID)
	if err != nil {
		return err
	}
	if !exist {
		return errors.New("not in room")
	}
	// 更新房间成员数量
	err = models.IncrRoomPlayers(roomID, -1)
	if err != nil {
		return err
	}
	err = models.LeaveRoom(roomID, playerID)
	return err
}

// UpdRoomStatus 更新房间状态
func UpdRoomStatus() {

}

// GetAvailableRooms 获取可快速加入的房间列表（需要和上报房间实时人数功能结合使用）
func GetAvailableRooms(ctx context.Context, appID string) ([]Room, error) {
	records, err := models.GetAvailableRooms(appID)
	if err != nil {
		return nil, err
	}
	var rooms []Room
	for _, record := range records {
		rooms = append(rooms, Room{
			RoomID:           record.RoomID,
			ProfileID:        record.ProfileID,
			AppID:            record.AppID,
			Type:             "",
			OwnerID:          record.OwnerID,
			Namespace:        record.Namespace,
			Name:             record.RoomName,
			JoinCode:         record.RoomCode,
			Visibility:       string(record.Visibility),
			MaxPlayers:       record.MaxPlayers,
			RoomTTLInMinutes: record.TTLMinutes,
			ExpirationTime:   record.ExpirationTime,
			ExternalUniqueID: "",
			CreatedAt:        record.CreatedAt.Format(time.DateTime),
			PlayerCount:      record.CurrentPlayers,
			CustomProperties: nil,
		})
	}
	return rooms, nil
}

func GetRooms(ctx context.Context, appID string, offset, limit int) ([]Room, int64, error) {
	records, cnt, err := models.GetRooms(appID, offset, limit)
	if err != nil {
		return nil, 0, err
	}

	var rooms []Room
	for _, record := range records {
		rooms = append(rooms, Room{
			RoomID:           record.RoomID,
			ProfileID:        record.ProfileID,
			AppID:            record.AppID,
			Type:             "",
			OwnerID:          record.OwnerID,
			Namespace:        record.Namespace,
			Name:             record.RoomName,
			JoinCode:         record.RoomCode,
			Visibility:       string(record.Visibility),
			MaxPlayers:       record.MaxPlayers,
			RoomTTLInMinutes: record.TTLMinutes,
		})
	}
	return rooms, cnt, nil
}

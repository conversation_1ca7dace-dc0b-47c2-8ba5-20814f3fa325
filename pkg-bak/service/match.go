package service

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
)

type MatchDetail struct {
	MatchId             string          `json:"match_id"`
	AppId               string          `json:"app_id"`
	Name                string          `json:"name"`
	MatchDefinition     MatchDefinition `json:"match_definition"`
	TicketTTL           int             `json:"ticket_ttl"`
	AutoBackFillEnabled bool            `json:"auto_back_fill_enabled"`
	DedicatedRoomInfo   struct {
		ProfileId        string            `json:"profile_id"`
		RoomTTLInMinutes int               `json:"room_ttl_in_minutes"`
		AllocationEnvs   map[string]string `json:"allocation_envs"`
	} `json:"dedicated_room_info"`
	CreatedAt  string            `json:"created_at"`
	Properties map[string]string `json:"properties"`
}

type MatchDefinition struct {
	PlayerAttributes []PlayerAttribute `json:"player_attributes"`
	TeamDefinitions  []TeamDefinition  `json:"team_definitions"`
	Rules            []Rule            `json:"rules"`
	Expansions       []Expansion       `json:"expansions"`
}

type PlayerAttribute struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

type TeamDefinition struct {
	Name       string `json:"name"`
	MinPlayers int    `json:"min_players"`
	MaxPlayers int    `json:"max_players"`
	MinTeams   int    `json:"min_teams"`
	MaxTeams   int    `json:"max_teams"`
}

type Rule struct {
	ID           uint64 `json:"id"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	Type         string `json:"type"`
	Reference    string `json:"reference"`
	Operation    string `json:"operation"`
	Measurements string `json:"measurements"`
	MaxDistance  int    `json:"max_distance"`
}

// Expansion 拓展
type Expansion struct {
	Description string `json:"description"`
	Target      string `json:"target"`
	Steps       []Step `json:"steps"`
}

type Step struct {
	WaitTimeSeconds int    `json:"waitTime_seconds"`
	Value           string `json:"value"`
	Type            string `json:"type"`
}

// GetMatchRules .
func GetMatchRules() ([]*Rule, int64, error) {
	records, cnt, err := models.NewMatchRuleRepo(dao.Db).FindAll(1, 20)
	if err != nil {
		return nil, 0, err
	}
	var result []*Rule
	for _, record := range records {
		result = append(result, &Rule{
			ID:           record.ID,
			Name:         record.RuleName,
			Description:  record.Description,
			Type:         record.RuleType,
			Reference:    record.Reference,
			Operation:    record.Operation,
			Measurements: record.Measurement,
			MaxDistance:  record.MaxDistance,
		})
	}
	return result, cnt, nil
}

// GetMatchTemplates 查询匹配模板
func GetMatchTemplates() ([]*models.MatchTemplate, error) {
	return nil, nil
}

func GetMatch() (*MatchDetail, error) {
	match := &MatchDetail{
		MatchId: "",
		AppId:   "",
		Name:    "",
		MatchDefinition: MatchDefinition{
			PlayerAttributes: []PlayerAttribute{
				{
					Name: "mode",
					Type: "string",
				},
			},
			TeamDefinitions: []TeamDefinition{
				{
					Name:       "team1",
					MinPlayers: 1,
					MaxPlayers: 1,
					MinTeams:   1,
					MaxTeams:   1,
				},
			},
			Rules: []Rule{
				{
					Name:         "teamRankRule",
					Description:  "team内所有玩家的段位积分之差不超过10",
					Type:         "distance",
					Reference:    "",
					Operation:    "",
					Measurements: "teams[*].players.attributes[rankLevel]",
					MaxDistance:  10,
				},
			},
			Expansions: []Expansion{
				{
					Description: "两个team的总分之差 - 动态更新",
					Target:      "rules[teamSumRule].maxDistance",
					Steps: []Step{
						{
							WaitTimeSeconds: 10,
							Value:           "40",
							Type:            "replace",
						},
						{
							WaitTimeSeconds: 20,
							Type:            "disable",
						},
					},
				},
			},
		},
		TicketTTL:           0,
		AutoBackFillEnabled: false,
		DedicatedRoomInfo: struct {
			ProfileId        string            `json:"profile_id"`
			RoomTTLInMinutes int               `json:"room_ttl_in_minutes"`
			AllocationEnvs   map[string]string `json:"allocation_envs"`
		}(struct {
			ProfileId        string
			RoomTTLInMinutes int
			AllocationEnvs   map[string]string
		}{}),
		CreatedAt: "",
		Properties: map[string]string{
			"test": "test",
		},
	}
	return match, nil
}

func GetMatchList(page, pageSize int) ([]*MatchDetail, error) {
	match := &MatchDetail{
		MatchId: "",
		AppId:   "",
		Name:    "",
		MatchDefinition: MatchDefinition{
			PlayerAttributes: []PlayerAttribute{
				{
					Name: "mode",
					Type: "string",
				},
			},
			TeamDefinitions: []TeamDefinition{
				{
					Name:       "team1",
					MinPlayers: 1,
					MaxPlayers: 1,
					MinTeams:   1,
					MaxTeams:   1,
				},
			},
			Rules: []Rule{
				{
					Name:         "teamRankRule",
					Description:  "team内所有玩家的段位积分之差不超过10",
					Type:         "distance",
					Reference:    "",
					Operation:    "",
					Measurements: "teams[*].players.attributes[rankLevel]",
					MaxDistance:  10,
				},
			},
			Expansions: []Expansion{
				{
					Description: "两个team的总分之差 - 动态更新",
					Target:      "rules[teamSumRule].maxDistance",
					Steps: []Step{
						{
							WaitTimeSeconds: 10,
							Value:           "40",
							Type:            "replace",
						},
						{
							WaitTimeSeconds: 20,
							Type:            "disable",
						},
					},
				},
			},
		},
		TicketTTL:           0,
		AutoBackFillEnabled: false,
		DedicatedRoomInfo: struct {
			ProfileId        string            `json:"profile_id"`
			RoomTTLInMinutes int               `json:"room_ttl_in_minutes"`
			AllocationEnvs   map[string]string `json:"allocation_envs"`
		}(struct {
			ProfileId        string
			RoomTTLInMinutes int
			AllocationEnvs   map[string]string
		}{}),
		CreatedAt: "",
		Properties: map[string]string{
			"test": "test",
		},
	}
	matches := []*MatchDetail{
		match,
	}
	return matches, nil
}

func DeleteMatch() error {
	return nil
}

type CreateMatchReq struct {
	Name                string          `json:"name"`
	MatchDefinition     MatchDefinition `json:"match_definition"`
	TicketTTL           int             `json:"ticket_ttl"`
	TemplateId          int             `json:"template_id"`
	ProfileId           string          `json:"profile_id"`
	AutoBackFillEnabled bool            `json:"auto_back_fill_enabled"`
}

// CreateMatch 创建匹配
func CreateMatch(req CreateMatchReq) error {
	return nil
}

type UpdMatchReq struct {
	MatchID string `json:"match_id"`
	CreateMatchReq
}

// UpdMatch 创建匹配
func UpdMatch(req *UpdMatchReq) error {
	return nil
}

// MatchTest 匹配测试
func MatchTest() error {
	return nil
}

/**
{
    "matches": [],
    "unmatchedTickets": [],
    "incompatibleTickets": [
        {
            "id": "19276775-7be5-4de9-a818-9ceb4432700b",
            "players": [
                {
                    "id": "0",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        },
        {
            "id": "ad18bf57-9c1b-47ca-b9c4-e1078bbf6b65",
            "players": [
                {
                    "id": "4",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        },
        {
            "id": "3883135e-d8a1-4df1-8742-01ff745889ac",
            "players": [
                {
                    "id": "9",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        },
        {
            "id": "6ab0c886-a118-458a-9fac-9d2fff38bd0d",
            "players": [
                {
                    "id": "2",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        },
        {
            "id": "6d09daf1-b96b-4acd-b805-2fff11b55bbf",
            "players": [
                {
                    "id": "6",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        },
        {
            "id": "cf512855-1af7-4ba6-930c-646a37632d6a",
            "players": [
                {
                    "id": "5",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        },
        {
            "id": "fcbcd856-8d42-40e1-90d4-7a768aca449f",
            "players": [
                {
                    "id": "3",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        },
        {
            "id": "71b7a9b8-4c2b-4849-9d16-15d23b372142",
            "players": [
                {
                    "id": "8",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        },
        {
            "id": "d5ad2681-9ee2-4cd4-8ae2-a34e79e0abe4",
            "players": [
                {
                    "id": "1",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        },
        {
            "id": "7294a34f-a8cd-49b5-a1a3-67688cf1ab11",
            "players": [
                {
                    "id": "7",
                    "attributes": {
                        "mode": "1"
                    }
                }
            ],
            "errMsg": "Ticket is incompatible with the config [b50d960b-70c5-4ff5-a616-07b5aa8db70a]"
        }
    ]
}
*/

type Category struct {
	ID            uint64        `json:"id"`
	Name          string        `json:"name"`
	Level         uint64        `json:"level"`
	Description   string        `json:"description"`
	BackgroundImg string        `json:"background_img"`
	CreatedAt     string        `json:"created_at"`
	SubCategories []SubCategory `json:"sub_categories"`
}

type SubCategory struct {
	ID                     uint64 `json:"id"`
	Name                   string `json:"name"`
	Level                  uint64 `json:"level"`
	Description            string `json:"description"`
	DefaultMatchDefinition string `json:"default_match_definition"`
	CreatedAt              string `json:"created_at"`
}

func GetTemplates() ([]Category, error) {
	var categories []Category
	parents, err := models.GetTemplateByLevel(1, 0)
	if err != nil {
		return categories, err
	}

	for _, parent := range parents {
		categories = append(categories, Category{
			ID:            parent.ID,
			Name:          parent.Name,
			Level:         parent.Level,
			Description:   parent.Description,
			BackgroundImg: parent.BackgroundImg,
			CreatedAt:     parent.CreatedAt.Format("2006-01-02 15:04:05"),
			SubCategories: []SubCategory{},
		})
	}
	for i, category := range categories {
		children, err := models.GetTemplateByLevel(2, category.ID)
		if err != nil {
			return categories, err
		}
		for _, child := range children {
			categories[i].SubCategories = append(category.SubCategories, SubCategory{
				ID:                     child.ID,
				Name:                   child.Name,
				Level:                  child.Level,
				Description:            child.Description,
				DefaultMatchDefinition: string(child.DefaultMatchDefinition),
				CreatedAt:              child.CreatedAt.Format("2006-01-02 15:04:05"),
			})
		}
	}
	return categories, nil
}

package authorization

import (
	"encoding/base64"
	"strings"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
	"github.com/gin-gonic/gin"
)

// AuthMiddleware 带缓存的认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 获取 Authorization 头
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.AbortWithStatusJSON(401, gin.H{"error": "Authorization header required"})
			return
		}

		// 2. 验证头格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Basic" {
			c.AbortWithStatusJSON(401, gin.H{"error": "Invalid authorization format"})
			return
		}

		// 3. Base64 解码
		decoded, err := base64.StdEncoding.DecodeString(parts[1])
		if err != nil {
			c.AbortWithStatusJSON(401, gin.H{"error": "Invalid base64 encoding"})
			return
		}

		// 4. 分割凭证
		creds := strings.SplitN(string(decoded), ":", 2)
		if len(creds) != 2 {
			c.AbortWithStatusJSON(401, gin.H{"error": "Invalid credential format"})
			return
		}
		appID, appSecret := creds[0], creds[1]

		// 6. 数据库验证
		dbAppSecret, err := models.QueryAppSecret(appID)
		if err != nil {
			c.AbortWithStatusJSON(500, gin.H{"error": "Internal server error"})
			return
		}

		if dbAppSecret == "" || dbAppSecret != appSecret {
			// 缓存无效凭证结果（防止暴力破解）
			c.AbortWithStatusJSON(401, gin.H{"error": "Invalid credentials"})
			return
		}

		// 7. 缓存有效凭证
		c.Set("appID", appID)
		c.Next()
	}
}

package jwt

import (
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
)

// JWT is jwt middleware
func JWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		var code int
		var data interface{}

		code = conf.SUCCESS
		token := c.Query("token")
		if token == "" {
			code = conf.InvalidParams
		} else {
			_, err := utils.ParseToken(token)
			if err != nil {
				switch err.(*jwt.ValidationError).Errors {
				case jwt.ValidationErrorExpired:
					code = conf.ErrorAuthCheckTokenTimeout
				default:
					code = conf.ErrorAuthCheckTokenFail
				}
			}
		}

		if code != conf.SUCCESS {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code": code,
				"msg":  conf.GetMsg(code),
				"data": data,
			})

			c.Abort()
			return
		}

		c.Next()
	}
}

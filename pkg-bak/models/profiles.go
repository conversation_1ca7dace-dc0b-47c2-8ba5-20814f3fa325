package models

import (
	"context"
	"errors"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
)

type Profile struct {
	ID          uint64    `gorm:"primaryKey;autoIncrement;column:id;comment:主键ID"`
	ProfileID   string    `gorm:"uniqueIndex;column:profile_id;type:varchar(36);not null;comment:业务ID(UUID)"`
	RevisionID  string    `gorm:"column:revision_id;type:varchar(36);not null;comment:当前版本ID"`
	AppID       string    `gorm:"column:app_id;type:varchar(36);not null;comment:关联游戏ID"`
	ProfileName string    `gorm:"uniqueIndex;column:profile_name;type:varchar(255);not null;comment:配置名称"`
	MinServers  uint64    `gorm:"column:min_servers;type:int unsigned;default:1;comment:最小服务实例数"`
	BufferSize  uint8     `gorm:"column:buffer_size;type:tinyint unsigned;default:0;comment:弹性缓冲池百分比（0-100）"`
	CreatedAt   time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt   time.Time `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:修改时间"`
	Deleted     bool      `gorm:"column:deleted;type:tinyint(1);default:0;comment:是否删除"`
}

func (*Profile) TableName() string {
	return "profiles"
}

// CreateProfile 创建启动配置
func CreateProfile(ctx context.Context, profile *Profile) error {
	if profile.ProfileID == "" {
		return errors.New("profile_id cannot be empty")
	}
	return dao.Db.Create(profile).Error
}

// CountProfileByAppID 查询启动配置数量
func CountProfileByAppID(ctx context.Context, appID string) (int64, error) {
	var cnt int64
	err := dao.Db.Table(ImagesTableName).
		Where("app_id = ? AND deleted = 0", appID).
		Order("created_at DESC").
		Count(&cnt).Error
	return cnt, err
}

func GetProfileList(appID string, offset, limit int) ([]*Profile, error) {
	var profiles []*Profile
	err := dao.Db.
		Where("app_id = ? AND deleted = 0", appID).
		Order("created_at DESC").Offset(offset).Limit(limit).
		Find(&profiles).Error
	return profiles, err
}

func GetProfile(profileID string) (*Profile, error) {
	var profile Profile
	err := dao.Db.Where("profile_id = ? AND deleted = 0", profileID).First(&profile).Error
	if err != nil {
		return nil, err
	}
	return &profile, nil
}

// SetProfileRevisionID 应用配置
func SetProfileRevisionID(profileID, revisionID string) error {
	return dao.Db.
		Model(&Profile{}).
		Where("profile_id = ?", profileID).
		Updates(map[string]interface{}{
			"revision_id": revisionID,
		}).Error
}

type UpdProfileReq struct {
	ProfileId  string `json:"profile_id"`
	MinServers int    `json:"min_servers"`
	BufferSize uint8  `json:"buffer_size"`
}

func UpdProfile(req *UpdProfileReq) error {
	if req.ProfileId == "" {
		return errors.New("profile_id cannot be empty")
	}
	return dao.Db.
		Model(&Profile{}).
		Where("profile_id = ?", req.ProfileId).
		Updates(map[string]interface{}{
			"min_servers": req.MinServers,
			"buffer_size": req.BufferSize,
		}).Error
}

func DeleteProfile(profileID string) error {
	if profileID == "" {
		return errors.New("profile_id cannot be empty")
	}
	profile, err := GetProfile(profileID)
	if err != nil {
		return err
	}
	if profile.Deleted {
		return nil
	}
	return dao.Db.
		Model(&Profile{}).
		Where("profile_id = ?", profileID).
		Updates(map[string]interface{}{
			"deleted": true,
		}).Error
}

// ReleaseResource 释放资源
// https://uos.unity.cn/multiverse/v1/game/games/profiles/ef6c1a03-06ab-41d2-893f-10fd766679f9/release-resource
func ReleaseResource() error {
	return nil
}

// TestServer 测试配置
// https://uos.unity.cn/multiverse/v1/game/profile-revisions/b43d173a-b1b7-4f77-8db1-0e7a83d1fe55/test-server
func TestServer() error {
	return nil
}

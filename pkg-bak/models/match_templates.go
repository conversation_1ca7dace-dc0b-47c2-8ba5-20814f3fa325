package models

import (
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"gorm.io/datatypes"
)

type MatchTemplate struct {
	ID                     uint64         `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	Name                   string         `gorm:"type:varchar(255);uniqueIndex;not null;comment:名称"`
	Level                  uint64         `gorm:"not null;comment:级别"` // smallint对应int16
	ParentLevelID          uint64         `gorm:"comment:父级ID"`        // 使用指针类型处理NULL
	Description            string         `gorm:"type:text;comment:描述"`
	BackgroundImg          string         `gorm:"type:varchar(512);comment:背景图"`
	DefaultMatchDefinition datatypes.JSON `gorm:"type:json;not null;comment:默认配置"`
	CreatedAt              time.Time      `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt              time.Time      `gorm:"autoUpdateTime;comment:修改时间"`
	Deleted                bool           `gorm:"type:tinyint(1);default:0;index;comment:是否删除"`
}

// TableName 表名配置
func (*MatchTemplate) TableName() string {
	return "match_templates"
}

func GetTemplateByLevel(level int16, parentID uint64) ([]*MatchTemplate, error) {
	var templates []*MatchTemplate
	db := dao.Db.Where("level = ?", level)
	if level > 1 {
		db = db.Where("parent_level_id = ?", parentID)
	}
	db = db.Find(&templates)
	err := db.Error
	if err != nil {
		return nil, err
	}
	return templates, nil
}

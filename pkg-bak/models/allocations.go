package models

import (
	"context"
	"fmt"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"gorm.io/datatypes"
)

type AllocationStatus uint8

const (
// 1. GameServer 主动调用 multiverseSdk.Shutdown() 方法.
// 2. 调用 DeleteAllocation API 删除 allocation (该 API 如果遇到 created 状态的 allocation 则取消分配, 遇到 allocated 状态的 allocation 则销毁分配).
// 3. 游戏服务器超时自动回收 (创建 APP 的时候可以指定一个统一的 allocation 超时回收时长, 创建每个 allocation 的时候也可以给单独指定超时回收时长, 但不能超过创建 APP 时指定的时长).
)

type Allocation struct {
	ID              uint64           `gorm:"primaryKey;autoIncrement;column:id"`
	AppID           string           `gorm:"column:app_id;type:varchar(36);index:idx_game_allocations,priority:1;not null;comment:关联游戏ID"`
	RoomID          string           `gorm:"column:room_id;type:varchar(36);index:idx_game_allocations,priority:2;not null;comment:关联房间ID"` // 服务器和房间1:1 绑定关系
	AllocationID    string           `gorm:"size:36;uniqueIndex;not null;column:allocation_id" json:"allocation_id"`
	ProfileID       string           `gorm:"size:36;not null;column:profile_id" json:"profile_id"`
	RevisionID      string           `gorm:"size:36;not null;column:revision_id" json:"revision_id"`
	ProfileRevision datatypes.JSON   `gorm:"type:longtext;not null;column:profile_revision" json:"profile_revision"`
	RegionID        string           `gorm:"size:36;not null;column:region_id" json:"region_id"`
	Envs            datatypes.JSON   `gorm:"type:longtext;column:envs" json:"envs"`
	AllocationTTL   int64            `gorm:"not null;default:0;column:allocation_ttl" json:"allocation_ttl"`
	Status          AllocationStatus `gorm:"type:tinyint;default:1;not null;column:status" json:"status"`
	CreatedAt       time.Time        `gorm:"column:created_at" json:"created_at"`
	UpdatedAt       time.Time        `gorm:"column:updated_at" json:"updated_at"`
	ExpiredAt       time.Time        `gorm:"column:expired_at" json:"expire_at"`
	Deleted         bool             `gorm:"column:deleted;default:false" json:"deleted"`
	Namespace       string           `gorm:"column:namespace" json:"namespace"`
	GssTemplate     string           `gorm:"column:gss_template" json:"gss_template"`
	GameServerName  string           `gorm:"column:game_server_name" json:"game_server_name"`
	IP              string           `gorm:"column:ip" json:"ip"`
}

const (
	AllocationStatusCreated     AllocationStatus = 1 // 对应服务器页面显示的 正在分配服务器 状态, 代表 allocation 创建成功, 正在为你分配服务器.
	AllocationStatusAllocated   AllocationStatus = 2 // 对应服务器页面显示的 正在运行 状态, 代表分配服务器成功, 服务器正在运行.
	AllocationStatusCanceled    AllocationStatus = 3 // 对应服务器页面显示的 已取消 状态, 如你创建了 allocation 但还没有到达 allocated 状态, 你可以通过 DeleteAllocation API 取消分配.
	AllocationStatusFailed      AllocationStatus = 4 // 对应服务器页面显示的 分配失败 状态, 可以查看日志定位失败原因.
	AllocationStatusUnhealthy   AllocationStatus = 5 // 对应服务器页面显示的 心跳检测失败 状态, GameServer 启动后便会开始心跳检测, 如果遇到 unhealthy 状态, 说明 GameServer 崩溃或是哪里有问题导致无法正常发送心跳检测包.
	AllocationStatusDeallocated AllocationStatus = 6 // 对应服务器页面显示的 已回收 状态, 有以下几种情况会触发服务器回收:
)

func GetAllocationStatus(status AllocationStatus) string {
	switch status {
	case AllocationStatusCreated:
		// 创建中
		return "Created"
	case AllocationStatusAllocated:
		// 已分配
		return "Allocated"
	case AllocationStatusDeallocated:
		// 已回收
		return "Deallocated"
	case AllocationStatusFailed:
		// 创建失败
		return "Failed"
	case AllocationStatusUnhealthy:
		// 不健康
		return "Unhealthy"
	case AllocationStatusCanceled:
		return "Canceled"
	}
	return "Unknown"
}

func (*Allocation) TableName() string {
	return "allocations"
}

// MarkReady 标记为已就绪
func MarkReady(allocationID string) error {
	tx := dao.Db.Exec("UPDATE allocations SET status = ? WHERE allocation_id = ? AND status = ?", AllocationStatusAllocated, allocationID, AllocationStatusCreated)

	err := tx.Error
	return err
}

// CreateAllocation 创建记录
func CreateAllocation(alloc *Allocation) (uint64, error) {
	result := dao.Db.Create(alloc)
	if result.Error != nil {
		return 0, result.Error
	}
	return alloc.ID, nil
}

// GetAllocationByID 查询单条记录（按 allocation_id）
func GetAllocationByID(allocationID string) (*Allocation, error) {
	var alloc Allocation
	err := dao.Db.Where("allocation_id = ? AND deleted = 0", allocationID).First(&alloc).Error
	return &alloc, err
}

// SetExpiredAt 设置过期时间
func SetExpiredAt(allocationID string, expiredAt time.Time) error {
	return dao.Db.Model(&Allocation{}).
		Where("allocation_id = ?", allocationID).
		Update("expired_at", expiredAt).Error
}

// UpdateAllocationStatus 更新（通过 allocation_id）
func UpdateAllocationStatus(allocationID string, status AllocationStatus) error {
	return dao.Db.Model(&Allocation{}).
		Where("allocation_id = ?", allocationID).
		Update("status", status).Error
}

// DeleteAllocation 软删除
func DeleteAllocation(allocationID string) error {
	return dao.Db.Model(&Allocation{}).
		Where("allocation_id = ? AND deleted = 0", allocationID).
		Update("deleted", true).Error
}

// BindRoom 绑定房间
func BindRoom(ctx context.Context, allocationID, roomID string) error {
	affected := dao.Db.Model(&Allocation{}).
		Where("allocation_id = ? AND room_id = ?", allocationID, "").
		Update("room_id", roomID).Limit(1).RowsAffected
	if affected == 0 {
		return fmt.Errorf("绑定失败")
	}
	return nil
}

// ListAllocations 查询全部（支持分页）
func ListAllocations(appID string, offset, limit int) ([]*Allocation, error) {
	var list []*Allocation
	err := dao.Db.Where("app_id = ? and deleted = 0", appID).Offset(offset).Limit(limit).Find(&list).Error
	return list, err
}

// CountAllocations 查询数量
func CountAllocations(ctx context.Context, appID string) (int64, error) {
	var cnt int64
	allocation := Allocation{}
	err := dao.Db.Table(allocation.TableName()).
		Where("app_id = ? AND deleted = 0", appID).
		Order("created_at DESC").
		Count(&cnt).Error
	return cnt, err
}

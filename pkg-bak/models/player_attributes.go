package models

import "time"

// PlayerAttribute 玩家属性定义表
type PlayerAttribute struct {
	ID        uint       `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	Name      string     `gorm:"type:varchar(64);uniqueIndex;not null;comment:属性名称"`
	DataType  string     `gorm:"type:enum('number','string','boolean','array');not null;comment:数据类型"`
	CreatedAt time.Time  `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt time.Time  `gorm:"autoUpdateTime;comment:更新时间"`
	Deleted   bool       `gorm:"default:0;comment:软删除标记"`
	DeletedAt *time.Time `gorm:"comment:删除时间"`
}

// TableName 设置表名
func (PlayerAttribute) TableName() string {
	return "player_attributes"
}

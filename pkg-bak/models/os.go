package models

import (
	"context"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
)

// Os 操作系统
type Os struct {
	ID        uint      `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	OSName    string    `gorm:"column:os_name;type:varchar(64);not null;comment:操作系统名称"`
	OSFamily  string    `gorm:"column:os_family;type:varchar(64);not null;comment:操作系统家族"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime;comment:创建时间"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime;comment:修改时间"`
	Deleted   bool      `gorm:"column:deleted;type:tinyint(1);not null;default:0;comment:是否删除"`
}

const OsTableName = "os"

// TableName 设置表名
func (*Os) TableName() string {
	return OsTableName
}

// CountOs 查询数量
func CountOs(ctx context.Context) (int64, error) {
	var cnt int64
	err := dao.Db.Table(OsTableName).
		Where("deleted = 0").
		Count(&cnt).Error
	return cnt, err
}

// GetOsList 查询系统列表
func GetOsList(ctx context.Context, offset, limit int) ([]Os, error) {
	var images []Os
	err := dao.Db.
		Where("deleted = 0").
		Order("created_at DESC").Offset(offset).Limit(limit).
		Find(&images).Error
	return images, err
}

// CreateOs 创建系统
func CreateOs(ctx context.Context, os *Os) error {
	return dao.Db.Create(os).Error
}

// UpdateOs 更新系统
func UpdateOs(ctx context.Context, os *Os) error {
	return dao.Db.Updates(os).Error
}

// GetOsByID 根据ID查询系统
func GetOsByID(ctx context.Context, osID uint) (*Os, error) {
	var os Os
	err := dao.Db.Where("id = ?", osID).First(&os).Error
	return &os, err
}

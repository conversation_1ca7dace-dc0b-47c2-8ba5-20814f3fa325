package models

import (
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"github.com/goccy/go-json"
	"gorm.io/datatypes"
)

type RoomMember struct {
	ID          uint64           `gorm:"primaryKey;autoIncrement" json:"id"`
	RoomID      string           `gorm:"type:varchar(64);not null;index:idx_room_user,unique" json:"room_id"`
	UserID      string           `gorm:"type:varchar(64);not null;index:idx_room_user,unique" json:"user_id"`
	JoinTime    time.Time        `gorm:"autoCreateTime" json:"join_time"`
	LeaveTime   *time.Time       `json:"leave_time,omitempty"`
	Status      RoomMemberStatus `gorm:"default:1" json:"status"` // 1: 加入, 2: 确认, 3: 离开
	DynamicInfo datatypes.JSON   `gorm:"type:json" json:"dynamic_info,omitempty"`
}

type RoomMemberStatus int8

const (
	RoomMemberStatusJoin    RoomMemberStatus = 1 // 已加入
	RoomMemberStatusConfirm RoomMemberStatus = 2 // 已确认
	RoomMemberStatusLeave   RoomMemberStatus = 3 // 已离开
)

func AddRoomMember(member *RoomMember) error {
	return dao.Db.Create(member).Error
}

func GetRoomMember(roomID, userID string) (*RoomMember, error) {
	var member RoomMember
	err := dao.Db.Where("room_id = ? AND user_id = ?", roomID, userID).First(&member).Error
	return &member, err
}

func UpdateRoomMemberStatus(roomID, userID string, newStatus int8) error {
	return dao.Db.Model(&RoomMember{}).
		Where("room_id = ? AND user_id = ?", roomID, userID).
		Update("status", newStatus).Error
}

func UpdateRoomMemberDynamicInfo(roomID, userID string, dynamicInfo map[string]interface{}) error {
	jsonData, _ := json.Marshal(dynamicInfo)
	return dao.Db.Model(&RoomMember{}).
		Where("room_id = ? AND user_id = ?", roomID, userID).
		Update("dynamic_info", datatypes.JSON(jsonData)).Error
}

func InRoom(roomID, userID string) (bool, error) {
	var member RoomMember
	err := dao.Db.Where("room_id = ? AND user_id = ?", roomID, userID).First(&member).Error
	return member.ID != 0, err
}

func LeaveRoom(roomID, userID string) error {
	now := time.Now()
	return dao.Db.Model(&RoomMember{}).
		Where("room_id = ? AND user_id = ?", roomID, userID).
		Updates(map[string]interface{}{
			"status":     3,
			"leave_time": &now,
		}).Error
}

func DeleteRoomMember(roomID, userID string) error {
	return dao.Db.Where("room_id = ? AND user_id = ?", roomID, userID).Delete(&RoomMember{}).Error
}

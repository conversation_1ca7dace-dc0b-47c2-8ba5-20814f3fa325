package models

import (
	"context"
	"fmt"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
)

type Project struct {
	ID                    uint       `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	AppID                 string     `gorm:"type:varchar(36);not null;index;comment:APPID"`
	ProjectID             string     `gorm:"type:varchar(36);not null;uniqueIndex;comment:项目ID"`
	OrganizationID        string     `gorm:"type:varchar(36);not null;index;comment:组织ID"`
	Name                  string     `gorm:"type:varchar(255);not null;comment:项目名称"`
	IconURL               string     `gorm:"type:varchar(255);not null;comment:icon"`
	Archived              bool       `gorm:"default:0;comment:是否归档"`
	Active                bool       `gorm:"default:0;comment:是否激活"`
	AllocationTTL         int64      `gorm:"not null;default:0;comment:服务器超时时长"`
	AllocationCallbackURL string     `gorm:"type:varchar(255);not null;comment:分配完成后端回调URL"`
	OsID                  int64      `gorm:"not null;comment:操作系统ID"`
	EnableDedicatedRoom   bool       `gorm:"default:0;comment:是否开启房间管理服务"`
	EnableMatchmakingMv   bool       `gorm:"default:0;comment:是否开启Matchmaking服务"`
	CreatedAt             time.Time  `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt             *time.Time `gorm:"autoUpdateTime;comment:修改时间"`
	Deleted               bool       `gorm:"default:0;index;comment:软删除标记"`
}

// TableName 自定义表名
func (Project) TableName() string {
	return "projects"
}

// CreateProject 创建项目
func CreateProject(project *Project) error {
	if project.ProjectID == "" {
		return fmt.Errorf("projectID is required")
	}

	return dao.Db.Create(project).Error
}

// GetByProjectID 根据项目ID获取项目（排除软删除）
func GetByProjectID(projectID string) (*Project, error) {
	var project Project
	err := dao.Db.Where("project_id = ? AND deleted = 0", projectID).First(&project).Error
	return &project, err
}

// CountProjectsByAppID 根据数量
func CountProjectsByAppID(ctx context.Context, appID string) (int64, error) {
	var cnt int64
	err := dao.Db.Table(ImagesTableName).
		Where("app_id = ? AND deleted = 0", appID).
		Order("created_at DESC").
		Count(&cnt).Error
	return cnt, err
}

// GetProjectsByAppID 查询列表
func GetProjectsByAppID(ctx context.Context, appID string, offset, limit int) ([]Project, error) {
	var projects []Project
	err := dao.Db.
		Where("app_id = ? AND deleted = 0", appID).
		Order("created_at DESC").Offset(offset).Limit(limit).
		Find(&projects).Error
	return projects, err
}

// GetByOrgID 根据组织ID获取所有项目
func GetByOrgID(orgID string, archived bool) ([]Project, error) {
	var projects []Project
	query := dao.Db.Where("organization_id = ? AND deleted = 0", orgID)

	if archived {
		query = query.Where("archived = 1")
	} else {
		query = query.Where("archived = 0")
	}

	err := query.Find(&projects).Error
	return projects, err
}

// UpdateProject 更新项目基础信息
func UpdateProject(projectID string, project Project) error {
	return dao.Db.Model(&Project{}).
		Where("project_id = ? AND deleted = 0", projectID).
		Updates(project).Error
}

// SoftDelete 软删除项目
func SoftDelete(projectID string) error {
	return dao.Db.Model(&Project{}).
		Where("project_id = ?", projectID).
		Updates(map[string]interface{}{
			"deleted":    1,
			"updated_at": time.Now(),
		}).Error
}

// HardDelete 硬删除项目
func HardDelete(projectID string) error {
	return dao.Db.Unscoped().
		Where("project_id = ?", projectID).
		Delete(&Project{}).Error
}

// ToggleArchive 切换项目归档状态
func ToggleArchive(projectID string, archive bool) error {
	return dao.Db.Model(&Project{}).
		Where("project_id = ? AND deleted = 0", projectID).
		Update("archived", archive).Error
}

// Search 批量查询（分页+过滤）
func Search(filter ProjectFilter, page, size int) ([]Project, int64, error) {
	var projects []Project
	var total int64

	query := dao.Db.Model(&Project{}).Where("deleted = 0")

	// 应用过滤条件
	if filter.OrganizationID != "" {
		query = query.Where("organization_id = ?", filter.OrganizationID)
	}
	if filter.Active {
		query = query.Where("active = ?", filter.Active)
	}
	if filter.Archived {
		query = query.Where("archived = ?", filter.Archived)
	}
	if filter.Name != "" {
		query = query.Where("name LIKE ?", "%"+filter.Name+"%")
	}

	// 分页查询
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Offset((page - 1) * size).Limit(size).Find(&projects).Error
	return projects, total, err
}

// ProjectFilter 过滤条件结构体
type ProjectFilter struct {
	OrganizationID string
	Name           string
	Active         bool
	Archived       bool
}

// UpdateDedicatedRoom 启用/禁用相关服务
func UpdateDedicatedRoom(projectID string, dedicatedRoom bool) error {
	return dao.Db.Model(&Project{}).
		Where("project_id = ? AND deleted = 0", projectID).
		Updates(map[string]interface{}{
			"enable_dedicated_room": dedicatedRoom,
		}).Error
}

// UpdateMatchmaking 启用/禁用相关服务
func UpdateMatchmaking(projectID string, matchmaking bool) error {
	return dao.Db.Model(&Project{}).
		Where("project_id = ? AND deleted = 0", projectID).
		Updates(map[string]interface{}{
			"enable_matchmaking_mv": matchmaking,
		}).Error
}

package models

import (
	"errors"
	"github.com/jinzhu/gorm"
	"time"
)

type ExpansionOperation string

const (
	Replace   ExpansionOperation = "replace"
	Disable   ExpansionOperation = "disable"
	Increment ExpansionOperation = "increment"
	Decrement ExpansionOperation = "decrement"
)

type RuleExpansion struct {
	ID              uint64             `gorm:"primaryKey;autoIncrement;column:id"`
	RuleID          uint64             `gorm:"column:rule_id;not null"`
	WaitTimeSeconds uint               `gorm:"column:wait_time_seconds;not null"`
	OperationType   ExpansionOperation `gorm:"column:operation_type;type:ENUM('replace','disable','increment','decrement');not null"`
	NewValue        string             `gorm:"column:new_value;type:varchar(255)"`
	TargetPath      string             `gorm:"column:target_path;type:varchar(512);not null"`
	CreatedAt       time.Time          `gorm:"column:created_at;not null;autoCreateTime"`
	UpdatedAt       time.Time          `gorm:"column:updated_at;not null;autoUpdateTime"`
	Deleted         bool               `gorm:"column:deleted;not null;default:0"`
}

// TableName 设置表名
func (*RuleExpansion) TableName() string {
	return "rule_expansions"
}

type RuleExpansionRepo struct {
	db *gorm.DB
}

func NewRuleExpansionRepo(db *gorm.DB) *RuleExpansionRepo {
	return &RuleExpansionRepo{db: db}
}

// Create 创建扩展规则
func (r *RuleExpansionRepo) Create(expansion *RuleExpansion) error {
	if expansion.WaitTimeSeconds <= 0 {
		return errors.New("等待时间必须大于0")
	}
	return r.db.Create(expansion).Error
}

// GetByID 通过ID获取扩展规则
func (r *RuleExpansionRepo) GetByID(id uint64) (*RuleExpansion, error) {
	var expansion RuleExpansion
	err := r.db.Where("id = ? AND deleted = ?", id, false).First(&expansion).Error
	if err != nil {
		return nil, err
	}
	return &expansion, nil
}

// GetByRuleID 获取规则的所有扩展
func (r *RuleExpansionRepo) GetByRuleID(ruleID uint64) ([]*RuleExpansion, error) {
	var expansions []*RuleExpansion
	err := r.db.Where("rule_id = ? AND deleted = ?", ruleID, false).Find(&expansions).Error
	if err != nil {
		return nil, err
	}
	return expansions, nil
}

// GetByOperationType 根据操作类型获取扩展
func (r *RuleExpansionRepo) GetByOperationType(ruleID uint64, opType ExpansionOperation) ([]*RuleExpansion, error) {
	var expansions []*RuleExpansion
	err := r.db.Where("rule_id = ? AND operation_type = ? AND deleted = ?", ruleID, opType, false).Find(&expansions).Error
	if err != nil {
		return nil, err
	}
	return expansions, nil
}

// Update 更新扩展规则
func (r *RuleExpansionRepo) Update(expansion *RuleExpansion) error {
	if expansion.WaitTimeSeconds <= 0 {
		return errors.New("等待时间必须大于0")
	}

	updateData := map[string]interface{}{
		"wait_time_seconds": expansion.WaitTimeSeconds,
		"operation_type":    expansion.OperationType,
		"new_value":         expansion.NewValue,
		"target_path":       expansion.TargetPath,
		"deleted":           expansion.Deleted,
	}

	return r.db.Model(&RuleExpansion{}).
		Where("id = ?", expansion.ID).
		Updates(updateData).Error
}

// SoftDelete 软删除扩展规则
func (r *RuleExpansionRepo) SoftDelete(id uint64) error {
	return r.db.Model(&RuleExpansion{}).
		Where("id = ?", id).
		Update("deleted", true).Error
}

// Delete 硬删除扩展规则
func (r *RuleExpansionRepo) Delete(id uint64) error {
	return r.db.Where("id = ?", id).Delete(&RuleExpansion{}).Error
}

// SoftDeleteByRuleID 批量软删除规则的所有扩展
func (r *RuleExpansionRepo) SoftDeleteByRuleID(ruleID uint64) error {
	return r.db.Model(&RuleExpansion{}).
		Where("rule_id = ?", ruleID).
		Update("deleted", true).Error
}

// FindAll 分页查询扩展规则
func (r *RuleExpansionRepo) FindAll(page, pageSize int) ([]*RuleExpansion, int64, error) {
	var expansions []*RuleExpansion
	var count int64

	offset := (page - 1) * pageSize

	if err := r.db.Where("deleted = ?", false).
		Count(&count).
		Offset(offset).
		Limit(pageSize).
		Find(&expansions).Error; err != nil {
		return nil, 0, err
	}

	return expansions, count, nil
}

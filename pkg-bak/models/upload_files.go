package models

import (
	"context"
	"errors"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
)

type UploadedFile struct {
	ID           uint64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement;comment:主键"`
	FileID       string     `gorm:"column:filename;type:varchar(64);not null;default:'';comment:UUID"`
	Filename     string     `gorm:"column:filename;type:varchar(255);not null;default:'';comment:原始文件名"`
	ObjectKey    string     `gorm:"column:object_key;type:varchar(255);not null;index:idx_object_key,unique;comment:OSS对象Key（路径）"`
	MimeType     string     `gorm:"column:mime_type;type:varchar(100);not null;default:'';comment:MIME类型"`
	FileSize     int64      `gorm:"column:file_size;default:0;comment:文件大小（单位：字节）"`
	UploaderID   int64      `gorm:"column:uploader_id;type:varchar(36);not null;comment:上传者ID"`
	IsDeleted    int8       `gorm:"column:is_deleted;type:tinyint(1);default:0;comment:是否逻辑删除 0否 1是"`
	IsCallback   bool       `gorm:"column:is_callback;type:tinyint(1);default:0;comment:是否回调 0否 1是"`
	CallbackTime *time.Time `gorm:"column:callback_time;comment:回调时间"`
	CreatedAt    time.Time  `gorm:"column:created_at;autoCreateTime;comment:创建时间"`
	UpdatedAt    time.Time  `gorm:"column:updated_at;autoUpdateTime;comment:修改时间"`
}

func (*UploadedFile) TableName() string {
	return "uploaded_files"
}

// SoftDelete 软删除方法
func (u *UploadedFile) SoftDelete() error {
	return dao.Db.Model(u).Update("is_deleted", 1).Error
}

// CreateFile 创建镜像记录
func CreateFile(ctx context.Context, file *UploadedFile) error {
	if file.FileID == "" {
		return errors.New("file_id cannot be empty")
	}
	return dao.Db.Create(file).Error
}

// SetCallbackData 设置回调状态
func SetCallbackData(ctx context.Context, fileID string, file *UploadedFile) error {
	fields := make(map[string]interface{})
	fields = map[string]interface{}{
		"is_callback":   1,
		"callback_time": time.Now(),
		"file_name":     file.Filename,
		"file_size":     file.FileSize,
		"mime_type":     file.MimeType,
		"object_key":    file.ObjectKey,
	}

	return dao.Db.Model(file).Where("file_id = ?", fileID).
		Updates(fields).Error
}

func GetFileByFileID(ctx context.Context, fileID string) (*UploadedFile, error) {
	var file UploadedFile
	err := dao.Db.Where("file_id = ?", fileID).First(&file).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

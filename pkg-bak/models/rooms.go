package models

import (
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"github.com/goccy/go-json"
	"github.com/jinzhu/gorm"
	log "github.com/sirupsen/logrus"
)

type RoomStatus uint8

const (
	RoomStatusCreated         RoomStatus = 0 // 创建成功未分配服务器
	RoomStatusServerAllocated RoomStatus = 1 // 已成功分配服务器(房间创建成功的默认状态)，该状态仅表示 Multiverse 服务器分配成功
	RoomStatusReady           RoomStatus = 2 // Multiverse 内游戏服务器已完全准备就绪，该状态下玩家可以加入房间
	RoomStatusRunning         RoomStatus = 3 // 房间处于运行中，该状态下玩家无法加入房间
	RoomStatusAllocatedFailed RoomStatus = 4 // 分配服务器失败（房间分配失败的状态）
	RoomStatusClosed          RoomStatus = 5 // 房间已关闭，服务器已销毁
)

// Visibility 自定义枚举类型
type Visibility string

const (
	VisibilityPublic  Visibility = "public"
	VisibilityPrivate Visibility = "private"
)

// Room 房间模型
type Room struct {
	ID                uint64          `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	RoomID            string          `gorm:"column:room_id;type:varchar(36);not null;uniqueIndex" json:"room_id"`
	ProfileID         string          `gorm:"column:profile_id;type:varchar(36);not null" json:"profile_id"`
	ProfileRevisionID string          `gorm:"column:profile_revision_id;type:varchar(36);not null" json:"profile_revision_id"`
	AppID             string          `gorm:"column:app_id;type:varchar(36);not null" json:"app_id"`
	OwnerID           string          `gorm:"column:owner_id;type:varchar(36);not null" json:"owner_id"`
	Namespace         string          `gorm:"column:namespace;type:varchar(255);not null" json:"namespace"`
	RoomName          string          `gorm:"column:room_name;type:varchar(255);not null" json:"room_name"`
	RoomCode          string          `gorm:"column:room_code;type:varchar(12)" json:"room_code,omitempty"` // 允许空值
	Visibility        Visibility      `gorm:"column:visibility;type:enum('public','private');not null;default:'public'" json:"visibility"`
	MaxPlayers        uint64          `gorm:"column:max_players;type:smallint(5) unsigned;not null" json:"max_players"`
	CurrentPlayers    uint64          `gorm:"column:current_players;type:smallint(5) unsigned;not null;default:0" json:"current_players"`
	Status            RoomStatus      `gorm:"column:status;type:enum('allocating','active','terminating','terminated');not null;default:'allocating'" json:"status"`
	TTLMinutes        uint64          `gorm:"column:ttl_minutes;type:smallint(5) unsigned;not null" json:"ttl_minutes"`
	ExpirationTime    uint64          `gorm:"column:expiration_time;type:int(11);not null;default: 0;autoUpdateTime" json:"expiration_time"`                          // 自动更新时间
	AllocationEnvs    json.RawMessage `gorm:"column:allocation_envs;type:longtext;charset:utf8mb4;collate:utf8mb4_bin;default:null" json:"allocation_envs,omitempty"` // JSON环境变量
	CreatedAt         time.Time       `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time       `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime" json:"updated_at"`
	Deleted           uint8           `gorm:"column:deleted;type:tinyint(1);not null;default:0" json:"deleted"` // 软删除标记（0未删除，1已删除）
}

// UnmarshalJSON 实现 json.Unmarshaler 接口，方便解析 AllocationEnvs
func (r *Room) UnmarshalJSON(data []byte) error {
	type Alias Room
	aux := &struct {
		*Alias
		AllocationEnvs string `json:"allocation_envs"`
	}{
		Alias: (*Alias)(r),
	}
	if err := json.Unmarshal(data, aux); err != nil {
		return err
	}
	if aux.AllocationEnvs != "" {
		var envs map[string]interface{}
		if err := json.Unmarshal([]byte(aux.AllocationEnvs), &envs); err != nil {
			return err
		}
		envBytes, _ := json.Marshal(envs)
		r.AllocationEnvs = envBytes
	}
	return nil
}

// CreateRoom 创建新房间
func CreateRoom(room *Room) error {
	return dao.Db.Create(room).Error
}

// GetRoomByID 根据ID查询未删除的房间
func GetRoomByID(roomID string) (*Room, error) {
	var room Room
	err := dao.Db.Where("room_id = ? AND deleted = 0", roomID).First(&room).Error
	if err != nil {
		return nil, err // 处理 gorm.ErrRecordNotFound
	}
	return &room, nil
}

// GetRoomByRoomID 根据业务ID查询未删除的房间
func GetRoomByRoomID(roomID string) (*Room, error) {
	var room Room
	err := dao.Db.Where("room_id = ? AND deleted = 0", roomID).First(&room).Error
	if err != nil {
		return nil, err
	}
	return &room, nil
}

// ListRooms 查询房间（分页）
func ListRooms(page, pageSize int, status RoomStatus) ([]Room, int64, error) {
	var (
		rooms  []Room
		total  int64
		offset = (page - 1) * pageSize
		now    = time.Now()
	)
	// 统计总数
	queryCnt := dao.Db.Model(&Room{}).
		Where("expiration_time > ? AND deleted = 0", now)
	if status != 0 {
		queryCnt.Where("status = ?", status)
	}
	err := queryCnt.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	// 查询分页数据
	query := dao.Db.Where("expiration_time > ? AND deleted = 0", now).
		Offset(offset).Limit(pageSize).
		Order("created_at DESC")
	if status != 0 {
		query.Where("status = ?", status)
	}
	err = query.Find(&rooms).Error
	return rooms, total, err
}

// GetRoomsByOwner 查询用户创建的未删除房间
func GetRoomsByOwner(ownerID string, page, pageSize int) ([]Room, int64, error) {
	var (
		rooms  []Room
		total  int64
		offset = (page - 1) * pageSize
	)
	err := dao.Db.Model(&Room{}).
		Where("owner_id = ? AND deleted = 0", ownerID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	err = dao.Db.Where("owner_id = ? AND deleted = 0", ownerID).
		Offset(offset).Limit(pageSize).
		Order("created_at DESC").
		Find(&rooms).Error
	return rooms, total, err
}

// IncrRoomPlayers 原子性增加当前玩家数
func IncrRoomPlayers(roomID string, num int64) error {
	if num == 0 {
		return nil
	}
	if num > 0 {
		return dao.Db.Model(&Room{}).
			Where("room_id = ? AND deleted = 0", roomID).
			Update("current_players", gorm.Expr("current_players + ?", num)).Error
	} else {
		return dao.Db.Model(&Room{}).
			Where("room_id = ? AND deleted = 0 AND current_players > ?", roomID, -num).
			Update("current_players", gorm.Expr("current_players - ?", -num)).Error
	}
}

// UpdateRoom 更新房间信息（部分字段）
func UpdateRoom(room *Room) error {
	return dao.Db.Model(room).
		Where("id = ? AND deleted = 0", room.ID).
		Updates(map[string]interface{}{
			"room_name":       room.RoomName,
			"max_players":     room.MaxPlayers,
			"status":          room.Status,
			"current_players": room.CurrentPlayers,
			"expiration_time": room.ExpirationTime,
			"allocation_envs": room.AllocationEnvs,
		}).Error
}

// IncrementPlayers 原子性增加当前玩家数（需检查是否超上限）
func IncrementPlayers(roomID string, maxPlayers uint16) error {
	return dao.Db.Model(&Room{}).
		Where("room_id = ? AND deleted = 0 AND current_players < ?", roomID, maxPlayers).
		Update("current_players", gorm.Expr("current_players + ?", 1)).Error
}

// DecrementPlayers 原子性减少当前玩家数（不能小于0）
func DecrementPlayers(roomID string) error {
	return dao.Db.Model(&Room{}).
		Where("room_id = ? AND deleted = 0 AND current_players > 0", roomID).
		Update("current_players", gorm.Expr("current_players - ?", 1)).Error
}

// SoftDeleteRoom 标记房间为已删除（软删除）
func SoftDeleteRoom(id uint64) error {
	return dao.Db.Model(&Room{}).
		Where("id = ? AND deleted = 0", id).
		Update("deleted", 1).Error
}

func GetAvailableRooms(appID string) ([]Room, error) {
	var rooms []Room
	err := dao.Db.Where("app_id = ? AND status = ? AND expiration_time > ?", appID, RoomStatusReady, time.Now()).Find(&rooms).Error
	if err != nil {
		log.Println("Failed to get available rooms:", err)
		return nil, err
	}
	return rooms, nil
}

// GetRooms 查询房间（分页）
func GetRooms(appID string, offset, limit int) ([]Room, int64, error) {
	var (
		rooms []Room
		total int64
		now   = time.Now()
	)
	// 统计总数
	queryCnt := dao.Db.Model(&Room{}).
		Where("app_id = ?", appID).
		Where("expiration_time > ? AND deleted = 0", now)
	queryCnt.Where("status IN ?", []RoomStatus{RoomStatusReady, RoomStatusRunning})

	err := queryCnt.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	// 查询分页数据
	query := dao.Db.Where("expiration_time > ? AND deleted = 0", now).
		Where("app_id = ?", appID).
		Offset(offset).Limit(limit).
		Order("created_at DESC")
	queryCnt.Where("status IN ?", []RoomStatus{RoomStatusReady, RoomStatusRunning})
	err = query.Find(&rooms).Error
	return rooms, total, err
}

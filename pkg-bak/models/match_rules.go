package models

import (
	"github.com/jinzhu/gorm"
	"time"
)

type MatchRule struct {
	ID          uint64    `gorm:"primaryKey;autoIncrement;column:id"`
	RuleName    string    `gorm:"column:rule_name;type:varchar(128);not null;uniqueIndex:uniq_rule_name"`
	Description string    `gorm:"column:description;type:text"`
	RuleType    string    `gorm:"column:rule_type;type:varchar(64);not null;index:idx_rule_type"`
	MaxDistance int       `gorm:"column:max_distance"`
	Reference   string    `gorm:"column:reference;type:varchar(64);not null"`
	Operation   string    `gorm:"column:operation;type:varchar(64);not null"`
	Measurement string    `gorm:"column:measurement;type:text;not null"`
	CreatedAt   time.Time `gorm:"column:created_at;not null;autoCreateTime"`
	UpdatedAt   time.Time `gorm:"column:updated_at;not null;autoUpdateTime"`
	Deleted     bool      `gorm:"column:deleted;not null;default:0"`
}

func (MatchRule) TableName() string {
	return "match_rules"
}

// CRUD 操作 ========================================================

type MatchRuleRepo struct {
	db *gorm.DB
}

func NewMatchRuleRepo(db *gorm.DB) *MatchRuleRepo {
	return &MatchRuleRepo{db: db}
}

// Create 创建记录
func (r *MatchRuleRepo) Create(rule *MatchRule) error {
	return r.db.Create(rule).Error
}

// GetByID 通过ID获取记录
func (r *MatchRuleRepo) GetByID(id uint) (*MatchRule, error) {
	var rule MatchRule
	err := r.db.Where("id = ? AND deleted = ?", id, false).First(&rule).Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

// GetByRuleName 通过规则名称获取记录
func (r *MatchRuleRepo) GetByRuleName(name string) (*MatchRule, error) {
	var rule MatchRule
	err := r.db.Where("rule_name = ? AND deleted = ?", name, false).First(&rule).Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

// GetByRuleType 通过规则类型获取记录
func (r *MatchRuleRepo) GetByRuleType(ruleType string) ([]*MatchRule, error) {
	var rules []*MatchRule
	err := r.db.Where("rule_type = ? AND deleted = ?", ruleType, false).Find(&rules).Error
	if err != nil {
		return nil, err
	}
	return rules, nil
}

// Update 更新记录
func (r *MatchRuleRepo) Update(rule *MatchRule) error {
	return r.db.Save(rule).Error
}

// SoftDelete 软删除记录
func (r *MatchRuleRepo) SoftDelete(id uint) error {
	return r.db.Model(&MatchRule{}).Where("id = ?", id).Update("deleted", true).Error
}

// Delete 完全删除记录
func (r *MatchRuleRepo) Delete(id uint) error {
	return r.db.Where("id = ?", id).Delete(&MatchRule{}).Error
}

// FindAll 分页查询
func (r *MatchRuleRepo) FindAll(page, pageSize int) ([]*MatchRule, int64, error) {
	var rules []*MatchRule
	var count int64

	offset := (page - 1) * pageSize

	if err := r.db.Where("deleted = ?", false).
		Count(&count).
		Offset(offset).
		Limit(pageSize).
		Order("id DESC").
		Find(&rules).Error; err != nil {
		return nil, 0, err
	}

	return rules, count, nil
}

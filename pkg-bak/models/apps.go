package models

import (
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"github.com/jinzhu/gorm"
)

// App .
type App struct {
	ID               uint       `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	AppID            string     `gorm:"column:app_id;type:varchar(36);not null;comment:关联游戏ID"`
	ProjectID        string     `gorm:"column:project_id;type:varchar(36);not null;comment:关联项目ID"`
	AppSecret        string     `gorm:"column:app_secret;type:varchar(128);not null;comment:秘钥"`
	AppServiceSecret string     `gorm:"column:app_service_secret;type:varchar(128);not null;comment:服务秘钥"`
	CreatedAt        time.Time  `gorm:"column:created_at;autoCreateTime;comment:创建时间"`
	UpdatedAt        time.Time  `gorm:"column:updated_at;autoUpdateTime;comment:修改时间"`
	Deleted          bool       `gorm:"column:deleted;type:tinyint(1);not null;default:0;comment:是否删除"`
	DeletedAt        *time.Time `gorm:"column:deleted_at;comment:删除时间"`
}

func QueryAppSecret(appId string) (string, error) {
	var app App
	err := dao.Db.Select("app_secret").Where(App{AppID: appId}).First(&app).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return "", err
	}

	if app.AppSecret != "" {
		return app.AppSecret, nil
	}
	return "", nil
}

package models

import "time"

// TeamDefinition 队伍配置定义表
type TeamDefinition struct {
	ID         uint       `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	TeamName   string     `gorm:"type:varchar(64);uniqueIndex;not null;comment:队伍名称"`
	MinTeams   uint8      `gorm:"default:1;comment:最小队伍数"`
	MaxTeams   uint8      `gorm:"default:1;comment:最大队伍数"`
	MinPlayers uint16     `gorm:"not null;comment:每队最少人数"`
	MaxPlayers uint16     `gorm:"not null;comment:每队最多人数"`
	CreatedAt  time.Time  `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt  time.Time  `gorm:"autoUpdateTime;comment:更新时间"`
	Deleted    bool       `gorm:"default:0;comment:软删除标记"`
	DeletedAt  *time.Time `gorm:"comment:删除时间"`
}

// TableName 设置表名
func (TeamDefinition) TableName() string {
	return "team_definitions"
}

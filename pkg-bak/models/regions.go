package models

import (
	"errors"
	"github.com/jinzhu/gorm"
	"time"

	"github.com/paulmach/orb"
	"github.com/paulmach/orb/encoding/ewkb"
)

type NetworkLine string

const (
	SingleLine NetworkLine = "single_line" // 单线
	DualLine   NetworkLine = "dual_line"   // 双线
	BGPMulti   NetworkLine = "bgp_multi"   // BGP多线
)

type Operator string

const (
	ChinaTelecom Operator = "telecom" //电信
	ChinaMobile  Operator = "mobile"  //移动
	ChinaUnicom  Operator = "unicom"  // 联通
	Other        Operator = "other"   // 其他
)

// Geometry 自定义几何类型（封装orb.Geometry）
type Geometry struct {
	orb.Geometry
}

func (g *Geometry) Scan(value interface{}) error {
	data, ok := value.([]byte)
	if !ok {
		return errors.New("invalid geometry type")
	}

	geom, _, err := ewkb.Unmarshal(data)
	if err != nil {
		return err
	}

	g.Geometry = geom
	return nil
}

//func (g *Geometry) Value() (driver.Value, error) {
//	return ewkb.Marshal(g.Geometry, 0)
//}

func (g *Geometry) GormDataType() string {
	return "geometry"
}

type Region struct {
	ID          uint64      `gorm:"primaryKey;autoIncrement;column:id"`
	RegionID    string      `gorm:"column:region_id;type:varchar(36);not null;unique"`
	AppID       string      `gorm:"column:app_id;type:varchar(36);not null"`
	Name        string      `gorm:"column:name;type:varchar(255);not null"`
	Polygon     Geometry    `gorm:"column:polygon;type:geometry;not null"`
	NetworkLine NetworkLine `gorm:"column:network_line;type:ENUM('单线','双线','BGP多线');not null"`
	Operator    Operator    `gorm:"column:operator;type:ENUM('电信','移动','联通','其他');not null"`
	ServiceArea Geometry    `gorm:"column:service_area;type:geometry;not null"`
	Deleted     bool        `gorm:"column:deleted;not null;default:0"`
	Active      bool        `gorm:"column:active;not null;default:1"`
	MaxServers  int64       `gorm:"column:max_servers;type:bigint;not null"`
	CreatedAt   time.Time   `gorm:"column:created_at;not null;autoCreateTime"`
	UpdatedAt   time.Time   `gorm:"column:updated_at;not null;autoUpdateTime"`
}

// polygon 是行政边界，service_area 是实际服务范围

func (Region) TableName() string {
	return "regions"
}

// CRUD 操作 ========================================================

type RegionRepo struct {
	db *gorm.DB
}

func NewRegionRepo(db *gorm.DB) *RegionRepo {
	return &RegionRepo{db: db}
}

func (r *RegionRepo) SetActive(regionID string, active bool) error {
	return r.db.Model(&Region{}).
		Where("region_id = ?", regionID).
		Update("active", active).Error
}

// Create 创建地域
func (r *RegionRepo) Create(region *Region) error {
	if region.RegionID == "" {
		return errors.New("region_id 不能为空")
	}
	return r.db.Create(region).Error
}

// GetByID 通过ID获取地域
func (r *RegionRepo) GetByID(id uint64) (*Region, error) {
	var region Region
	err := r.db.Where("id = ? AND deleted = ?", id, false).First(&region).Error
	if err != nil {
		return nil, err
	}
	return &region, nil
}

// GetByRegionID 通过region_id获取地域
func (r *RegionRepo) GetByRegionID(regionID string) (*Region, error) {
	var region Region
	err := r.db.Where("region_id = ? AND deleted = ?", regionID, false).First(&region).Error
	if err != nil {
		return nil, err
	}
	return &region, nil
}

// FindByPoint 查询多边形包含某点的区域
//func (r *RegionRepo) FindByPoint(lng, lat float64) ([]Region, error) {
//	point := orb.Point{lng, lat}
//
//	var regions []Region
//	err := r.db.Where("deleted = ? AND ST_Contains(polygon, ST_GeomFromText(?))",
//		false, point.ToWKT()).
//		Find(&regions).Error
//
//	return regions, err
//}

// FindByNetworkType 根据网络类型查询
func (r *RegionRepo) FindByNetworkType(networkType NetworkLine) ([]Region, error) {
	var regions []Region
	err := r.db.Where("network_line = ? AND deleted = ?", networkType, false).Find(&regions).Error
	return regions, err
}

// FindByOperator 根据运营商查询
func (r *RegionRepo) FindByOperator(operator Operator) ([]Region, error) {
	var regions []Region
	err := r.db.Where("operator = ? AND deleted = ?", operator, false).Find(&regions).Error
	return regions, err
}

// Update 更新地域
func (r *RegionRepo) Update(region *Region) error {
	if region.RegionID == "" {
		return errors.New("region_id 不能为空")
	}
	return r.db.Save(region).Error
}

// SoftDelete 软删除地域
func (r *RegionRepo) SoftDelete(id uint64) error {
	return r.db.Model(&Region{}).
		Where("id = ?", id).
		Update("deleted", true).Error
}

// Delete 完全删除地域
func (r *RegionRepo) Delete(id uint64) error {
	return r.db.Delete(&Region{}, id).Error
}

// FindAll 分页查询
func (r *RegionRepo) FindAll(page, pageSize int) ([]Region, int64, error) {
	var regions []Region
	var count int64
	offset := (page - 1) * pageSize

	if err := r.db.Model(&Region{}).
		Where("deleted = ?", false).
		Count(&count).
		Offset(offset).
		Limit(pageSize).
		Find(&regions).Error; err != nil {
		return nil, 0, err
	}

	return regions, count, nil
}

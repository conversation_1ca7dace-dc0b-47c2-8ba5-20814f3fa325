package models

import (
	"context"
	"errors"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"gorm.io/datatypes"
)

type ProfileRevision struct {
	ID                   uint64         `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	RevisionID           string         `gorm:"type:varchar(36);uniqueIndex;comment:版本ID(UUID)" json:"revision_id"`
	ProfileID            string         `gorm:"type:varchar(36);not null;comment:关联profileID" json:"profile_id"`
	GameImageID          string         `gorm:"type:varchar(36);not null;comment:关联镜像ID" json:"game_image_id"`
	RevisionStatus       RevisionStatus `gorm:"type:tinyint(3);default:'draft';comment:版本状态" json:"revision_status"`
	ReleaseVersion       uint64         `gorm:"default:0;comment:发布版本号" json:"release_version"`
	EnvironmentVariables datatypes.JSON `gorm:"type:longtext;check:json_valid(environment_variables);comment:环境变量" json:"environment_variables"`
	GameServerPorts      datatypes.JSON `gorm:"type:longtext;check:json_valid(game_server_ports);comment:服务器端口" json:"game_server_ports"`
	CPULimit             string         `gorm:"type:varchar(16);default:'100m';comment:CPU限制" json:"cpu_limit"`
	CPURequest           string         `gorm:"type:varchar(16);default:'100m'" json:"cpu_request"`
	MemoryLimit          string         `gorm:"type:varchar(16);default:'128Mi';comment:内存限制" json:"memory_limit"`
	MemoryRequest        string         `gorm:"type:varchar(16);default:'128Mi'" json:"memory_request"`
	GPULimit             string         `gorm:"type:varchar(32);default:'';comment:GPU限制" json:"gpu_limit"`
	GPURequest           string         `gorm:"type:varchar(32);default:''" json:"gpu_request"`
	GameStartDuration    int64          `gorm:"type:varchar(16);default:'30s';comment:启动时长" json:"game_start_duration"`
	EntryPoints          datatypes.JSON `gorm:"type:longtext;check:json_valid(entry_points);comment:入口程序启动命令" json:"entry_points"`
	CreatedBy            string         `gorm:"type:varchar(64);not null;comment:创建者标识" json:"created_by"`
	ModifiedBy           string         `gorm:"type:varchar(64);default:'';comment:修改者标识" json:"modified_by"`
	ReleasedBy           string         `gorm:"type:varchar(64);default:'';comment:发布者标识" json:"released_by"`
	ReleasedAt           *time.Time     `gorm:"comment:发布时间" json:"released_at"`
	FileConfigs          datatypes.JSON `gorm:"type:longtext;check:json_valid(file_configs);comment:挂载文件" json:"file_configs"`
	CreatedAt            time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt            time.Time      `gorm:"autoUpdateTime;comment:修改时间" json:"updated_at"`
	Deleted              int8           `gorm:"default:0;comment:是否删除" json:"deleted"`
}

// TableName 设置表名
func (ProfileRevision) TableName() string {
	return "profile_revisions"
}

type RevisionStatus uint8

// `gorm:"type:ENUM('draft','staged','active','archived');default:'draft';comment:版本状态" json:"revision_status"`
const (
	RevisionStatusDraft    RevisionStatus = 0 // 草稿：配置编辑中
	RevisionStatusStaged   RevisionStatus = 1 // 已暂存：通过测试待发布
	RevisionStatusActive   RevisionStatus = 2 // 已激活：当前生效版本
	RevisionStatusArchived RevisionStatus = 3 // 已归档：历史存档版本
)

var (
	RevisionStatusMap = map[RevisionStatus]string{
		RevisionStatusDraft:    "draft",
		RevisionStatusStaged:   "staged",
		RevisionStatusActive:   "active",
		RevisionStatusArchived: "archived",
	}
)

func (s RevisionStatus) String() string {
	text := RevisionStatusMap[s]
	if text == "" {
		return "unknown"
	}
	return text
}

/** 状态机
graph LR
Draft -->|提交测试| Staged
Staged -->|审核通过| Active
Active -->|新版本上线| Archived
Archived -->|紧急回滚| Active
Staged -->|测试失败| Draft
*/

// CreateProfileRevision 创建启动配置版本
func CreateProfileRevision(ctx context.Context, revision *ProfileRevision) error {
	if revision.ProfileID == "" || revision.RevisionID == "" {
		return errors.New("profile_id and revision_id cannot be empty")
	}
	return dao.Db.Create(revision).Error
}

// UpdProfileRevision 修改配置
func UpdProfileRevision(ctx context.Context, revision *ProfileRevision, currentVersion uint64) error {
	if revision.RevisionID == "" {
		return errors.New("revision_id cannot be empty")
	}
	if err := dao.Db.Model(&ProfileRevision{}).Where("revision_id = ? AND release_version = ?", revision.RevisionID, currentVersion).Limit(1).Updates(revision).Error; err != nil {
		return err
	}
	return nil
}

// CountProfileRevisionByProfileID 查询启动配置版本数量
func CountProfileRevisionByProfileID(ctx context.Context, profileID string) (int64, error) {
	var cnt int64
	err := dao.Db.Table(ImagesTableName).
		Where("profile_id = ? AND deleted = 0", profileID).
		Order("created_at DESC").
		Count(&cnt).Error
	return cnt, err
}

func GetProfileRevisionList(profileID string, offset, limit int) ([]*ProfileRevision, error) {
	list := make([]*ProfileRevision, 0)
	err := dao.Db.
		Where("profile_id = ? AND deleted = 0", profileID).
		Order("created_at DESC").Offset(offset).Limit(limit).
		Find(&list).Error
	return list, err
}

// GetProfileRevision 查看配置
func GetProfileRevision(revisionID string) (*ProfileRevision, error) {
	var revision ProfileRevision
	if err := dao.Db.Where("revision_id = ? AND deleted = 0", revisionID).First(&revision).Error; err != nil {
		return nil, err
	}

	return &revision, nil
}

// DeleteProfileRevision 删除配置 软删除
func DeleteProfileRevision(ctx context.Context, revisionID string) error {
	if err := dao.Db.Model(&ProfileRevision{}).Where("revision_id = ? AND deleted = 0", revisionID).Update("deleted", 1).Error; err != nil {
		return err
	}
	return nil
}

func DeleteProfileRevisionByProfileID(ctx context.Context, profileID string) error {
	if err := dao.Db.Model(&ProfileRevision{}).Where("profile_id = ? AND deleted = 0", profileID).Update("deleted", 1).Error; err != nil {
		return err
	}
	return nil
}

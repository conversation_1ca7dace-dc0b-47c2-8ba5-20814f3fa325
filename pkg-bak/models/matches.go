package models

import (
	"gorm.io/datatypes"
	"time"
)

// Match 匹配表
type Match struct {
	ID                  uint           `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	MatchID             string         `gorm:"type:varchar(36);uniqueIndex;not null;comment:匹配ID"`
	AppID               string         `gorm:"type:varchar(36);not null;comment:appid"`
	Name                string         `gorm:"type:varchar(255);not null;comment:匹配名称"`
	MatchDefinition     datatypes.JSON `gorm:"not null;comment:匹配定义"`
	TicketTTL           *int           `gorm:"default:30;comment:请求超时时长"`
	AutoBackfillEnabled *bool          `gorm:"default:1;comment:匹配回填"`
	RegionID            *string        `gorm:"type:varchar(36);comment:地域ID"`
	ProfileID           *string        `gorm:"type:varchar(36);comment:启动配置ID"`
	CreatedAt           time.Time      `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt           time.Time      `gorm:"autoUpdateTime;comment:修改时间"`
	Deleted             bool           `gorm:"default:0;comment:是否删除"`
	DeletedAt           *time.Time     `gorm:"comment:删除时间"`
}

// TableName 设置表名
func (Match) TableName() string {
	return "matches"
}

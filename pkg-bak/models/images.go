package models

import (
	"context"
	"errors"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"
	"github.com/goccy/go-json"
	"gorm.io/datatypes"
)

const ImagesTableName = "images"

// Image 游戏镜像存储表模型
type Image struct {
	ID                 uint             `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	ImageID            string           `gorm:"column:image_id;type:varchar(36);uniqueIndex;not null;comment:镜像业务ID(UUID)"`
	AppID              string           `gorm:"column:app_id;type:varchar(36);index:idx_game_images,priority:1;not null;comment:关联游戏ID"`
	ImageTag           string           `gorm:"column:image_tag;type:varchar(128);not null;comment:镜像标签(如v1.2.3)"`
	ImageStatus        ImageStatus      `gorm:"column:image_status;type:enum('building','pushed','deprecated');default:building;not null;comment:镜像状态"`
	ImageTagPrefix     *string          `gorm:"column:image_tag_prefix;type:varchar(64);comment:镜像标签前缀(如prod/test)"`
	ObjectName         *string          `gorm:"column:object_name;type:varchar(256);comment:存储对象名称"`
	ObjectURL          *string          `gorm:"column:object_url;type:varchar(512);comment:存储对象URL"`
	CompressedFormat   CompressedFormat `gorm:"column:compressed_format;default:1;not null;comment:压缩格式"`
	Description        *string          `gorm:"column:description;type:text;comment:镜像描述"`
	DockerFile         *string          `gorm:"column:docker_file;type:text;comment:Dockerfile内容"`
	OSId               int              `gorm:"column:os_id;type:int;not null;comment:操作系统ID"`
	OSName             string           `gorm:"column:os_name;type:varchar(64);not null;comment:操作系统名称"`
	ExecutableFilelist datatypes.JSON   `gorm:"column:executable_filelist;type:text;comment:可执行文件列表"`
	CreatedAt          time.Time        `gorm:"column:created_at;autoCreateTime;comment:创建时间"`
	UpdatedAt          time.Time        `gorm:"column:updated_at;autoUpdateTime;comment:修改时间"`
	Deleted            bool             `gorm:"column:deleted;type:tinyint(1);not null;default:0;comment:是否删除"`
}

// ImageStatus 镜像状态枚举
type ImageStatus uint8

const (
	StatusPending    ImageStatus = 0 // 待处理
	StatusBuilding   ImageStatus = 1 // 构建中
	StatusPushed     ImageStatus = 2 // 已推送仓库
	StatusDeprecated ImageStatus = 3 // 弃用
	StatusFailed     ImageStatus = 4 // 构建失败
)

var imageStatusMap = map[ImageStatus]string{
	StatusPending:    "pending",
	StatusBuilding:   "building",
	StatusPushed:     "pushed",
	StatusDeprecated: "deprecated",
	StatusFailed:     "failed",
}

func (i ImageStatus) GetStatus() string {
	if status, ok := imageStatusMap[i]; ok {
		return status
	}
	return "unknown"
}

// CompressedFormat 压缩格式枚举
type CompressedFormat uint8

const (
	FormatZip   CompressedFormat = 1 // "zip"
	FormatTar   CompressedFormat = 2 // "tar"
	FormatTarGz CompressedFormat = 3 // "tar.gz"
	Format7z    CompressedFormat = 4 // "7z"
)

var compressedFormatMap = map[CompressedFormat]string{
	FormatZip:   "zip",
	FormatTar:   "tar",
	FormatTarGz: "tar.gz",
	Format7z:    "7z",
}

func (c CompressedFormat) GetFormat() string {
	if format, ok := compressedFormatMap[c]; ok {
		return format
	}
	return "unknown"
}

// GetFilelist 将 JSON 字段转换为 []string
func (g *Image) GetFilelist() ([]string, error) {
	if len(g.ExecutableFilelist) == 0 {
		return []string{}, nil
	}

	var filelist []string
	err := json.Unmarshal(g.ExecutableFilelist, &filelist)
	return filelist, err
}

// SetFilelist 设置 []string 到 JSON 字段
func (g *Image) SetFilelist(files []string) error {
	data, err := json.Marshal(files)
	if err != nil {
		return err
	}
	g.ExecutableFilelist = data
	return nil
}

// TableName 设置表名
func (*Image) TableName() string {
	return "images"
}

// CreateImage 创建镜像记录
func CreateImage(ctx context.Context, image *Image) error {
	if image.ImageID == "" {
		return errors.New("image_id cannot be empty")
	}
	return dao.Db.Create(image).Error
}

// GetImageByID 根据业务ID查询镜像
func GetImageByID(ctx context.Context, imageID string) (*Image, error) {
	var image Image
	err := dao.Db.Where("image_id = ? AND deleted = 0", imageID).First(&image).Error
	if err != nil {
		return nil, err
	}
	return &image, nil
}

func BatchGetImageIDs(ctx context.Context, imageIDs []string) ([]Image, map[string]Image, error) {
	var images []Image
	err := dao.Db.Where("image_id IN ? AND deleted = 0", imageIDs).Find(&images).Error
	if err != nil {
		return nil, nil, err
	}
	imageMap := make(map[string]Image)
	for _, image := range images {
		imageMap[image.ImageID] = image
	}
	return images, imageMap, err
}

// CountImagesByAppID 根据游戏ID查询镜像数量
func CountImagesByAppID(ctx context.Context, appID string) (int64, error) {
	var cnt int64
	err := dao.Db.Table(ImagesTableName).
		Where("app_id = ? AND deleted = 0", appID).
		Order("created_at DESC").
		Count(&cnt).Error
	return cnt, err
}

// GetImagesByAppID 根据游戏ID查询镜像列表
func GetImagesByAppID(ctx context.Context, appID string, offset, limit int) ([]Image, error) {
	var images []Image
	err := dao.Db.
		Where("app_id = ? AND deleted = 0", appID).
		Order("created_at DESC").Offset(offset).Limit(limit).
		Find(&images).Error
	return images, err
}

// UpdateImageStatus 更新镜像状态
func UpdateImageStatus(ctx context.Context, imageID string, status ImageStatus) error {
	return dao.Db.
		Model(&Image{}).
		Where("image_id = ? AND deleted = 0", imageID).
		Update("image_status", status).Error
}

// SoftDeleteImage 逻辑删除镜像
func SoftDeleteImage(ctx context.Context, imageID string) error {
	return dao.Db.
		Model(&Image{}).
		Where("image_id = ?", imageID).
		Updates(map[string]interface{}{
			"deleted": true,
		}).Error
}

// UpdateImageFields 选择性更新字段
func UpdateImageFields(ctx context.Context, imageID string, fields map[string]interface{}) error {
	// 排除不可更新字段
	delete(fields, "id")
	delete(fields, "image_id")
	delete(fields, "created_at")

	return dao.Db.
		Model(&Image{}).
		Where("image_id = ? AND deleted = 0", imageID).
		Updates(fields).Error
}

// GetImageByTag 根据标签查询镜像（带状态过滤）
func GetImageByTag(ctx context.Context, appID, imageTag string, status ImageStatus) (*Image, error) {
	var image Image
	err := dao.Db.
		Where("app_id = ? AND image_tag = ? AND image_status = ? AND deleted = 0",
			appID, imageTag, status).
		First(&image).Error
	return &image, err
}

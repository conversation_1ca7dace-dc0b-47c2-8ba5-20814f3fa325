package api

import (
	"math"
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/controllers"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service"
	"github.com/astaxie/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"
)

// GetOsList 查询系统列表
func GetOsList(c *gin.Context) {
	appG := app.Gin{C: c}

	// 获取分页参数（带默认值）
	page, pageSize, offset := controllers.GetPageParam(c)

	total, list, err := service.GetOsList(c, offset, pageSize)
	if err != nil {
		return
	}
	data := make(map[string]interface{})
	data["list"] = list
	data["total"] = total
	data["page"] = page
	data["page_size"] = pageSize
	data["total_page"] = int(math.Ceil(float64(total) / float64(pageSize)))
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func CreateOs(c *gin.Context) {
	appG := app.Gin{C: c}
	req := service.CreateOsReq{}
	if err := c.BindJSON(&req); err != nil {
		appG.Response(200, 400, nil)
		return
	}
	err := service.CreateOs(c, req.OsName, req.OsFamily)
	if err != nil {
		appG.Error(conf.ERROR, "创建系统失败", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func GetOsByID(c *gin.Context) {
	appG := app.Gin{C: c}

	osID := c.GetInt64("os_id")

	os, err := service.GetOsByID(c, uint(osID))
	if err != nil {
		appG.Error(conf.ERROR, "获取系统失败", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, os)
}

// GetProject 获取项目详情
// @Summary 获取指定项目详细信息
// @Description 通过项目ID获取项目完整信息
// @Tags 项目管理
// @Accept json
// @Produce json
// @Param id path string true "项目ID" format(uuid) example(550e8400-e29b-41d4-a716-************)
// @Security ApiKeyAuth
// @Router /multiverse/projects/{id} [get]
func GetProject(c *gin.Context) {
	appG := app.Gin{C: c}

	projectID := c.Param("project_id") // 获取路径参数
	project, err := service.GetProject(projectID)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, project)
}

func GetProjectList(c *gin.Context) {
	appG := app.Gin{C: c}
	// 获取分页参数（带默认值）
	// 获取分页参数（带默认值）
	page, pageSize, offset := controllers.GetPageParam(c)
	appID := c.Param("app_id")

	projects, total, err := service.GetProjectList(appG.C, appID, offset, pageSize)
	if err != nil {
		return
	}
	data := make(map[string]interface{})
	data["list"] = projects
	data["total"] = total
	data["page"] = page
	data["page_size"] = pageSize
	data["total_page"] = int(math.Ceil(float64(total) / float64(pageSize)))
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func UpdProject(c *gin.Context) {
	appG := app.Gin{C: c}

	req := service.UpdProjectReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		appG.Response(200, 400, nil)
		return
	}
	bytes, _ := json.Marshal(req)
	logs.Info("UpdProject", string(bytes))

	err := service.UpdProject(&req)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func EnableRoomManager(c *gin.Context) {
	appG := app.Gin{C: c}
	projectID := c.Param("project_id") // 获取路径参数

	err := service.EnableRoomManager(projectID)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func DisableRoomManager(c *gin.Context) {
	appG := app.Gin{C: c}
	projectID := c.Param("project_id") // 获取路径参数

	err := service.DisableRoomManager(projectID)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func EnableMatchmaking(c *gin.Context) {
	appG := app.Gin{C: c}
	projectID := c.Param("project_id") // 获取路径参数

	err := service.EnableMatchmaking(projectID)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func DisableMatchmaking(c *gin.Context) {
	appG := app.Gin{C: c}
	projectID := c.Param("project_id") // 获取路径参数

	err := service.DisableMatchmaking(projectID)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func CreateProject(c *gin.Context) {
	appG := app.Gin{C: c}

	req := service.CreateProjectReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		logs.Info("CreateImage", err)
		appG.Response(200, 400, nil)
		return
	}

	err := service.CreateProject(c, &req)
	if err != nil {
		logs.Error("CreateImage", err)
		appG.Response(http.StatusOK, conf.ERROR, nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func DeleteProject(c *gin.Context) {
	appG := app.Gin{C: c}
	projectID := c.Param("project_id") // 获取路径参数

	err := service.DeleteProject(projectID)
	if err != nil {
		logs.Error("删除项目失败", projectID, err.Error())
		appG.Error(conf.ERROR, "删除项目失败", nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

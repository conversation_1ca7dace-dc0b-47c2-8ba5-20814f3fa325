package api

import (
	"net/http"
	"strconv"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service"
	"github.com/astaxie/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"
)

type CreateAllocationReq struct {
	ProfileId     string            `json:"profile_id"`
	RegionId      string            `json:"region_id"`
	AllocationTTL string            `json:"allocation_ttl"`
	Envs          map[string]string `json:"envs"`
}

func CreateAllocation(c *gin.Context) {
	appG := app.Gin{C: c}
	req := service.CreateAllocationReq{}
	if err := c.BindJ<PERSON>(&req); err != nil {
		logs.Error("CreateAllocation", err.Error())
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}
	bytes, _ := json.Marshal(req)
	logs.Info("CreateAllocation", string(bytes))
	err := service.CreateAllocation(c, req)
	if err != nil {
		logs.Error("CreateAllocation", err.Error())
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func ListAllocations(c *gin.Context) {
	// todo 需要带搜索
	appG := app.Gin{C: c}
	appID := c.Param("app_id")
	// 获取分页参数（带默认值）
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	// 参数验证
	if page < 1 || pageSize < 1 || pageSize > 100 {
		appG.Response(200, conf.InvalidParams, nil)
		return
	}
	_, allocations, err := service.ListAllocations(appG.C, appID, page, pageSize)
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}
	data := make(map[string]interface{})
	data["list"] = allocations
	data["total"] = 100
	data["page"] = page
	data["page_size"] = pageSize
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func GetAllocation(c *gin.Context) {
	appG := app.Gin{C: c}
	allocationID := c.Param("allocation_id")
	allocation, err := service.GetAllocation(allocationID)
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, allocation)
}

func DeleteAllocation(c *gin.Context) {
	appG := app.Gin{C: c}
	allocationID := c.Param("allocation_id")
	err := service.RemoveAllocation(c, allocationID)
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

package api

import (
	"bytes"
	"crypto"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"io"
	"log"
	"net/http"
	"strconv"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils/oss_util"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
	"github.com/astaxie/beego/logs"
	"github.com/gin-gonic/gin"
)

// GetUploadSignature 获取上传签名
func GetUploadSignature(c *gin.Context) {
	appG := app.Gin{C: c}
	token, err := oss_util.GetPolicyToken(c)
	if err != nil {
		logs.Info(token, err)
		appG.Response(http.StatusOK, conf.ErrorGetUploadSignatureFail, nil)
		return
	}
	c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
	appG.Response(http.StatusOK, conf.SUCCESS, token)
}

// OSSCallbackRequest bucket=${bucket}&object=${object}&filename=${object}&size=${size}&mimeType=${mimeType}&file_id=${x:file_id}
type OSSCallbackRequest struct {
	Bucket   string `form:"bucket" json:"bucket"`
	Object   string `form:"object" json:"object"`
	FileID   string `form:"file_id" json:"file_id"`
	Filename string `form:"filename" json:"filename"`
	Size     string `form:"size" json:"size"`
	MimeType string `form:"mimeType" json:"mimeType"`
}

func validateOSSRequest(r *http.Request) bool {
	pubKeyURL, err := base64.StdEncoding.DecodeString(r.Header.Get("x-oss-pub-key-url"))
	if err != nil {
		return false
	}

	pubKeyResp, err := http.Get(string(pubKeyURL))
	if err != nil {
		return false
	}
	defer pubKeyResp.Body.Close()

	pubKeyBytes, _ := io.ReadAll(pubKeyResp.Body)
	pubKeyBlock, _ := pem.Decode(pubKeyBytes)
	if pubKeyBlock == nil {
		return false
	}
	pubKey, err := x509.ParsePKIXPublicKey(pubKeyBlock.Bytes)
	if err != nil {
		return false
	}

	body, _ := io.ReadAll(r.Body)
	r.Body = io.NopCloser(bytes.NewBuffer(body)) // 重新赋值，后续还能读取

	authStr := r.RequestURI + "\n" + string(body)
	signature, _ := base64.StdEncoding.DecodeString(r.Header.Get("authorization"))

	return rsa.VerifyPKCS1v15(pubKey.(*rsa.PublicKey), crypto.SHA1, sha1Sum(authStr), signature) == nil
}

func sha1Sum(content string) []byte {
	h := sha1.New()
	h.Write([]byte(content))
	return h.Sum(nil)
}

// OssCallback OSS 回调
func OssCallback(c *gin.Context) {
	var callbackReq OSSCallbackRequest
	// Step 1: 验签（可选强验证，默认可跳过）
	if ok := validateOSSRequest(c.Request); !ok {
		c.JSON(http.StatusForbidden, gin.H{"message": "验签失败"})
		return
	}

	// Step 2: 解析回调内容
	if err := c.ShouldBind(&callbackReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": "参数解析失败", "error": err.Error()})
		return
	}

	log.Printf("OSS 上传成功：文件：%s，大小：%s，类型：%s", callbackReq.Filename, callbackReq.Size, callbackReq.MimeType)

	size, _ := strconv.ParseInt(callbackReq.Size, 10, 64)

	// Step 3: 设置回调内容
	err := models.SetCallbackData(c, callbackReq.FileID, &models.UploadedFile{
		FileID:    callbackReq.FileID,
		Filename:  callbackReq.Filename,
		ObjectKey: callbackReq.Object,
		MimeType:  callbackReq.MimeType,
		FileSize:  size,
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "database error"})
		return
	}

	// 返回 OK 让 OSS 不重试
	c.JSON(http.StatusOK, gin.H{"Status": "OK"})
}

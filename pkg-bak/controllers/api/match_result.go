package api

type MatchResponse struct {
	Matches             []Match       `json:"matches"`
	UnmatchedTickets    []interface{} `json:"unmatchedTickets"`    // 根据实际票据结构替换 interface{}
	IncompatibleTickets []interface{} `json:"incompatibleTickets"` // 同上
}

type Match struct {
	Proposals       []Proposal `json:"proposals"`
	WaitTimeSeconds int        `json:"waitTimeSeconds"`
}

type Proposal struct {
	Teams []Team `json:"teams"`
}

type Team struct {
	TeamDefinitionName string   `json:"teamDefinitionName"`
	TeamName           string   `json:"teamName"`
	Tickets            []Ticket `json:"tickets"`
}

type Ticket struct {
	ID      string   `json:"id"`
	Players []Player `json:"players"`
}

type Player struct {
	ID         string                 `json:"id"`
	Attributes map[string]interface{} `json:"attributes"` // 根据实际属性结构替换
}

/**
teamDefinitionName	队伍类型标识（如 "quick-game" 表示快速匹配模式）
tickets	代表一组玩家组成的匹配单元（一个 ticket 可能包含多个玩家）
players[].attributes	玩家属性（如技能等级、偏好角色等，可根据业务需求定义具体字段）
waitTimeSeconds	本次匹配等待时间（用于监控匹配效率）
*/

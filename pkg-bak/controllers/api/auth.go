package api

import (
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"

	"github.com/astaxie/beego/validation"
	"github.com/gin-gonic/gin"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service/auth_service"
)

type auth struct {
	Username string `valid:"Required; MaxSize(50)"`
	Password string `valid:"Required; MaxSize(50)"`
}

func GetAuth(c *gin.Context) {
	appG := app.Gin{C: c}
	valid := validation.Validation{}

	username := c.<PERSON>Form("username")
	password := c.PostForm("password")

	a := auth{Username: username, Password: password}
	ok, _ := valid.Valid(&a)

	if !ok {
		app.MarkErrors(valid.Errors)
		appG.Response(http.StatusBadRequest, 400, nil)
		return
	}

	authService := auth_service.Auth{Username: username, Password: password}
	isExist, err := authService.Check()
	if err != nil {
		appG.Response(http.StatusInternalServerError, 500, nil)
		return
	}

	if !isExist {
		appG.Response(http.StatusUnauthorized, 500, nil)
		return
	}

	token, err := utils.GenerateToken(username, password)
	if err != nil {
		appG.Response(http.StatusInternalServerError, 500, nil)
		return
	}

	appG.Response(http.StatusOK, 200, map[string]string{
		"token": token,
	})
}

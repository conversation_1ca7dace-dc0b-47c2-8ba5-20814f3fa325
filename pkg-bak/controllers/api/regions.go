package api

import (
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/controllers"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service"
	"github.com/astaxie/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"
)

func GetRegions(c *gin.Context) {
	appG := app.Gin{C: c}
	// 获取分页参数（带默认值）
	page, pageSize, _ := controllers.GetPageParam(c)

	regions, total, err := service.GetAllRegions(page, pageSize)
	if err != nil {
		appG.Error(conf.ERROR, "查询区域失败，请稍后再试", nil)
		return
	}
	data := make(map[string]interface{})
	data["list"] = regions
	data["total"] = total
	data["page"] = page
	data["page_size"] = pageSize
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func GetRegion(c *gin.Context) {
	appG := app.Gin{C: c}
	regionID := c.Param("region_id")

	region, err := service.GetRegionByID(regionID)
	if err != nil {
		appG.Error(conf.ERROR, "查询区域失败，请稍后再试", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, region)
}

type UpdRegionReq struct {
	RegionID   string `json:"region_id"`
	MaxServers int64  `json:"max_servers"`
}

type SetActiveReq struct {
	RegionID string `json:"region_id"`
}

func UpdRegion(c *gin.Context) {
	appG := app.Gin{C: c}

	req := UpdRegionReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		appG.Response(200, 400, nil)
		return
	}
	bytes, _ := json.Marshal(req)
	logs.Info("UpdProject", string(bytes))

	err := service.UpdateRegion(req.RegionID, req.MaxServers)
	if err != nil {
		appG.Error(conf.ERROR, "更新区域失败，请稍后再试", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func StopRegion(c *gin.Context) {
	appG := app.Gin{C: c}

	req := SetActiveReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		appG.Response(200, 400, nil)
		return
	}
	err := service.SetActive(req.RegionID, false)
	if err != nil {
		appG.Error(conf.ERROR, "停用区域失败，请稍后再试", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func StartRegion(c *gin.Context) {
	appG := app.Gin{C: c}

	req := SetActiveReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		appG.Response(200, 400, nil)
		return
	}
	err := service.SetActive(req.RegionID, true)
	if err != nil {
		appG.Error(conf.ERROR, "启用区域失败，请稍后再试", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func ReleaseRegion(c *gin.Context) {
	appG := app.Gin{C: c}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func Allocate(c *gin.Context) {
	appG := app.Gin{C: c}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

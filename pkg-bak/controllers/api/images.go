package api

import (
	"math"
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/controllers"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service"
	"github.com/astaxie/beego/logs"
	"github.com/gin-gonic/gin"
)

// GetImage 查询镜像
func GetImage(c *gin.Context) {
	appG := app.Gin{C: c}
	appID := c.Param("app_id")
	imageID := c.Param("image_id")
	logs.Info("GetImage", appID, imageID)

	image, err := service.GetImage(appG.C, appID, imageID)
	if err != nil {
		logs.Error("GetImage %v", err)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, image)
}

// GetImageList 查询镜像列表
func GetImageList(c *gin.Context) {
	appG := app.Gin{C: c}
	// 获取分页参数（带默认值）
	page, pageSize, offset := controllers.GetPageParam(c)
	appID := c.Param("app_id")

	total, images, err := service.GetImageList(appG.C, appID, offset, pageSize)
	if err != nil {
		return
	}
	data := make(map[string]interface{})
	data["list"] = images
	data["total"] = total
	data["page"] = page
	data["page_size"] = pageSize
	data["total_page"] = int(math.Ceil(float64(total) / float64(pageSize)))
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

// CreateImage 创建镜像
func CreateImage(c *gin.Context) {
	appG := app.Gin{C: c}

	req := service.CreateImageReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		logs.Info("CreateImage", err)
		appG.Response(200, 400, nil)
		return
	}

	err := service.CreateImage(c, req)
	if err != nil {
		logs.Error("CreateImage", err)
		appG.Response(http.StatusOK, conf.ERROR, nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

// DeleteImage 删除镜像
func DeleteImage(c *gin.Context) {
	appG := app.Gin{C: c}
	appID := c.Param("app_id")
	imageID := c.Param("image_id")
	if len(appID) == 0 || len(imageID) == 0 {
		appG.Response(200, conf.InvalidParams, nil)
		return
	}

	err := service.DeleteImage(c, appID, imageID)
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

JSON 结构说明

该 JSON 表示一个 匹配结果，包含以下核心部分：
1. `matches`: 成功匹配的集合（可能包含多个匹配方案，每个方案包含多个队伍）。
2. `unmatchedTickets`: 未能匹配的票据（暂时没有匹配的玩家或队伍）。
3. `incompatibleTickets`: 不兼容的票据（因规则冲突无法匹配）。

---

对应的 Go 结构体定义

```go
type MatchResponse struct {
	Matches             []Match          `json:"matches"`
	UnmatchedTickets    []interface{}    `json:"unmatchedTickets"`    // 根据实际票据结构替换 interface{}
	IncompatibleTickets []interface{}    `json:"incompatibleTickets"` // 同上
}

type Match struct {
	Proposals        []Proposal `json:"proposals"`
	WaitTimeSeconds  int        `json:"waitTimeSeconds"`
}

type Proposal struct {
	Teams []Team `json:"teams"`
}

type Team struct {
	TeamDefinitionName string   `json:"teamDefinitionName"`
	TeamName           string   `json:"teamName"`
	Tickets            []Ticket `json:"tickets"`
}

type Ticket struct {
	ID      string   `json:"id"`
	Players []Player `json:"players"`
}

type Player struct {
	ID         string                 `json:"id"`
	Attributes map[string]interface{} `json:"attributes"` // 根据实际属性结构替换
}
```

---

关键字段解释

| 字段                     | 说明                                                                 |
|--------------------------|--------------------------------------------------------------------|
| `teamDefinitionName`      | 队伍类型标识（如 "quick-game" 表示快速匹配模式）                        |
| `tickets`                | 代表一组玩家组成的匹配单元（一个 ticket 可能包含多个玩家）               |
| `players[].attributes`   | 玩家属性（如技能等级、偏好角色等，可根据业务需求定义具体字段）              |
| `waitTimeSeconds`        | 本次匹配等待时间（用于监控匹配效率）                                     |

---

使用示例（Gin 框架返回响应）

```go
func GetMatchResult(c *gin.Context) {
    response := MatchResponse{
        Matches: []Match{
            {
                Proposals: []Proposal{
                    {
                        Teams: []Team{
                            {
                                TeamDefinitionName: "quick-game",
                                TeamName:           "quick-game",
                                Tickets: []Ticket{
                                    {
                                        ID: "900de952-fe74-4f8e-ae49-c616b692c6fd",
                                        Players: []Player{
                                            {ID: "3", Attributes: map[string]interface{}{}},
                                        },
                                    },
                                    // 其他 tickets...
                                },
                            },
                        },
                    },
                },
                WaitTimeSeconds: 0,
            },
        },
        UnmatchedTickets:    []interface{}{},
        IncompatibleTickets: []interface{}{},
    }

    c.JSON(200, response)
}
```

---

扩展建议

1. 明确票据结构：若 `unmatchedTickets` 和 `incompatibleTickets` 有具体字段，可替换 `interface{}` 为具体类型（如 `Ticket` 结构体）。
2. 玩家属性优化：将 `map[string]interface{}` 替换为业务相关的结构体，例如：
   ```go
   type PlayerAttributes struct {
       SkillLevel int    `json:"skillLevel"`
       Role       string `json:"role"`
   }
   ```
3. 添加校验逻辑：在匹配过程中验证 `teamDefinitionName` 的合法性，确保符合预定义规则。

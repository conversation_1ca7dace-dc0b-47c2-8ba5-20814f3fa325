package sdk

import (
	"net/http"
	"strconv"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service"
	"github.com/gin-gonic/gin"
)

// Ready 标记服务器就绪
// @Summary 标记服务器就绪
// @Description 标记服务器就绪
// @Tags 服务器管理
// @Accept json
// @Produce json
// @Param allocation_id path string true "服务器ID" format(uuid) example(550e8400-e29b-41d4-a716-************)
// @Security ApiKeyAuth
func Ready(c *gin.Context) {
	appG := app.Gin{C: c}
	allocationID := c.Param("allocation_id")

	if err := service.MarkReady(allocationID); err != nil {
		appG.Response(http.StatusOK, conf.ERROR, nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

// GetEnvs 获取环境变量
func GetEnvs(c *gin.Context) {
	appG := app.Gin{C: c}
	allocationID := c.Param("allocation_id")

	envs, err := service.GetEnvs(allocationID)
	if err != nil {
		appG.Response(http.StatusOK, conf.ERROR, nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, envs)
}

// GetServerInfo .
func GetServerInfo(c *gin.Context) {
	appG := app.Gin{C: c}
	allocationID := c.Param("allocation_id")

	serverInfo, err := service.GetServerInfo(allocationID)
	if err != nil {
		appG.Response(http.StatusOK, conf.ERROR, nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, serverInfo)
}

// GetExpiredAt 获取过期时间
func GetExpiredAt(c *gin.Context) {
	appG := app.Gin{C: c}
	allocationID := c.Param("allocation_id")

	data, err := service.GetExpiredAt(allocationID)
	if err != nil {
		appG.Response(http.StatusOK, conf.ERROR, nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

// Shutdown 关闭服务器
func Shutdown(c *gin.Context) {
	appG := app.Gin{C: c}
	allocationID := c.Param("allocation_id")

	err := service.Shutdown(allocationID)
	if err != nil {
		appG.Response(http.StatusOK, conf.ERROR, nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func Allocate(c *gin.Context) {
	appG := app.Gin{C: c}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func Watch(c *gin.Context) {
	appG := app.Gin{C: c}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

type ServiceEndpoint struct {
	RegionId     string `json:"region_id"`
	HttpEndpoint string `json:"http_endpoint"`
	HttpPort     int    `json:"http_port"`
	UdpEndpoint  string `json:"udp_endpoint"`
	UdpPort      int    `json:"udp_port"`
}

func PingServers(c *gin.Context) {
	appG := app.Gin{C: c}
	// 获取分页参数（带默认值）
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	// 参数验证
	if page < 1 || pageSize < 1 || pageSize > 100 {
		appG.Response(200, conf.InvalidParams, nil)
		return
	}

	list := make([]ServiceEndpoint, 0)
	list = append(list, ServiceEndpoint{
		RegionId:     "",
		HttpEndpoint: "",
		HttpPort:     0,
		UdpEndpoint:  "",
		UdpPort:      0,
	})

	data := make(map[string]interface{})
	data["list"] = list
	data["total"] = 100
	data["page"] = page
	data["page_size"] = pageSize
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

package sdk

import (
	"fmt"
	"math"
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/controllers"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service"
	"github.com/astaxie/beego/logs"
	"github.com/gin-gonic/gin"
)

type Room struct {
	CreatedAt           string            `json:"created_at"`
	CustomProperties    map[string]string `json:"custom_properties"`
	ExpirationTime      string            `json:"expiration_time"`
	ExternalUniqueId    string            `json:"external_unique_id"`
	Id                  int               `json:"id"`
	JoinCode            string            `json:"join_code"`
	MaxPlayers          int               `json:"max_players"`
	MultiverseProfileId string            `json:"multiverse_profile_id"`
	Name                string            `json:"name"`
	Namespace           string            `json:"namespace"` // 房间类型，例如：路人局，天梯赛，娱乐模式等
	OwnerId             string            `json:"owner_id"`  // 房主的ID
	PlayerCount         int               `json:"player_count"`
	RoomTTLInMinutes    int               `json:"room_ttl_in_minutes"`
	RoomUUID            string            `json:"room_uuid"`
	Status              string            `json:"status"` // 房间状态SERVER_ALLOCATED/READY/RUNNING
	Type                string            `json:"type"`
	UosAppId            string            `json:"uos_app_id"`
	UpdatedAt           string            `json:"updated_at"`
	Visibility          string            `json:"visibility"`
}

type GetRoomsResp struct {
	Rooms      []Room `json:"rooms"`
	TotalCount int    `json:"total_count"`
}

type GameServerPort struct {
	Name       string `json:"name"`
	Port       int    `json:"port"`
	PortPolicy int    `json:"portPolicy"`
	Protocol   string `json:"protocol"`
}

type AllocationInfo struct {
	AllocationTTL   string           `json:"allocation_ttl"`
	CreatedAt       string           `json:"created_at"`
	CreatedByUser   string           `json:"created_by_user"`
	AppID           string           `json:"app_id"`
	GameServerName  string           `json:"game_server_name"`
	GameServerPorts []GameServerPort `json:"game_server_ports"`
	Ip              string           `json:"ip"`
	ModifiedAt      string           `json:"modified_at"`
	ModifiedByUser  string           `json:"modified_by_user"`
	Msg             string           `json:"msg"`
	ProfileID       string           `json:"profile_id"`
	ProfileName     string           `json:"profile_name"`
	ProfileRevision string           `json:"profile_revision"` // 启动配置版本
	RegionID        string           `json:"region_id"`
	RegionName      string           `json:"region_name"`
	Status          string           `json:"status"`
	UUID            string           `json:"uuid"`
}

type RoomDetail struct {
	AllocationInfo AllocationInfo `json:"allocation_info"`
	Players        []string       `json:"players"`
	RoomInfo       RoomInfo       `json:"room_info"`
}

type RoomInfo struct {
	CreatedAt           string            `json:"created_at"`
	CustomProperties    map[string]string `json:"custom_properties"`
	ExpirationTime      uint64            `json:"expiration_time"`
	ExternalUniqueID    string            `json:"external_unique_id"`
	JoinCode            string            `json:"join_code"`
	MaxPlayers          uint64            `json:"max_players"`
	MultiverseProfileID string            `json:"multiverse_profile_id"`
	Name                string            `json:"name"`
	Namespace           string            `json:"namespace"`
	OwnerID             string            `json:"owner_id"`
	PlayerCount         uint64            `json:"player_count"`
	RoomTTLInMinutes    uint64            `json:"room_ttl_in_minutes"`
	RoomUUID            string            `json:"room_uuid"`
	Status              string            `json:"status"`
	Type                string            `json:"type"`
	AppID               string            `json:"app_id"`
	Visibility          string            `json:"visibility"`
}

func GetServerInfoAsync(c *gin.Context) {
	appG := app.Gin{C: c}

	data := make(map[string]interface{})
	data["room_uuid"] = "aafd3a7f-76d1-4527-b030-2dc4b61e3ce6"
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func GetRooms(c *gin.Context) {
	appG := app.Gin{C: c}
	page, pageSize, offset := controllers.GetPageParam(c)
	appID := c.Param("app_id")

	rooms, total, err := service.GetRooms(c, appID, offset, pageSize)
	if err != nil {
		appG.Error(conf.NotFound, err.Error(), "")
		return
	}
	if err != nil {
		return
	}
	data := make(map[string]interface{})
	data["list"] = rooms
	data["total"] = total
	data["page"] = page
	data["page_size"] = pageSize
	data["total_page"] = int(math.Ceil(float64(total) / float64(pageSize)))
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func GetRoom(c *gin.Context) {
	appG := app.Gin{C: c}
	roomID := c.Param("room_id")

	room, err := service.GetRoom(c, roomID)
	if err != nil {
		appG.Error(conf.NotFound, err.Error(), "")
		return
	}
	profile, err := service.GetProfile(room.ProfileID)
	if err != nil {
		return
	}

	data := RoomDetail{
		AllocationInfo: AllocationInfo{
			AllocationTTL:  "",
			CreatedAt:      "",
			CreatedByUser:  "",
			AppID:          "",
			GameServerName: "",
			GameServerPorts: []GameServerPort{
				{
					Name:       "",
					Port:       8888,
					PortPolicy: 0,
					Protocol:   "UDP",
				},
				{
					Name:       "",
					Port:       9999,
					PortPolicy: 0,
					Protocol:   "TCP",
				},
			},
			Ip:              "",
			ModifiedAt:      "",
			ModifiedByUser:  "",
			Msg:             "",
			ProfileID:       profile.ProfileID,
			ProfileName:     "",
			ProfileRevision: "",
			RegionID:        "",
			RegionName:      "",
			Status:          "",
			UUID:            profile.ProfileID,
		},
		Players: []string{"player1", "player2"},
		RoomInfo: RoomInfo{
			CreatedAt: room.CreatedAt,
			CustomProperties: map[string]string{
				"aa": "11",
				"bb": "22",
			},
			ExpirationTime:      room.ExpirationTime,
			ExternalUniqueID:    "",
			JoinCode:            room.JoinCode,
			MaxPlayers:          room.MaxPlayers,
			MultiverseProfileID: room.ProfileID,
			Name:                room.Name,
			Namespace:           room.Namespace,
			OwnerID:             room.OwnerID,
			PlayerCount:         room.PlayerCount,
			RoomTTLInMinutes:    room.RoomTTLInMinutes,
			RoomUUID:            room.RoomID,
			Status:              "", //
			Type:                room.Type,
			AppID:               room.AppID,
			Visibility:          room.Visibility,
		},
	}

	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func CreateRoom(c *gin.Context) {
	appG := app.Gin{C: c}

	req := service.Room{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		logs.Info("CreateImage", err)
		appG.Response(200, 400, nil)
		return
	}

	// 参数校验
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	err := service.CreateRoom(c, &req)
	if err != nil {
		appG.Error(conf.ERROR, "创建房间失败", nil)
	}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

type UpdateRoomReq struct {
	CustomProperties map[string]string `json:"custom_properties"`
	ExternalUniqueId string            `json:"external_unique_id"`
	OwnerId          string            `json:"owner_id"`
}

func UpdateRoom(c *gin.Context) {
	appG := app.Gin{C: c}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

type ConfirmJoinRoomReq struct {
	PlayerId string `json:"player_id"`
}

func ConfirmJoinRoom(c *gin.Context) {
	appG := app.Gin{C: c}

	err := service.ConfirmJoinRoom(c, "", "")
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

type JoinRoomReq struct {
	JoinCode string `json:"join_code"`
	PlayerID string `json:"player_id"`
	RoomID   string `json:"room_id"`
}

func JoinRoom(c *gin.Context) {
	appG := app.Gin{C: c}

	req := JoinRoomReq{}
	if err := c.BindJSON(&req); err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}

	err := service.JoinRoom(c, req.PlayerID, req.RoomID, req.JoinCode)
	if err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

type LeaveRoomReq struct {
	PlayerID string `json:"player_id"`
	RoomID   string `json:"room_id"`
}

func LeaveRoom(c *gin.Context) {
	appG := app.Gin{C: c}
	req := LeaveRoomReq{}
	if err := c.BindJSON(&req); err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}

	err := service.LeaveRoom(c, req.PlayerID, req.RoomID)
	if err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

type UpdateRoomStatusReq struct {
	RoomUuid string `json:"room_uuid"`
	Status   string `json:"status"`
}

func UpdateRoomStatus(c *gin.Context) {
	appG := app.Gin{C: c}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

// GetAvailableRooms 查询可用房间
func GetAvailableRooms(c *gin.Context) {
	appG := app.Gin{C: c}
	appID := c.Param("app_id")

	rooms, err := service.GetAvailableRooms(c, appID)
	if err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, rooms)
}

type ReportDynamicInfoReq struct {
	RoomID      string `json:"room_id"`
	PlayerCount int    `json:"player_count"`
	Timestamp   int    `json:"timestamp"`
}

func ReportDynamicInfo(c *gin.Context) {
	appG := app.Gin{C: c}

	req := ReportDynamicInfoReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		logs.Info("CreateImage", err)
		appG.Response(200, 400, nil)
		return
	}

	fmt.Println(req.RoomID, req.Timestamp, req.Timestamp)

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

package api

import (
	"net/http"
	"strconv"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service"
	"github.com/astaxie/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"
)

func ListRooms(c *gin.Context) {
	// todo 需要带搜索
	appG := app.Gin{C: c}
	// 获取分页参数（带默认值）
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.Default<PERSON>y("page_size", "10"))
	status, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("status", "0"))

	// 参数验证
	if page < 1 || pageSize < 1 || pageSize > 100 {
		appG.Response(200, conf.InvalidParams, nil)
		return
	}
	rooms, total, err := service.ListRooms(appG.C, page, pageSize, int64(status))
	if err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}
	data := make(map[string]interface{})
	data["list"] = rooms
	data["total"] = total
	data["page"] = page
	data["page_size"] = pageSize
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func GetRoom(c *gin.Context) {
	appG := app.Gin{C: c}

	roomID := c.Param("room_id")
	room, err := service.GetRoom(appG.C, roomID)
	if err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, room)
}

func CreateRoom(c *gin.Context) {
	appG := app.Gin{C: c}

	req := service.Room{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		appG.Response(200, 400, nil)
		return
	}
	bytes, _ := json.Marshal(req)
	logs.Info("CreateRoom", string(bytes))

	err := service.CreateRoom(c, &req)
	if err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

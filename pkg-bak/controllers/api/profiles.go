package api

import (
	"math"
	"net/http"
	"strings"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/controllers"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/models"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service"
	"github.com/astaxie/beego/logs"
	"github.com/gin-gonic/gin"
)

func CreateProfile(c *gin.Context) {
	appG := app.Gin{C: c}
	req := service.CreateProfileReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.ShouldBind(&req); err != nil {
		appG.Error(conf.ERROR, "数据格式有误，请检查", nil)
		return
	}

	err := service.CreateProfile(c, req)
	if err != nil {
		appG.Error(conf.ERROR, "创建启动配置失败，请稍后再试", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func UpdProfile(c *gin.Context) {
	appG := app.Gin{C: c}
	//profileID := c.Param("profile_id")

	req := models.UpdProfileReq{}

	if req.BufferSize < 0 || req.BufferSize > 100 {
		appG.Error(conf.ERROR, "参数错误", nil)
		return
	}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		appG.Response(200, 400, nil)
		return
	}

	err := models.UpdProfile(&req)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func DeleteProfile(c *gin.Context) {
	appG := app.Gin{C: c}
	profileID := c.Param("profile_id")
	if profileID == "" {
		appG.Error(conf.ERROR, "参数错误", nil)
		return
	}
	err := service.DeleteProfile(c, profileID)
	if err != nil {
		if strings.Contains(err.Error(), "record not found") {
			appG.Error(conf.ERROR, "启动配置不存在", nil)
			return
		}
		appG.Error(conf.ERROR, "删除启动配置失败，请稍后再试", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func GetProfile(c *gin.Context) {
	appG := app.Gin{C: c}

	//appID := c.Param("app_id")
	profileID := c.Param("profile_id")

	profile, err := service.GetProfile(profileID)
	if err != nil {
		appG.Error(conf.ERROR, "查询启动配置失败，请稍后再试", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, profile)
}

func GetProfileList(c *gin.Context) {
	appG := app.Gin{C: c}
	appID := c.Query("app_id")
	logs.Info("GetProfileList", appID)

	page, pageSize, offset := controllers.GetPageParam(c)

	total, profiles, err := service.GetProfileList(c, appID, offset, pageSize)
	if err != nil {
		appG.Error(conf.ERROR, "查询启动配置列表失败，请稍后再试", nil)
		return
	}
	data := make(map[string]interface{})
	data["list"] = profiles
	data["total"] = total
	data["page"] = page
	data["page_size"] = pageSize
	data["total_page"] = int(math.Ceil(float64(total) / float64(pageSize)))
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func GetProfileRevision(c *gin.Context) {
	appG := app.Gin{C: c}
	revisionID := c.Param("revision_id")

	revision, err := service.GetProfileRevision(revisionID)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, revision)
}

func GetProfileRevisionList(c *gin.Context) {
	appG := app.Gin{C: c}
	page, pageSize, offset := controllers.GetPageParam(c)

	// 参数验证
	if page < 1 || pageSize < 1 || pageSize > 100 {
		appG.Response(200, conf.InvalidParams, nil)
		return
	}

	profileID := c.Param("profile_id")
	profiles, total, err := service.GetProfileRevisionList(c, profileID, offset, pageSize)
	if err != nil {
		appG.Error(conf.ERROR, "查询启动配置版本列表失败，请稍后再试", nil)
		return
	}
	data := make(map[string]interface{})
	data["list"] = profiles
	data["total"] = total
	data["page"] = page
	data["page_size"] = pageSize
	data["total_page"] = int(math.Ceil(float64(total) / float64(pageSize)))
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func CreateProfileRevision(c *gin.Context) {
	appG := app.Gin{C: c}

	req := service.CreateProfileRevisionReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}

	_, err := service.CreateProfileRevision(appG.C, &req)
	if err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func UpdProfileRevision(c *gin.Context) {
	appG := app.Gin{C: c}

	req := service.UpdProfileRevisionReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		appG.Response(200, 400, nil)
		return
	}

	err := service.UpdProfileRevision(c, &req)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func DeleteProfileRevision(c *gin.Context) {
	appG := app.Gin{C: c}
	revisionID := c.Param("revision_id")

	err := models.DeleteProfileRevision(c, revisionID)
	if err != nil {
		appG.Response(200, conf.ERROR, nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func ProfileRevisionTestServer(c *gin.Context) {
	appG := app.Gin{C: c}

	data := make(map[string]interface{})
	data["test_id"] = "aafd3a7f-76d1-4527-b030-2dc4b61e3ce6"
	data["revision_id"] = "28943f7f-b0fe-4046-84be-c5b7723496ba"
	data["test_server_status"] = "na"
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func GetProfileRevisionTestServerStatus(c *gin.Context) {
	appG := app.Gin{C: c}

	data := make(map[string]interface{})
	data["test_id"] = "aafd3a7f-76d1-4527-b030-2dc4b61e3ce6"
	data["revision_id"] = "28943f7f-b0fe-4046-84be-c5b7723496ba"
	data["status"] = "na"
	data["reason"] = ""
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func ProfileRevisionCancelTestServer(c *gin.Context) {
	appG := app.Gin{C: c}
	data := make(map[string]interface{})
	data["test_id"] = "aafd3a7f-76d1-4527-b030-2dc4b61e3ce6"
	data["revision_id"] = "28943f7f-b0fe-4046-84be-c5b7723496ba"
	data["test_server_status"] = "deleted"
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

// ProfileRevisionRelease 应用配置
func ProfileRevisionRelease(c *gin.Context) {
	appG := app.Gin{C: c}
	revisionID := c.Param("revision_id")

	err := service.ProfileRevisionRelease(c, revisionID)
	if err != nil {
		logs.Error("配置失败，请稍后再试", err)
		appG.Error(conf.ERROR, "配置失败，请稍后再试", nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

// ReleaseResource 释放资源
func ReleaseResource(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

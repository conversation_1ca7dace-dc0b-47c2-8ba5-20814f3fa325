package api

import (
	"net/http"
	"strconv"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/app"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/service/match_service"
	"github.com/astaxie/beego/logs"
	"github.com/gin-gonic/gin"
)

func CreateMatch(c *gin.Context) {
	appG := app.Gin{C: c}

	req := service.CreateMatchReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		logs.Info("CreateImage", err)
		appG.Response(200, 400, nil)
		return
	}

	err := service.CreateMatch(req)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

// GetMatchTemplates 匹配模板 - todo
func GetMatchTemplates(c *gin.Context) {
	appG := app.Gin{C: c}

	categories, err := service.GetTemplates()
	if err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, categories)
}

// GetRules 参考语句-rules
func GetRules(c *gin.Context) {
	appG := app.Gin{C: c}

	rules, total, err := service.GetMatchRules()
	if err != nil {
		appG.Error(conf.ERROR, err.Error(), nil)
		return
	}

	data := make(map[string]interface{})
	data["list"] = rules
	data["total"] = total
	data["page"] = 1
	data["page_size"] = 20
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

// GetExpansions 参考语句-expansions
func GetExpansions(c *gin.Context) {
	appG := app.Gin{C: c}

	data := make(map[string]interface{})
	expansions := make([]service.Expansion, 0)

	expansions = append(expansions, service.Expansion{
		Description: "两个team的总分之差 - 动态更新",
		Target:      "rules[teamSumRule].maxDistance",
		Steps: []service.Step{
			{
				WaitTimeSeconds: 10,
				Value:           "40",
				Type:            "replace",
			},
			{
				WaitTimeSeconds: 20,
				Type:            "disable",
			},
		},
	})

	data["list"] = expansions
	data["total"] = 100
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

// GetMatch 查询匹配详情
func GetMatch(c *gin.Context) {
	appG := app.Gin{C: c}

	image, err := service.GetMatch()
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, image)
}

// GetMatchList 查询匹配列表
func GetMatchList(c *gin.Context) {
	appG := app.Gin{C: c}
	// 获取分页参数（带默认值）
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	// 参数验证
	if page < 1 || pageSize < 1 || pageSize > 100 {
		appG.Response(200, conf.InvalidParams, nil)
		return
	}
	matches, err := service.GetMatchList(page, pageSize)
	if err != nil {
		return
	}
	data := make(map[string]interface{})
	data["list"] = matches
	data["total"] = 100
	data["page"] = page
	data["page_size"] = pageSize
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

// DeleteMatch 删除匹配
func DeleteMatch(c *gin.Context) {
	appG := app.Gin{C: c}

	err := service.DeleteMatch()
	if err != nil {
		appG.Response(200, conf.ERROR, nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

// UpdMatch 修改匹配
func UpdMatch(c *gin.Context) {
	appG := app.Gin{C: c}

	req := service.UpdMatchReq{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		logs.Info("CreateImage", err)
		appG.Response(200, 400, nil)
		return
	}

	err := service.UpdMatch(&req)
	if err != nil {
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func CreateTestPlayers(c *gin.Context) {
	appG := app.Gin{C: c}

	err := service.MatchTest()
	if err != nil {
		appG.Response(200, conf.ERROR, nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func MatchTest(c *gin.Context) {
	appG := app.Gin{C: c}

	err := service.MatchTest()
	if err != nil {
		appG.Response(200, conf.ERROR, nil)
		return
	}

	response := MatchResponse{
		Matches: []Match{
			{
				Proposals: []Proposal{
					{
						Teams: []Team{
							{
								TeamDefinitionName: "quick-game",
								TeamName:           "quick-game",
								Tickets: []Ticket{
									{
										ID: "900de952-fe74-4f8e-ae49-c616b692c6fd",
										Players: []Player{
											{ID: "3", Attributes: map[string]interface{}{}},
										},
									},
									// 其他 tickets...
								},
							},
						},
					},
				},
				WaitTimeSeconds: 0,
			},
		},
	}
	appG.Response(http.StatusOK, conf.SUCCESS, response)
}

func CreateTicket(c *gin.Context) {
	appG := app.Gin{C: c}

	req := match_service.TicketRequest{}
	// 自动解析 JSON 到结构体，并校验 binding 标签
	if err := c.BindJSON(&req); err != nil {
		logs.Info("CreateImage", err)
		appG.Response(200, 400, nil)
		return
	}

	ticket, err := match_service.CreateTicket(req)
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}

	data := map[string]string{
		"ticket": ticket,
	}
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

// GetTicketStatus 获取匹配状态
func GetTicketStatus(c *gin.Context) {
	appG := app.Gin{C: c}

	ticketID := c.Param("ticket_id")
	connection, err := match_service.GetTicketStatus(ticketID)
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}
	data := map[string]interface{}{
		"ticket_id": ticketID,
		"matched":   false,
		"address":   "",
	}
	if connection != "" {
		data["address"] = connection
	}
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func DeleteTicket(c *gin.Context) {
	appG := app.Gin{C: c}

	ticketId := c.Param("ticket_id")
	err := match_service.DeleteTicket(ticketId)
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}

	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func StartBackFill(c *gin.Context) {
	appG := app.Gin{C: c}
	backFillID, err := match_service.CreateBackFill()
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}
	data := map[string]string{
		"backfill_id": backFillID,
	}
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func StopBackFill(c *gin.Context) {
	appG := app.Gin{C: c}
	backFillID := c.Param("backfill_id")
	err := match_service.DeleteBackFill(backFillID)
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}
	appG.Response(http.StatusOK, conf.SUCCESS, nil)
}

func IsBackFillStarted(c *gin.Context) {
	appG := app.Gin{C: c}
	backFillID := c.Param("backfill_id")
	backFillID, err := match_service.GetBackFill(backFillID)
	if err != nil {
		appG.Error(500, err.Error(), nil)
		return
	}

	data := map[string]string{
		"backfill_id": backFillID,
	}
	appG.Response(http.StatusOK, conf.SUCCESS, data)
}

func GetMatchData(c *gin.Context) {
}

func WatchMatch(c *gin.Context) {
	/**
	功能： 查询匹配结果（可能包括房间ID、服务器地址、玩家列表等）
	注意：Open Match 本身不维护 Match 实体，MatchID 是你平台维护的概念，需要你结合 MMF 和 Director 输出信息自行记录。
	监听匹配数据（WebSocket）]
	功能： 可使用 WebSocket 或 SSE 推送匹配进度或结果
	*/
}

func ListBackFillPlayers(c *gin.Context) {
	/**
	  功能： 获取回填玩家列表
	  注意：查询当前 Backfill 被加入的新玩家（你要记录 Player 信息）
	*/
}

func WatchBackFill(c *gin.Context) {
	/**
	  功能： 监听回填数据（WebSocket）
	  注意： 可使用 WebSocket 或 SSE 推送回填进度或结果
	*/
}

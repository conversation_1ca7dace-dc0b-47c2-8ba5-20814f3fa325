package controllers

import (
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/conf"
	"github.com/gin-gonic/gin"
)

func HandleNotFound(c *gin.Context) {
	code := conf.NotFound
	c.JSONP(http.StatusOK, code)
	return
}

func ErrHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		if length := len(c.Errors); length > 0 {
			e := c.Errors[length-1]
			err := e.Err
			if err != nil {
				//var Err *Error
				//if e, ok := err.(*Error); ok {
				//	Err = e
				//} else if e, ok := err.(error); ok {
				//	Err = OtherError(e.Error())
				//} else {
				//	Err = ServerError
				//}
				// 记录一个错误的日志
				c.JSONP(http.StatusOK, conf.ERROR)
				return
			}
		}

	}
}

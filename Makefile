
# 生成Swagger API文档
# 使用swag工具解析注释生成OpenAPI文档
.PHONY: swagger
swagger:
	swag init -g cmd/services/main.go --parseDependency \
	--exclude pkg-bak
# =============================================================================
# 代码生成任务
# =============================================================================

# 运行自定义代码生成器
# 生成controller、model等模板代码
.PHONY: gen
gen:
	go run cmd/gen/main.go

# =============================================================================
# 开发运行任务
# =============================================================================

# 运行开发服务器
# 直接编译并运行main.go
.PHONY: run
run:
	go run cmd/services/main.go
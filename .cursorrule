# RESTful API 设计规范
## 使用资源命名：
使用名词来表示资源，而不是动词。例如，/users 而不是 /getUsers。
使用复数形式表示集合。例如，/users 表示用户集合。
使用小写字母和连字符分隔单词。例如，/user-profiles 而不是 /userProfiles。
嵌套资源使用层次结构。例如，/users/{id}/orders 表示特定用户的订单。

## HTTP 动词：
GET：用于获取资源。
POST：用于创建新资源。
PUT：用于更新资源的全部。
PATCH：用于部分更新资源。
DELETE：用于删除资源。

## 状态码：
200 OK：请求成功。
201 Created：成功创建资源。
204 No Content：成功处理请求，但没有返回内容。
400 Bad Request：请求无效。
401 Unauthorized：未授权。
403 Forbidden：禁止访问。
404 Not Found：资源未找到。
409 Conflict：资源冲突。
422 Unprocessable Entity：请求格式正确但语义错误。
429 Too Many Requests：请求过于频繁。
500 Internal Server Error：服务器内部错误。
503 Service Unavailable：服务不可用。

# Golang 代码规范

## 命名规范：
1. 使用驼峰命名法（CamelCase）。
2. 导出的标识符首字母大写，私有标识符首字母小写。
3. 常量使用全大写字母，单词间用下划线分隔。
4. 接口名通常以 "er" 结尾，如 Reader、Writer。
5. 包名使用小写字母，简短且有意义。

## 代码组织：
1. 结构体的方法代码放在结构体定义的后面。
2. 相关的类型和函数应该组织在一起。
3. 导入包按标准库、第三方库、本地包的顺序分组。
4. 每个文件应该有明确的职责，避免过大的文件。

## 错误处理：
1. 始终检查并处理错误。
2. 使用 errors.New() 或 fmt.Errorf() 创建错误。
3. 错误信息应该清晰、具体，便于调试。
4. 在适当的层级处理错误，避免错误传递过深。

## 并发安全：
1. 使用 sync.Mutex 或 sync.RWMutex 保护共享数据。
2. 优先使用 channel 进行 goroutine 间通信。
3. 避免共享内存，通过通信共享内存。
4. 使用 sync.WaitGroup 等待多个 goroutine 完成。
5. 谨慎使用全局变量，必要时加锁保护。

## 性能优化：
1. 避免不必要的内存分配，复用对象池。
2. 使用 strings.Builder 而不是字符串拼接。
3. 合理使用 make() 预分配 slice 和 map 的容量。
4. 避免在循环中进行昂贵的操作。
5. 使用 context.Context 进行请求超时和取消控制。
6. 实现连接池复用数据库连接。

## 架构设计：
1. 遵循分层架构：Controller -> Service -> Repository -> Model。
2. 使用依赖注入，便于测试和维护。
3. 实现接口隔离，依赖抽象而非具体实现。
4. 遵循单一职责原则，每个组件职责明确。
5. 使用中间件处理横切关注点（日志、认证、限流等）。

## API 设计：
1. 使用结构体定义请求和响应参数。
2. 实现统一的响应格式。
3. 添加参数验证和数据校验。
4. 实现分页查询，避免返回大量数据。
5. 提供 API 版本控制机制。
6. 实现请求限流和熔断保护。

## k8s operator 设计规范：

### 架构设计原则：
1. 遵循 Operator Pattern，将运维知识编码到软件中。
2. 实现声明式 API，用户只需描述期望状态。
3. 使用控制器模式，持续监控和调和资源状态。
4. 遵循 Kubernetes 设计理念：可观测性、可扩展性、可维护性。
5. 实现幂等性操作，确保多次执行结果一致。

### CRD 设计规范：
1. 使用语义化版本控制 API 版本（v1alpha1, v1beta1, v1）。
2. 定义清晰的资源名称和命名空间策略。
3. 实现合理的字段验证和默认值设置。
4. 使用 OpenAPI 规范定义 API 结构。
5. 实现版本转换和兼容性保证。
6. 添加详细的字段注释和示例。

### 控制器设计规范：
1. 实现 Reconcile 循环，处理资源状态变化。
2. 使用 OwnerReference 建立资源所有权关系。
3. 实现优雅的错误处理和重试机制。
4. 添加资源事件记录和状态更新。
5. 实现并发控制和资源锁机制。
6. 使用 Finalizer 确保资源清理。

### 调和逻辑规范：
1. 实现状态机模式，管理资源生命周期。
2. 使用条件（Conditions）表示资源状态。
3. 实现增量更新，避免全量重建。
4. 添加资源依赖关系处理。
5. 实现配置验证和冲突检测。
6. 使用事件驱动架构提高响应性。

### 错误处理和恢复：
1. 实现指数退避重试策略。
2. 区分临时错误和永久错误。
3. 添加错误分类和优先级处理。
4. 实现熔断器模式防止级联故障。
5. 记录详细的错误上下文信息。
6. 提供手动恢复和重置机制。

### 可观测性设计：
1. 实现结构化日志记录。
2. 添加 Prometheus 指标收集。
3. 使用分布式追踪监控请求链路。
4. 实现健康检查和就绪检查。
5. 添加资源状态监控和告警。
6. 提供调试和诊断工具。

### 安全设计：
1. 实现 RBAC 权限控制。
2. 使用 ServiceAccount 进行身份认证。
3. 实现准入控制验证资源。
4. 添加敏感信息加密存储。
5. 实现审计日志记录。
6. 遵循最小权限原则。

### 性能优化：
1. 实现资源缓存和索引优化。
2. 使用批量操作减少 API 调用。
3. 实现并发控制和限流机制。
4. 优化内存使用和垃圾回收。
5. 使用连接池复用网络连接。
6. 实现资源预分配和复用。

### 测试规范：
1. 编写单元测试覆盖核心逻辑。
2. 实现集成测试验证端到端流程。
3. 使用 envtest 进行 API 测试。
4. 编写性能测试验证扩展性。
5. 实现混沌测试验证容错能力。
6. 添加回归测试防止功能退化。

### 部署和运维：
1. 使用 Helm Chart 管理部署。
2. 实现多集群部署支持。
3. 添加滚动更新和回滚机制。
4. 实现配置热更新能力。
5. 提供监控和告警配置。
6. 实现备份和恢复策略。

### 文档和示例：
1. 编写详细的 API 文档。
2. 提供丰富的使用示例。
3. 实现快速开始指南。
4. 添加故障排除文档。
5. 维护变更日志和升级指南。
6. 提供最佳实践和设计模式。

### 代码生成和工具：
1. 使用 controller-gen 生成代码。
2. 实现自定义代码生成器。
3. 使用 kubebuilder 脚手架工具。
4. 添加代码格式化和静态检查。
5. 实现自动化测试和部署流水线。
6. 使用 Makefile 管理构建流程。

## 数据库操作：
1. 使用事务确保数据一致性。
2. 实现数据库连接池。
3. 避免 N+1 查询问题。
4. 使用索引优化查询性能。
5. 实现软删除机制。
6. 添加数据库操作的错误重试机制。

## 测试规范：
1. 编写单元测试，覆盖率应达到 80% 以上。
2. 使用表驱动测试处理多种输入情况。
3. 编写集成测试验证组件间协作。
4. 使用 mock 隔离外部依赖。
5. 测试函数命名格式：Test{FunctionName}_{Scenario}。
6. 编写基准测试验证性能。

## 日志规范：
1. 使用结构化日志，便于查询和分析。
2. 记录关键操作和错误信息。
3. 避免记录敏感信息（密码、token 等）。
4. 使用不同日志级别：DEBUG、INFO、WARN、ERROR。
5. 添加 trace_id 便于链路追踪。

## 监控和指标：
1. 实现健康检查接口。
2. 收集关键业务指标。
3. 监控 API 响应时间和错误率。
4. 实现告警机制。
5. 添加链路追踪支持。

## 安全规范：
1. 实现 JWT 认证和授权。
2. 使用 HTTPS 传输敏感数据。
3. 实现输入验证和 SQL 注入防护。
4. 添加访问控制和权限管理。
5. 实现敏感数据加密存储。

## 文档要求：
1. 为导出的函数和类型添加注释。
2. 使用 Swagger 生成 API 文档。
3. 编写 README 说明项目架构和使用方法。
4. 维护变更日志（CHANGELOG）。

## 代码质量要求：
1. 生成高质量的代码，可用于生产环境。
2. 代码具有良好的可读性和可维护性。
3. 设计具有可扩展性，支持业务增长。
4. 实现高性能和高并发处理能力。
5. 确保并发安全和数据一致性。
6. 考虑代码要可测试性，方便mock测试。


## 项目结构规范：
cmd/：应用程序入口
internal/：私有应用程序代码
pkg/：可被外部应用程序使用的库代码
config/：配置文件
docs/：文档文件
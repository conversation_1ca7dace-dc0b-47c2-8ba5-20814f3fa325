apiVersion: apps/v1
kind: Deployment
metadata:
  name: multiverse-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: multiverse
  template:
    metadata:
      labels:
        app: multiverse
    spec:
      containers:
        - name: multiverse
          image: multiverse:latest
          imagePullPolicy: Never # 💥 加上这个才不去拉远程镜像！
          ports:
            - containerPort: 8000
          volumeMounts:
            - name: config-volume
              mountPath: /etc/multiverse
              readOnly: true
      volumes:
        - name: config-volume
          configMap:
            name: multiverse-config


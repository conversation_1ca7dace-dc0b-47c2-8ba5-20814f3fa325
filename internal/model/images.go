package model

// BaseImageConfig 基础镜像配置
type BaseImageConfig struct {
	Name        string `json:"name"`
	Image       string `json:"image"`
	Description string `json:"description"`
	Category    string `json:"category"`
	Icon        string `json:"icon"`
}

func (b *BaseImageConfig) TableName() string {
	return "base_image_config"
}

func (b *BaseImageConfig) TableComment() string {
	return "基础镜像配置"
}

// 镜像信息
type ImageInfo struct {
	BaseModel
	ProjectConfigurationID int64  `gorm:"uniqueIndex:idx_projectconfig_tagname;not null;comment:所属项目配置ID"`
	TagName                string `gorm:"uniqueIndex:idx_projectconfig_tagname;not null;comment:镜像标签"`
	UUID                   string `gorm:"not null;comment:UUID"`
	SystemName             string `gorm:"not null;comment:操作系统名称"`
	ImageURL               string `gorm:"not null;comment:镜像URL 创建pod时用到"`
}

func (i *ImageInfo) TableName() string {
	return "image_info"
}

func (i *ImageInfo) TableComment() string {
	return "镜像信息"
}

// 镜像配置
type ImageConfig struct {
	BaseModel
	StartupID       int64   `gorm:"not null;comment:所属启动配置ID"`
	StartupConfigCT int64   `gorm:"not null;comment:启动配置创建的时间"`
	ImageInfoID     int64   `gorm:"not null;comment:所属镜像ID"`
	ImageTag        string  `gorm:"not null;comment:镜像标签"`
	Image           string  `gorm:"not null;comment:镜像"`
	CPU             float32 `gorm:"not null;comment:CPU配置"`
	StartupTimeout  int64   `gorm:"not null;comment:启动超时时长"`
	Ports           []*Port `gorm:"serializer:json;not null;comment:端口配置"`
}

func (i *ImageConfig) TableName() string {
	return "image_config"
}

func (i *ImageConfig) TableComment() string {
	return "镜像配置"
}

package model

import (
	"time"
)

// CloudAccount 云账号模型
type CloudAccount struct {
	BaseModel

	// 基本信息
	Name        string `gorm:"size:128;not null;default:'';comment:云账号名称"` // 云账号名称
	Description string `gorm:"size:256;default:'';comment:账号描述"`           // 账号描述
	Status      string `gorm:"size:32;not null;default:'init';comment:状态"` // 状态
	Enabled     bool   `gorm:"not null;default:true;comment:是否启用"`         // 是否启用

	// 云服务商信息
	ProviderID  uint   `gorm:"not null;default:1;comment:云服务提供商ID"`        // 云服务提供商ID
	AccountName string `gorm:"size:128;not null;default:'';comment:云账号名称"` // 云账号名称
	AccountType string `gorm:"size:32;not null;default:'';comment:账号类型"`   // 账号类型（主账号/子账号）

	// 认证信息
	AK       string `gorm:"size:256;not null;default:'';comment:Access Key"` // Access Key
	SK       string `gorm:"size:256;not null;default:'';comment:Secret Key"` // Secret Key
	Endpoint string `gorm:"size:2048;not null;default:'';comment:Endpoint"`  // Endpoint

	// 同步信息
	LastSyncAt    *time.Time `gorm:"type:datetime;comment:最后同步时间"`                 // 最后同步时间
	LastSyncError string     `gorm:"type:text;not null;default:'';comment:最后同步错误"` // 最后同步错误

	// 其他配置
	Options map[string]string `gorm:"serializer:json;not null;default:'{}';comment:其他配置"` // 其他配置（用于存储不同云厂商的特定配置）
}

func (CloudAccount) TableName() string {
	return "cloudaccounts"
}

func (CloudAccount) TableComment() string {
	return "云账号"
}

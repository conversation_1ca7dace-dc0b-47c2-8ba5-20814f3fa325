package model

type Cluster struct {
	BaseModel
	MasterIp          string `gorm:"column:master_ip;not null;comment:主节点ip"`
	Port              int    `gorm:"column:port;not null;comment:端口"`
	PodSubnet         string `gorm:"column:pod_subnet;not null;comment:pod网段地址"`
	ServiceSubnet     string `gorm:"column:service_subnet;not null;comment:service网段"`
	Name              string `gorm:"column:name;not null;comment:集群名称"`
	ImageRepository   string `gorm:"column:image_repository;not null;comment:镜像库"`
	WorkerIPs         string `gorm:"column:worker_ips;not null;comment:工作节点ip"`
	Region            string `gorm:"column:region;not null;comment:地域"`
	Zone              string `gorm:"column:zone;not null;comment:地区"`
	ClusterConfigName string `gorm:"column:cluster_config_name;not null;comment:集群配置名称"`
	ClusterConfigPath string `gorm:"column:cluster_config_path;not null;comment:集群配置路径"`
}

func (Cluster) TableName() string {
	return "cluster"
}

func (Cluster) TableComment() string {
	return "集群信息表"
}

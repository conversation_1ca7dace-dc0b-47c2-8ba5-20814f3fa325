package model

type ProjectConfiguration struct {
	BaseModel
	ProjectID     int64  `gorm:"uniqueIndex:idx_project_tenant;not null;comment:所属项目ID"` // 所属项目 ID
	TenantID      int64  `gorm:"uniqueIndex:idx_project_tenant;not null;comment:所属租户ID"` // 所属租户 ID
	ServerTimeout int64  `gorm:"not null;default:0;comment:服务器超时时长"`
	ServerOS      string `gorm:"not null;default:'';comment:服务器操作系统"`
	CallbackURL   string `gorm:"not null;default:'';comment:分配完成后回调URL"`
}

func (ProjectConfiguration) TableName() string {
	return "project_configurations"
}

func (ProjectConfiguration) TableComment() string {
	return "项目配置"
}

package model

import "time"

type UserRegion struct {
	BaseModel
	UserId      string    `gorm:"column:user_id;type:bigint(20);not null;comment:用户ID"`
	UserName    string    `gorm:"column:user_name;type:varchar(64);not null;comment:用户名称"`
	RegionName  string    `gorm:"column:region_name;type:varchar(64);not null;comment:地域名称"`
	Zone        string    `gorm:"column:zone;type:varchar(64);not null;comment:地区名称"`
	EnableTime  time.Time `gorm:"column:enable_time;type:datetime;not null;comment:启用时间"`
	DisableTime time.Time `gorm:"column:disable_time;type:datetime;not null;comment:禁用时间"`
	Enable      int32     `gorm:"column:enable;type:tinyint(4);not null;default:1;comment:启用状态"` //1：禁用 2：启用
}

func (UserRegion) TableName() string {
	return "user_region"
}

func (UserRegion) TableComment() string {
	return "用户地域绑定关系表"
}

package model

type StartupConfiguration struct {
	BaseModel
	ProjectID              int64  `gorm:"index;not null;comment:所属项目ID"` // 所属项目 ID
	TenantID               int64  `gorm:"index;not null;comment:所属租户ID"` // 所属租户 ID
	ProjectConfigurationID int64  `gorm:"index;not null;comment:所属项目配置ID"`
	UUID                   string `gorm:"index;not null;comment:UUID"`
	Name                   string `gorm:"not null;comment:名称"`
	ImageConfigID          int64  `gorm:"comment:镜像配置ID"`
	ImageConfigCT          int64  `gorm:"comment:镜像配置创建的时间"`
	ImageTag               string `gorm:"comment:镜像标签"`
}

func (s *StartupConfiguration) TableName() string {
	return "startup_configurations"
}

func (s *StartupConfiguration) TableComment() string {
	return "启动配置"
}

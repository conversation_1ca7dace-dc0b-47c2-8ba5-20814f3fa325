package model

import (
	"time"
)

type ServicesAllocation struct {
	BaseModel
	ProjectConfigurationID   int64             `gorm:"not null;comment:项目配置ID"`
	UUID                     string            `gorm:"not null;comment:分配ID"`
	StartupConfigurationID   int64             `gorm:"not null;comment:启动配置ID"`
	StartupConfigurationName string            `gorm:"not null;comment:启动配置名称"`
	Region                   string            `gorm:"not null;comment:区域名称"` // 区域名称
	AllocationTTL            int64             `gorm:"not null;comment:分配TTL"`
	Env                      map[string]string `gorm:"serializer:json;not null;comment:环境变量"`
	Ports                    []*Port           `gorm:"serializer:json;not null;comment:端口"`
	Status                   string            `gorm:"not null;comment:状态"`
	Msg                      string            `gorm:"not null;comment:消息"`
	CreatedAt                time.Time         `gorm:"not null;comment:创建时间"`
	CreatedByUser            string            `gorm:"not null;comment:创建者"`
	ModifiedAt               time.Time         `gorm:"not null;comment:修改时间"`
	ModifiedByUser           string            `gorm:"not null;comment:修改者"`
	DeletedAt                time.Time         `gorm:"not null;comment:删除时间"`
	DeletedByUser            string            `gorm:"not null;comment:删除者"`
}

func (s *ServicesAllocation) TableName() string {
	return "services_allocations"
}

func (s *ServicesAllocation) TableComment() string {
	return "服务分配"
}

// 参考uos.unity的allocation结构体
// https://multiverse.scaling.unity.cn/v1/allocation/games/{appid}allocations/{uuid}
// {
//     "allocation": {
//         "uuid": "8d2c12cd-c3f6-4742-9fae-fc092cee1d27",
//         "gameId": "c8291248-be6f-4fc8-92d4-c74f1d1decd4",
//         "profileId": "88807b4d-d8ff-42ef-a11f-1acf233542f1",
//         "regionId": "7bba5ff1-b4a3-498c-9c0b-24cad73cff54",
//         "ip": "************",
//         "gameServerPorts": [
//             {
//                 "port": 7756,
//                 "protocol": "UDP",
//                 "name": "udp-9998"
//             }
//         ],
//         "gameServerName": "demo02-qb2w8",
//         "createdAt": "2025-07-30T09:27:07.833174Z",
//         "createdByUser": "",
//         "modifiedAt": null,
//         "modifiedByUser": "",
//         "fulfilledAt": "2025-07-30T09:27:17.038204Z",
//         "deletedAt": null,
//         "deletedByUser": "",
//         "status": "allocated",
//         "msg": "",
//         "allocationTTL": "18m0s",
//         "profileRevision": null,
//         "regionName": "Shanghai",
//         "profileName": "demo02"
//     }
// }

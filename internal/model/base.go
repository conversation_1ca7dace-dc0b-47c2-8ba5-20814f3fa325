package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

const (
	// 资源状态
	RESOURCE_STATUS_INACTIVE = "inactive" // 不可用状态 （例如在阿里云上创建失败）
	RESOURCE_STATUS_PENDING  = "pending"  // 待同步状态 （例如在本地数据库上创建成功，等待同步到阿里云）
	RESOURCE_STATUS_ACTIVE   = "active"   // 活跃状态
	RESOURCE_STATUS_DELETED  = "deleted"  // 已删除状态
	// 同步状态
	SYNC_STATUS_PENDING = "pending" // 待同步状态
	SYNC_STATUS_SUCCESS = "success" // 同步成功状态
	SYNC_STATUS_ERROR   = "error"   // 同步失败状态
)

// 端口定义
type Port struct {
	Port     int    `gorm:"not null;comment:端口"`
	Protocol string `gorm:"not null;comment:协议"`
	Name     string `gorm:"not null;comment:名称"`
}

// PageResult 分页结果
type PageResult[T any] struct {
	Data     []T   `json:"data"`      // 列表数据
	Total    int64 `json:"total"`     // 总记录数
	Page     int   `json:"page"`      // 当前页码
	PageSize int   `json:"page_size"` // 每页数量
}

type Result[t any] struct {
	Data t `json:"data"`
}

type BaseModel struct {
	ID int64 `gorm:"primarykey"` // 主键ID
	// CreatedAt time.Time `gorm:"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:创建时间"` // 创建时间（非空，默认当前时间）
	// UpdatedAt time.Time `gorm:"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:更新时间"` // 更新时间（非空，默认当前时间，更新时自动更新）
	// swagger:ignore
	CreatedAt time.Time `gorm:"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:创建时间;autoCreateTime"`
	// swagger:ignore                           // 创建时间（非空，默认当前时间）
	UpdatedAt time.Time `gorm:"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间;autoUpdateTime"` // 更新时间（非空，默认当前时间，更新时自动更新时间）
	// swagger:ignore
	Deleted bool `gorm:"default:false;comment:是否已删除"`
	// swagger:ignore
	DbVersion uint `gorm:"not null;default:0;comment:数据库版本用于加锁"`
}

// CloudResource 云资源基础模型
type CloudResource struct {
	ExternalID string `gorm:"size:128;not null;default:'';comment:外部云平台资源ID"` // 外部云平台资源ID
	ProviderID uint   `gorm:"not null;default:1;comment:云服务提供商ID"`            // 云服务提供商ID
	RegionName string `gorm:"size:128;not null;default:'';comment:资源所在区域"`    // 资源所在区域
	AccountID  int64  `gorm:"not null;default:0;comment:云账号ID"`               // 云账号ID

	// 同步信息
	SyncAt        time.Time `gorm:"type:timestamp;default:CURRENT_TIMESTAMP;comment:最后同步时间"` // 最后同步时间
	Status        string    `gorm:"size:32;not null;default:'active';comment:资源状态"`          // 资源状态
	SyncStatus    string    `gorm:"type:text;default:'';comment:同步状态"`                       // 同步状态
	LastSyncError string    `gorm:"type:text;default:'';comment:最后同步错误"`                     // 最后同步错误
}

type CommentedTable interface {
	TableName() string
	TableComment() string
}

func AutoMigrateWithComments(db *gorm.DB, tables ...CommentedTable) {
	for _, t := range tables {
		db.AutoMigrate(t)
		db.Exec(fmt.Sprintf("ALTER TABLE `%s` COMMENT = '%s'", t.TableName(), t.TableComment()))
	}
}

// FilterCondition 过滤条件
type FilterCondition struct {
	Operator string      `json:"operator"` // 操作符: eq(等于), ne(不等于), gt(大于), lt(小于), ge(大于等于), le(小于等于), like(模糊匹配), in(包含), nin(不包含), between(范围)
	Value    interface{} `json:"value"`    // 值
}

// ListQuery 列表查询通用结构
type ListQuery struct {
	Page     int                        `json:"page" form:"page"`           // 页码
	PageSize int                        `json:"page_size" form:"page_size"` // 每页数量
	Filters  map[string]FilterCondition `json:"filters" form:"filters"`     // 过滤条件
}

// Validate 验证查询参数
func (q *ListQuery) Validate() {
	if q.Page < 1 {
		q.Page = 1
	}
	if q.PageSize < 1 && q.PageSize != -1 {
		q.PageSize = 10
	}
	if q.PageSize > 100 && q.PageSize != -1 {
		q.PageSize = 100
	}
}

// ApplyFilters 应用过滤条件到查询
func (q *ListQuery) ApplyFilters(db *gorm.DB) *gorm.DB {
	if len(q.Filters) == 0 {
		return db
	}

	for field, condition := range q.Filters {
		switch condition.Operator {
		case "eq":
			db = db.Where(fmt.Sprintf("%s = ?", field), condition.Value)
		case "ne":
			db = db.Where(fmt.Sprintf("%s != ?", field), condition.Value)
		case "gt":
			db = db.Where(fmt.Sprintf("%s > ?", field), condition.Value)
		case "lt":
			db = db.Where(fmt.Sprintf("%s < ?", field), condition.Value)
		case "ge":
			db = db.Where(fmt.Sprintf("%s >= ?", field), condition.Value)
		case "le":
			db = db.Where(fmt.Sprintf("%s <= ?", field), condition.Value)
		case "like":
			db = db.Where(fmt.Sprintf("%s LIKE ?", field), condition.Value)
		case "in":
			db = db.Where(fmt.Sprintf("%s IN ?", field), condition.Value)
		case "nin":
			db = db.Where(fmt.Sprintf("%s NOT IN ?", field), condition.Value)
		case "between":
			if values, ok := condition.Value.([]interface{}); ok && len(values) == 2 {
				db = db.Where(fmt.Sprintf("%s BETWEEN ? AND ?", field), values[0], values[1])
			}
		}
	}
	return db
}

package openmatch

// 创建Ticket响应
type TicketResponse struct {
	TicketId string `json:"ticketId"`
}

// Assignment 详情
type Assignment struct {
	Connection string      `json:"connection"`
	Assignment interface{} `json:"assignment"`
}

// Assignment 响应
type AssignmentResponse struct {
	Status     string      `json:"status"`
	Assignment *Assignment `json:"assignment,omitempty"`
}

// WatchAssignments 响应
type WatchAssignmentsResponse struct {
	Assignments []interface{} `json:"assignments"`
}

// 通用错误响应
type ErrorResponse struct {
	Error string `json:"error"`
}

// Ticket参数结构体

type OpenMatchTicket struct {
	TicketId             string                 `json:"ticketId,omitempty"` // Ticket 唯一ID，便于回溯
	Version              string                 `json:"version,omitempty"`  // 结构体版本号，便于调试
	PlayerID             string                 `json:"playerId" binding:"required"`
	Skill                float64                `json:"skill"`
	Role                 string                 `json:"role"`
	JoinTime             int64                  `json:"joinTime"`
	PreferredPlayerCount int                    `json:"preferredPlayerCount"`
	MinPlayerCount       int                    `json:"minPlayerCount"`
	LatencyMap           map[string]int         `json:"latencyMap"`
	Metadata             map[string]interface{} `json:"metadata"`
	Mode                 string                 `json:"mode" binding:"required"`
	Extensions           map[string]interface{} `json:"extensions,omitempty"`
	SearchFields         map[string]interface{} `json:"searchFields,omitempty"`
}

// Backfill 相关结构体

// Backfill 结构体，映射 open-match om.Backfill

type Backfill struct {
	Id           string                 `json:"id,omitempty"`           // Backfill 唯一ID
	SearchFields map[string]interface{} `json:"searchFields,omitempty"` // 匹配搜索字段
	Extensions   map[string]interface{} `json:"extensions,omitempty"`   // 扩展字段
	Assignment   map[string]interface{} `json:"assignment,omitempty"`   // 分配信息
}

// 创建Backfill请求
// 对应 om.CreateBackfillRequest

type CreateBackfillRequest struct {
	Backfill Backfill `json:"backfill"`
}

// Backfill 响应
// 对应 om.Backfill

type BackfillResponse struct {
	Backfill Backfill `json:"backfill"`
}

// 更新Backfill请求
// 对应 om.UpdateBackfillRequest

type UpdateBackfillRequest struct {
	Backfill Backfill `json:"backfill"`
}

// 确认Backfill请求
// 对应 om.AcknowledgeBackfillRequest

type AcknowledgeBackfillRequest struct {
	Assignment map[string]interface{} `json:"assignment"` // Assignment 信息
}

// 确认Backfill响应
// 对应 om.AcknowledgeBackfillResponse

type AcknowledgeBackfillResponse struct {
	Backfill Backfill `json:"backfill"`
}

package openmatch

import (
	"context"
	"fmt"
	"testing"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
)

func init() {
	config.LoadGameFrontendConfig("/root/code/mg/paas-group/ros-group/multiverse/cmd/gamefrontend/config.yaml")
}

func TestTicketToMatchFlow(t *testing.T) {
	ctx := context.Background()
	// 构造 Ticket
	ticket := NewOpenMatchTicket(
		"test-flow-001",   // playerId
		100,               // skill
		"warrior",         // role
		time.Now().Unix(), // joinTime
		4, 2,              // preferredPlayerCount, minPlayerCount
		map[string]int{"us-east": 50},        // latencyMap
		map[string]interface{}{"foo": "bar"}, // metadata
		"battle-royale",                      // mode
		"v1",                                 // version
		nil,                                  // extensions
		nil,                                  // searchFields
	)

	resp, err := GetFrontendClient().CreateTicket(ctx, ticket)
	if err != nil {
		t.Fatalf("CreateTicket failed: %v", err)
	}
	t.Logf("Ticket created: %s", resp.TicketId)

	// 轮询 Assignment，等待匹配
	var assign AssignmentResponse
	for i := 0; i < 10; i++ {
		assign, err = GetFrontendClient().GetAssignment(ctx, resp.TicketId)
		if err != nil {
			t.Fatalf("PollAssignment error: %v", err)
		}
		if assign.Status == "matched" && assign.Assignment != nil {
			break
		}
		fmt.Println("assign.Status", assign.Status)
		time.Sleep(3 * time.Second)
	}

	if assign.Status != "matched" || assign.Assignment == nil {
		t.Fatalf("No match found, status: %s", assign.Status)
	}
	info := assign.Assignment.Assignment.(map[string]interface{})
	ip, _ := info["ip"].(string)
	port, _ := info["port"].(string)
	t.Logf("Matched! GameServer: %s:%s", ip, port)
	fmt.Printf("Matched! GameServer: %s:%s\n", ip, port)
}

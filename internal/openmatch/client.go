package openmatch

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/structpb"
	om "open-match.dev/open-match/pkg/pb"
)

type Client struct {
	frontendClient om.FrontendServiceClient
}

var (
	globalClient *Client
	once         sync.Once
)

// GetFrontendClient returns a singleton instance of the Open Match Frontend Client.
func GetFrontendClient() *Client {
	once.Do(func() {
		frontendConnStr, err := GetOpenMatchFrontendConn()
		if err != nil {
			log.Fatalf("Failed to init Open Match Frontend conn,because of %v", err)
			return
		}
		frontendConn, err := grpc.Dial(frontendConnStr, grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			log.Fatalf("Failed to connect to Open Match frontend: %v", err)
		}
		globalClient = &Client{frontendClient: om.NewFrontendServiceClient(frontendConn)}
	})
	return globalClient
}

// NewClient creates a new Open Match client with the provided gRPC client connection.
// This function is primarily for testing purposes to allow dependency injection.
func NewClient(conn grpc.ClientConnInterface) *Client {
	return &Client{frontendClient: om.NewFrontendServiceClient(conn)}
}

func GetOpenMatchFrontendConn() (string, error) {
	return fmt.Sprintf("%s:%d", config.GameFrontendCfg.OpenMatchFrontendAddr, config.GameFrontendCfg.OpenMatchFrontendPort), nil
}

// NewOpenMatchTicket 构造函数，支持多模式和参数
func NewOpenMatchTicket(
	playerId string, skill float64, role string, joinTime int64,
	preferredPlayerCount, minPlayerCount int, latencyMap map[string]int,
	metadata map[string]interface{}, mode, version string,
	extensions map[string]interface{}, searchFields map[string]interface{},
) OpenMatchTicket {
	return OpenMatchTicket{
		Version:              version,
		PlayerID:             playerId,
		Skill:                skill,
		Role:                 role,
		JoinTime:             joinTime,
		PreferredPlayerCount: preferredPlayerCount,
		MinPlayerCount:       minPlayerCount,
		LatencyMap:           latencyMap,
		Metadata:             metadata,
		Mode:                 mode,
		Extensions:           extensions,
		SearchFields:         searchFields,
	}
}

// CreateTicket 创建Ticket
// CreateTicket 创建Ticket
func (c *Client) CreateTicket(ctx context.Context, req OpenMatchTicket) (TicketResponse, error) {
	log.Printf("[Ticket][v%s] Create: %+v", req.Version, req)

	ext := req.Extensions
	if ext == nil {
		ext = map[string]interface{}{}
	}
	// 补充所有字段到 ext
	ext["playerId"] = req.PlayerID
	ext["skill"] = req.Skill
	ext["role"] = req.Role
	ext["joinTime"] = req.JoinTime
	ext["preferredPlayerCount"] = req.PreferredPlayerCount
	ext["minPlayerCount"] = req.MinPlayerCount
	// 修正 LatencyMap 类型
	latencyMap := map[string]interface{}{}
	for k, v := range req.LatencyMap {
		latencyMap[k] = v
	}
	ext["latencyMap"] = latencyMap
	ext["metadata"] = req.Metadata
	ext["mode"] = req.Mode
	ext["version"] = req.Version

	structExt, err := structpb.NewStruct(ext)
	if err != nil {
		return TicketResponse{}, err
	}
	anyExt, err := anypb.New(structExt)
	if err != nil {
		return TicketResponse{}, err
	}

	// 组装 SearchFields
	searchFields := &om.SearchFields{Tags: []string{req.Mode}}
	if req.SearchFields != nil {
		if tags, ok := req.SearchFields["tags"].([]interface{}); ok {
			for _, t := range tags {
				if s, ok := t.(string); ok {
					searchFields.Tags = append(searchFields.Tags, s)
				}
			}
		}
		if strArgs, ok := req.SearchFields["string_args"].(map[string]interface{}); ok {
			searchFields.StringArgs = map[string]string{}
			for k, v := range strArgs {
				if s, ok := v.(string); ok {
					searchFields.StringArgs[k] = s
				}
			}
		}
		if dblArgs, ok := req.SearchFields["double_args"].(map[string]interface{}); ok {
			searchFields.DoubleArgs = map[string]float64{}
			for k, v := range dblArgs {
				if f, ok := v.(float64); ok {
					searchFields.DoubleArgs[k] = f
				}
			}
		}
	}

	ticket := &om.Ticket{
		SearchFields: searchFields,
		Extensions:   map[string]*anypb.Any{"custom": anyExt},
	}
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	resp, err := c.frontendClient.CreateTicket(ctx, &om.CreateTicketRequest{Ticket: ticket})
	if err != nil {
		log.Printf("[Ticket][v%s] Create error: %v", req.Version, err)
		return TicketResponse{}, err
	}
	return TicketResponse{TicketId: resp.Id}, nil
}

// DeleteTicket 删除Ticket
func (c *Client) DeleteTicket(ctx context.Context, ticketID string) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	_, err := c.frontendClient.DeleteTicket(ctx, &om.DeleteTicketRequest{TicketId: ticketID})
	return err
}

// GetTicket 查询Ticket详情
func (c *Client) GetTicket(ctx context.Context, ticketID string) (*om.Ticket, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	ticket, err := c.frontendClient.GetTicket(ctx, &om.GetTicketRequest{TicketId: ticketID})
	if err != nil {
		return nil, err
	}
	return ticket, nil
}

// parseGameServerConnection 解析 Assignment.Connection
func parseGameServerConnection(conn string) (ip string, port string) {
	parts := strings.Split(conn, ":")
	if len(parts) == 2 {
		return parts[0], parts[1]
	}
	return conn, ""
}

// GetAssignment 查询Ticket的Assignment，解析服务器信息
func (c *Client) GetAssignment(ctx context.Context, ticketID string) (AssignmentResponse, error) {
	ticket, err := c.GetTicket(ctx, ticketID)
	if err != nil {
		log.Printf("[Ticket] GetAssignment error: %v", err)
		return AssignmentResponse{Status: "error"}, err
	}
	assignment := ticket.GetAssignment()
	if assignment == nil || assignment.Connection == "" {
		return AssignmentResponse{Status: "submitted", Assignment: nil}, nil
	}
	ip, port := parseGameServerConnection(assignment.Connection)
	return AssignmentResponse{
		Status: "matched",
		Assignment: &Assignment{
			Connection: assignment.Connection,
			Assignment: map[string]interface{}{
				"ip":   ip,
				"port": port,
				"raw":  assignment,
			},
		},
	}, nil
}

// PollAssignment 长轮询Ticket Assignment，增强错误处理
func (c *Client) PollAssignment(ctx context.Context, ticketID string) (AssignmentResponse, error) {
	maxAttempts := 7
	var assignment *om.Assignment
	var err error
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		ticket, e := c.GetTicket(ctx, ticketID)
		if e != nil {
			err = e
			break
		}
		assignment = ticket.GetAssignment()
		if assignment != nil && assignment.Connection != "" {
			ip, port := parseGameServerConnection(assignment.Connection)
			return AssignmentResponse{
				Status: "matched",
				Assignment: &Assignment{
					Connection: assignment.Connection,
					Assignment: map[string]interface{}{
						"ip":   ip,
						"port": port,
						"raw":  assignment,
					},
				},
			}, nil
		}
		if attempt < maxAttempts {
			time.Sleep(5 * time.Second)
		}
	}
	if err != nil {
		log.Printf("[Ticket] PollAssignment error: %v", err)
		return AssignmentResponse{Status: "error"}, err
	}
	return AssignmentResponse{Status: "timeout", Assignment: nil}, nil
}

// WatchAssignments 流式监听Ticket Assignment，增强错误处理
func (c *Client) WatchAssignments(ctx context.Context, ticketID string) (WatchAssignmentsResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	stream, err := c.frontendClient.WatchAssignments(ctx, &om.WatchAssignmentsRequest{TicketId: ticketID})
	if err != nil {
		log.Printf("[Ticket] WatchAssignments error: %v", err)
		return WatchAssignmentsResponse{}, err
	}
	assignments := []interface{}{}
	for {
		resp, err := stream.Recv()
		if err != nil {
			break
		}
		assignment := resp.GetAssignment()
		if assignment != nil && assignment.Connection != "" {
			ip, port := parseGameServerConnection(assignment.Connection)
			assignments = append(assignments, map[string]interface{}{
				"connection": assignment.Connection,
				"ip":         ip,
				"port":       port,
				"raw":        assignment,
			})
		}
	}
	return WatchAssignmentsResponse{Assignments: assignments}, nil
}

// Backfill 相关方法

// CreateBackfill 创建 Backfill
// 将自定义 CreateBackfillRequest 转换为 om.CreateBackfillRequest，调用 OpenMatch，再将结果转换为 BackfillResponse
func (c *Client) CreateBackfill(ctx context.Context, req CreateBackfillRequest) (BackfillResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 参数转换：自定义 Backfill -> om.Backfill
	omBackfill, err := toOmBackfill(req.Backfill)
	if err != nil {
		return BackfillResponse{}, err
	}
	resp, err := c.frontendClient.CreateBackfill(ctx, &om.CreateBackfillRequest{Backfill: omBackfill})
	if err != nil {
		return BackfillResponse{}, err
	}
	// 响应转换：om.Backfill -> 自定义 Backfill
	return BackfillResponse{Backfill: fromOmBackfill(resp)}, nil
}

// GetBackfill 查询 Backfill
func (c *Client) GetBackfill(ctx context.Context, backfillID string) (BackfillResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	resp, err := c.frontendClient.GetBackfill(ctx, &om.GetBackfillRequest{BackfillId: backfillID})
	if err != nil {
		return BackfillResponse{}, err
	}
	return BackfillResponse{Backfill: fromOmBackfill(resp)}, nil
}

// UpdateBackfill 更新 Backfill
func (c *Client) UpdateBackfill(ctx context.Context, backfillID string, req UpdateBackfillRequest) (BackfillResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	omBackfill, err := toOmBackfill(req.Backfill)
	if err != nil {
		return BackfillResponse{}, err
	}
	omBackfill.Id = backfillID
	resp, err := c.frontendClient.UpdateBackfill(ctx, &om.UpdateBackfillRequest{Backfill: omBackfill})
	if err != nil {
		return BackfillResponse{}, err
	}
	return BackfillResponse{Backfill: fromOmBackfill(resp)}, nil
}

// AcknowledgeBackfill 确认 Backfill
func (c *Client) AcknowledgeBackfill(ctx context.Context, backfillID string, req AcknowledgeBackfillRequest) (AcknowledgeBackfillResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	// Assignment 映射
	exts := map[string]*anypb.Any{}
	for k, v := range req.Assignment {
		if v == nil {
			continue
		}
		st, err := structpb.NewStruct(map[string]interface{}{k: v})
		if err != nil {
			continue
		}
		an, err := anypb.New(st)
		if err != nil {
			continue
		}
		exts[k] = an
	}
	resp, err := c.frontendClient.AcknowledgeBackfill(ctx, &om.AcknowledgeBackfillRequest{
		BackfillId: backfillID,
		Assignment: &om.Assignment{Extensions: exts},
	})
	if err != nil {
		return AcknowledgeBackfillResponse{}, err
	}
	return AcknowledgeBackfillResponse{Backfill: fromOmBackfill(resp.GetBackfill())}, nil
}

// toOmBackfill: 自定义 Backfill -> om.Backfill
func toOmBackfill(b Backfill) (*om.Backfill, error) {
	// SearchFields 映射
	var searchFields *om.SearchFields
	if b.SearchFields != nil {
		searchFields = &om.SearchFields{}
		if tags, ok := b.SearchFields["tags"].([]interface{}); ok {
			for _, t := range tags {
				if s, ok := t.(string); ok {
					searchFields.Tags = append(searchFields.Tags, s)
				}
			}
		}
		if strArgs, ok := b.SearchFields["string_args"].(map[string]interface{}); ok {
			searchFields.StringArgs = map[string]string{}
			for k, v := range strArgs {
				if s, ok := v.(string); ok {
					searchFields.StringArgs[k] = s
				}
			}
		}
		if dblArgs, ok := b.SearchFields["double_args"].(map[string]interface{}); ok {
			searchFields.DoubleArgs = map[string]float64{}
			for k, v := range dblArgs {
				if f, ok := v.(float64); ok {
					searchFields.DoubleArgs[k] = f
				}
			}
		}
	}
	// Extensions 映射
	exts := map[string]*anypb.Any{}
	for k, v := range b.Extensions {
		if v == nil {
			continue
		}
		st, err := structpb.NewStruct(map[string]interface{}{k: v})
		if err != nil {
			continue
		}
		an, err := anypb.New(st)
		if err != nil {
			continue
		}
		exts[k] = an
	}
	persist := map[string]*anypb.Any{}
	return &om.Backfill{
		Id:              b.Id,
		SearchFields:    searchFields,
		Extensions:      exts,
		PersistentField: persist,
		// Generation, CreateTime 可按需补充
	}, nil
}

// fromOmBackfill: om.Backfill -> 自定义 Backfill
func fromOmBackfill(ob *om.Backfill) Backfill {
	if ob == nil {
		return Backfill{}
	}
	searchFields := map[string]interface{}{}
	if ob.SearchFields != nil {
		if len(ob.SearchFields.Tags) > 0 {
			searchFields["tags"] = ob.SearchFields.Tags
		}
		if len(ob.SearchFields.StringArgs) > 0 {
			searchFields["string_args"] = ob.SearchFields.StringArgs
		}
		if len(ob.SearchFields.DoubleArgs) > 0 {
			searchFields["double_args"] = ob.SearchFields.DoubleArgs
		}
	}
	extensions := map[string]interface{}{}
	for k, v := range ob.Extensions {
		if v == nil {
			continue
		}
		var m map[string]interface{}
		if st, err := v.UnmarshalNew(); err == nil {
			if s, ok := st.(*structpb.Struct); ok {
				m = s.AsMap()
			}
		}
		if m != nil {
			extensions[k] = m[k]
		}
	}
	// PersistentField 可按需补充
	return Backfill{
		Id:           ob.Id,
		SearchFields: searchFields,
		Extensions:   extensions,
	}
}

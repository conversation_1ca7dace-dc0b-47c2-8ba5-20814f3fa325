package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// WatchAssignments godoc
// @Summary 流式监听Ticket Assignment
// @Description 流式监听指定Ticket的Assignment（简单实现为多次拉取）
// @Tags Ticket
// @Param ticket_id path string true "TicketID"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /ticket/{ticket_id}/watch-assignments [get]
func WatchAssignments(c *gin.Context) {
	ticketID := c.Param("ticket_id")
	resp, err := openmatch.GetFrontendClient().WatchAssignments(c.Request.Context(), ticketID)
	if err != nil {
		c.JSON(500, openmatch.ErrorResponse{Error: err.Error()})
		return
	}
	c.<PERSON><PERSON><PERSON>(200, resp)
}

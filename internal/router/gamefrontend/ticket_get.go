package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
	_ "open-match.dev/open-match/pkg/pb"
)

// GetTicket godoc
// @Summary 查询Ticket详情
// @Description 查询指定ID的Ticket详情
// @Tags Ticket
// @Param ticket_id path string true "TicketID"
// @Success 200 {object} pb.Ticket
// @Failure 500 {object} map[string]string
// @Router /ticket/{ticket_id} [get]
func GetTicket(c *gin.Context) {
	ticketID := c.Param("ticket_id")
	ticket, err := openmatch.GetFrontendClient().GetTicket(c.Request.Context(), ticketID)
	if err != nil {
		c.JSON(500, openmatch.ErrorResponse{Error: err.Error()})
		return
	}
	c.JSO<PERSON>(200, ticket)
}

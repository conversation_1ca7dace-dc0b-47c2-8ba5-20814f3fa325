package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// DeleteBackfill godoc
// @Summary 删除Backfill
// @Description 删除指定ID的Backfill
// @Tags Backfill
// @Param backfill_id path string true "BackfillID"
// @Success 200 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /backfill/{backfill_id} [delete]
func DeleteBackfill(c *gin.Context) {
	backfillID := c.Param("backfill_id")
	err := openmatch.GetFrontendClient().DeleteTicket(c.Request.Context(), backfillID)
	if err != nil {
		c.JSON(500, openmatch.ErrorResponse{Error: err.Error()})
		return
	}
	c.JSON(200, gin.H{"msg": "deleted"})
}

package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// DeleteTicket godoc
// @Summary 删除Ticket
// @Description 删除指定ID的Ticket
// @Tags Ticket
// @Param ticket_id path string true "TicketID"
// @Success 200 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /ticket/{ticket_id} [delete]
func DeleteTicket(c *gin.Context) {
	ticketID := c.Param("ticket_id")
	err := openmatch.GetFrontendClient().DeleteTicket(c.Request.Context(), ticketID)
	if err != nil {
		c.JSON(500, openmatch.ErrorResponse{Error: "删除失败: " + err.Error()})
		return
	}
	c.JSON(200, gin.H{"msg": "deleted"})
}

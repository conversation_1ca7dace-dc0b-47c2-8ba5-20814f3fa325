package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// GetAssignment godoc
// @Summary 查询Ticket的Assignment
// @Description 查询指定Ticket的Assignment信息
// @Tags Ticket
// @Param ticket_id path string true "TicketID"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /ticket/{ticket_id}/assignment [get]
func GetAssignment(c *gin.Context) {
	ticketID := c.Param("ticket_id")
	resp, err := openmatch.GetFrontendClient().GetAssignment(c.Request.Context(), ticketID)
	if err != nil {
		c.JSON(500, openmatch.ErrorResponse{Error: "查询失败: " + err.Error()})
		return
	}
	c.JSO<PERSON>(200, resp)
}

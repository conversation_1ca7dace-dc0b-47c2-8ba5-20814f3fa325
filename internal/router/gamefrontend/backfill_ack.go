package gamefrontend

import (
	om "git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// AcknowledgeBackfill godoc
// @Summary 确认Backfill
// @Description 确认指定ID的Backfill（通知OpenMatch分配GameServer）
// @Tags Backfill
// @Accept json
// @Produce json
// @Param backfill_id path string true "BackfillID"
// @Param ack body om.AcknowledgeBackfillRequest true "确认参数"

// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /backfill/{backfill_id}/ack [post]
func AcknowledgeBackfill(c *gin.Context) {
	backfillID := c.Param("backfill_id")
	var req om.AcknowledgeBackfillRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, om.ErrorResponse{Error: err.Error()})
		return
	}
	resp, err := om.GetFrontendClient().AcknowledgeBackfill(c.Request.Context(), backfillID, req)
	if err != nil {
		c.JSON(500, om.ErrorResponse{Error: err.Error()})
		return
	}
	c.JSON(200, resp)
}

package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// CreateBackfill godoc
// @Summary 创建Backfill
// @Description 创建一个新的Backfill对象
// @Tags Backfill
// @Accept json
// @Produce json
// @Param backfill body openmatch.CreateBackfillRequest true "Backfill参数"

// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /backfill [post]
func CreateBackfill(c *gin.Context) {
	var req openmatch.CreateBackfillRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, openmatch.ErrorResponse{Error: err.Error()})
		return
	}
	resp, err := openmatch.GetFrontendClient().CreateBackfill(c.Request.Context(), req)
	if err != nil {
		c.<PERSON>(500, openmatch.ErrorResponse{Error: err.Error()})
		return
	}
	c.<PERSON>(200, resp)
}

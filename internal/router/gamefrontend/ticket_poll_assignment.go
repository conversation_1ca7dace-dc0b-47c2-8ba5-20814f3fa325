package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// PollAssignment godoc
// @Summary 长轮询Ticket Assignment
// @Description 轮询指定Ticket的Assignment，最多30秒
// @Tags Ticket
// @Param ticket_id path string true "TicketID"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /ticket/{ticket_id}/poll-assignment [get]
func PollAssignment(c *gin.Context) {
	ticketID := c.Param("ticket_id")
	resp, err := openmatch.GetFrontendClient().PollAssignment(c.Request.Context(), ticketID)
	if err != nil {
		c.JSON(500, openmatch.ErrorResponse{Error: err.Error()})
		return
	}
	c.JSON(200, resp)
}

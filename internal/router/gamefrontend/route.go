package gamefrontend

import (
	"github.com/gin-gonic/gin"
)

func RegisterTicketRoutes(r *gin.Engine) {
	br := r.Group("/")
	ticket := br.Group("/ticket")

	ticket.POST("", CreateTicket)
	ticket.DELETE(":ticket_id", DeleteTicket)
	ticket.GET(":ticket_id", GetTicket)
	ticket.GET(":ticket_id/assignment", GetAssignment)
	ticket.GET(":ticket_id/poll-assignment", PollAssignment)
	ticket.GET(":ticket_id/watch-assignments", WatchAssignments)

	backfill := br.Group("/backfill")
	backfill.POST("", CreateBackfill)
	backfill.DELETE(":backfill_id", DeleteBackfill)
	backfill.GET(":backfill_id", GetBackfill)
	backfill.PUT(":backfill_id", UpdateBackfill)
	backfill.POST(":backfill_id/ack", AcknowledgeBackfill)
}

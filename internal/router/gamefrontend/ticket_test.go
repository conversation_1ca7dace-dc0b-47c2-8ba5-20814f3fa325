package gamefrontend

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
)

const baseURL = "http://localhost:8081"

func TestIntegration_CreateTicket(t *testing.T) {
	// 统一 Ticket 构造
	body := openmatch.OpenMatchTicket{
		PlayerID:             "test-player-intg",
		Skill:                100,
		Role:                 "warrior",
		JoinTime:             time.Now().Unix(),
		PreferredPlayerCount: 4,
		MinPlayerCount:       2,
		LatencyMap:           map[string]int{"us-east": 50},
		Metadata:             map[string]interface{}{"foo": "bar"},
		Mode:                 "battle-royale",
	}
	jsonBody, _ := json.Marshal(body)
	fmt.Println("[CreateTicket] 请求参数:", string(jsonBody))
	resp, err := http.Post(baseURL+"/ticket", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[CreateTicket] 请求失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	fmt.Printf("[CreateTicket] 响应状态码: %d\n", resp.StatusCode)
	fmt.Println("[CreateTicket] 响应内容:", result)
}

func TestIntegration_CreateTicketInvalidParam(t *testing.T) {
	body := openmatch.OpenMatchTicket{
		PlayerID:             "test-player-intg",
		Skill:                100,
		Role:                 "warrior",
		JoinTime:             time.Now().Unix(),
		PreferredPlayerCount: 4,
		MinPlayerCount:       2,
		LatencyMap:           map[string]int{"us-east": 50},
		Metadata:             map[string]interface{}{"foo": "bar"},
		Mode:                 "battle-royale",
	}
	jsonBody, _ := json.Marshal(body)
	fmt.Println("[CreateTicketInvalidParam] 请求参数:", string(jsonBody))
	resp, err := http.Post(baseURL+"/ticket", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[CreateTicketInvalidParam] 请求失败:", err)
		return
	}
	defer resp.Body.Close()
	bodyBytes, _ := io.ReadAll(resp.Body)
	fmt.Printf("[CreateTicketInvalidParam] 响应状态码: %d\n", resp.StatusCode)
	fmt.Println("[CreateTicketInvalidParam] 响应内容:", string(bodyBytes))
}

func TestIntegration_DeleteTicket(t *testing.T) {
	// 先创建一个 ticket
	body := openmatch.OpenMatchTicket{
		PlayerID:             "test-player-del",
		Skill:                100,
		Role:                 "warrior",
		JoinTime:             time.Now().Unix(),
		PreferredPlayerCount: 4,
		MinPlayerCount:       2,
		LatencyMap:           map[string]int{"us-east": 50},
		Metadata:             map[string]interface{}{"foo": "bar"},
		Mode:                 "battle-royale",
	}
	jsonBody, _ := json.Marshal(body)
	resp, err := http.Post(baseURL+"/ticket", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[DeleteTicket] 创建ticket失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	ticketId, ok := result["ticketId"].(string)
	if !ok {
		fmt.Println("[DeleteTicket] ticketId 获取失败")
		return
	}

	// 删除
	req, _ := http.NewRequest("DELETE", baseURL+"/ticket/"+ticketId, nil)
	w := &http.Client{}
	resp2, err := w.Do(req)
	if err != nil {
		fmt.Println("[DeleteTicket] 删除请求失败:", err)
		return
	}
	defer resp2.Body.Close()
	bodyBytes, _ := io.ReadAll(resp2.Body)
	fmt.Printf("[DeleteTicket] 响应状态码: %d\n", resp2.StatusCode)
	fmt.Println("[DeleteTicket] 响应内容:", string(bodyBytes))
}

func TestIntegration_GetTicket(t *testing.T) {
	// 先创建一个 ticket
	body := openmatch.OpenMatchTicket{
		PlayerID:             "test-player-get",
		Skill:                100,
		Role:                 "warrior",
		JoinTime:             time.Now().Unix(),
		PreferredPlayerCount: 4,
		MinPlayerCount:       2,
		LatencyMap:           map[string]int{"us-east": 50},
		Metadata:             map[string]interface{}{"foo": "bar"},
		Mode:                 "battle-royale",
	}
	jsonBody, _ := json.Marshal(body)
	resp, err := http.Post(baseURL+"/ticket", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[GetTicket] 创建ticket失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	ticketId, ok := result["ticketId"].(string)
	if !ok {
		fmt.Println("[GetTicket] ticketId 获取失败")
		return
	}

	// 查询 ticket
	resp2, err := http.Get(baseURL + "/ticket/" + ticketId)
	if err != nil {
		fmt.Println("[GetTicket] ticket请求失败:", err)
		return
	}
	defer resp2.Body.Close()
	bodyBytes, _ := io.ReadAll(resp2.Body)
	fmt.Printf("[GetTicket] 响应状态码: %d\n", resp2.StatusCode)
	fmt.Println("[GetTicket] 响应内容:", string(bodyBytes))
}

func TestIntegration_GetAssignment(t *testing.T) {
	// 先创建一个 ticket
	body := openmatch.OpenMatchTicket{
		PlayerID:             "test-player-assign",
		Skill:                100,
		Role:                 "warrior",
		JoinTime:             time.Now().Unix(),
		PreferredPlayerCount: 4,
		MinPlayerCount:       2,
		LatencyMap:           map[string]int{"us-east": 50},
		Metadata:             map[string]interface{}{"foo": "bar"},
		Mode:                 "battle-royale",
	}
	jsonBody, _ := json.Marshal(body)
	resp, err := http.Post(baseURL+"/ticket", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[GetAssignment] 创建ticket失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	ticketId, ok := result["ticketId"].(string)
	if !ok {
		fmt.Println("[GetAssignment] ticketId 获取失败")
		return
	}

	// 查询 assignment
	resp2, err := http.Get(baseURL + "/ticket/" + ticketId + "/assignment")
	if err != nil {
		fmt.Println("[GetAssignment] assignment请求失败:", err)
		return
	}
	defer resp2.Body.Close()
	bodyBytes, _ := io.ReadAll(resp2.Body)
	fmt.Printf("[GetAssignment] 响应状态码: %d\n", resp2.StatusCode)
	fmt.Println("[GetAssignment] 响应内容:", string(bodyBytes))
}

func TestIntegration_PollAssignment(t *testing.T) {
	// 先创建一个 ticket
	body := openmatch.OpenMatchTicket{
		PlayerID:             "test-player-poll",
		Skill:                100,
		Role:                 "warrior",
		JoinTime:             time.Now().Unix(),
		PreferredPlayerCount: 4,
		MinPlayerCount:       2,
		LatencyMap:           map[string]int{"us-east": 50},
		Metadata:             map[string]interface{}{"foo": "bar"},
		Mode:                 "battle-royale",
	}
	jsonBody, _ := json.Marshal(body)
	resp, err := http.Post(baseURL+"/ticket", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[PollAssignment] 创建ticket失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	ticketId, ok := result["ticketId"].(string)
	if !ok {
		fmt.Println("[PollAssignment] ticketId 获取失败")
		return
	}

	// 轮询 assignment
	resp2, err := http.Get(baseURL + "/ticket/" + ticketId + "/poll-assignment")
	if err != nil {
		fmt.Println("[PollAssignment] poll-assignment请求失败:", err)
		return
	}
	defer resp2.Body.Close()
	bodyBytes, _ := io.ReadAll(resp2.Body)
	fmt.Printf("[PollAssignment] 响应状态码: %d\n", resp2.StatusCode)
	fmt.Println("[PollAssignment] 响应内容:", string(bodyBytes))
}

func TestIntegration_WatchAssignments(t *testing.T) {
	// 先创建一个 ticket
	body := openmatch.OpenMatchTicket{
		PlayerID:             "test-player-watch",
		Skill:                100,
		Role:                 "warrior",
		JoinTime:             time.Now().Unix(),
		PreferredPlayerCount: 4,
		MinPlayerCount:       2,
		LatencyMap:           map[string]int{"us-east": 50},
		Metadata:             map[string]interface{}{"foo": "bar"},
		Mode:                 "battle-royale",
	}
	jsonBody, _ := json.Marshal(body)
	resp, err := http.Post(baseURL+"/ticket", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[WatchAssignments] 创建ticket失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	ticketId, ok := result["ticketId"].(string)
	if !ok {
		fmt.Println("[WatchAssignments] ticketId 获取失败")
		return
	}

	// watch assignments
	resp2, err := http.Get(baseURL + "/ticket/" + ticketId + "/watch-assignments")
	if err != nil {
		fmt.Println("[WatchAssignments] watch-assignments请求失败:", err)
		return
	}
	defer resp2.Body.Close()
	bodyBytes, _ := io.ReadAll(resp2.Body)
	fmt.Printf("[WatchAssignments] 响应状态码: %d\n", resp2.StatusCode)
	fmt.Println("[WatchAssignments] 响应内容:", string(bodyBytes))
}

func TestIntegration_CreateBackfill(t *testing.T) {
	body := map[string]interface{}{
		"backfill": map[string]interface{}{
			"search_fields": map[string]interface{}{
				"tags": []string{"battle-royale"},
			},
		},
	}
	jsonBody, _ := json.Marshal(body)
	fmt.Println("[CreateBackfill] 请求参数:", string(jsonBody))
	resp, err := http.Post(baseURL+"/backfill", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[CreateBackfill] 请求失败:", err)
		return
	}
	defer resp.Body.Close()
	bodyBytes, _ := io.ReadAll(resp.Body)
	fmt.Printf("[CreateBackfill] 响应状态码: %d\n", resp.StatusCode)
	fmt.Println("[CreateBackfill] 响应内容:", string(bodyBytes))
}

func TestIntegration_DeleteBackfill(t *testing.T) {
	// 先创建一个 backfill
	body := map[string]interface{}{
		"backfill": map[string]interface{}{
			"search_fields": map[string]interface{}{
				"tags": []string{"battle-royale"},
			},
		},
	}
	jsonBody, _ := json.Marshal(body)
	resp, err := http.Post(baseURL+"/backfill", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[DeleteBackfill] 创建backfill失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	backfillId, ok := result["id"].(string)
	if !ok {
		fmt.Println("[DeleteBackfill] backfillId 获取失败")
		return
	}

	// 删除
	req, _ := http.NewRequest("DELETE", baseURL+"/backfill/"+backfillId, nil)
	w := &http.Client{}
	resp2, err := w.Do(req)
	if err != nil {
		fmt.Println("[DeleteBackfill] 删除请求失败:", err)
		return
	}
	defer resp2.Body.Close()
	bodyBytes, _ := io.ReadAll(resp2.Body)
	fmt.Printf("[DeleteBackfill] 响应状态码: %d\n", resp2.StatusCode)
	fmt.Println("[DeleteBackfill] 响应内容:", string(bodyBytes))
}

func TestIntegration_GetBackfill(t *testing.T) {
	// 先创建一个 backfill
	body := map[string]interface{}{
		"backfill": map[string]interface{}{
			"search_fields": map[string]interface{}{
				"tags": []string{"battle-royale"},
			},
		},
	}
	jsonBody, _ := json.Marshal(body)
	resp, err := http.Post(baseURL+"/backfill", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[GetBackfill] 创建backfill失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	backfillId, ok := result["id"].(string)
	if !ok {
		fmt.Println("[GetBackfill] backfillId 获取失败")
		return
	}

	// 查询 backfill
	resp2, err := http.Get(baseURL + "/backfill/" + backfillId)
	if err != nil {
		fmt.Println("[GetBackfill] backfill请求失败:", err)
		return
	}
	defer resp2.Body.Close()
	bodyBytes, _ := io.ReadAll(resp2.Body)
	fmt.Printf("[GetBackfill] 响应状态码: %d\n", resp2.StatusCode)
	fmt.Println("[GetBackfill] 响应内容:", string(bodyBytes))
}

func TestIntegration_UpdateBackfill(t *testing.T) {
	// 先创建一个 backfill
	body := map[string]interface{}{
		"backfill": map[string]interface{}{
			"search_fields": map[string]interface{}{
				"tags": []string{"battle-royale"},
			},
		},
	}
	jsonBody, _ := json.Marshal(body)
	resp, err := http.Post(baseURL+"/backfill", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[UpdateBackfill] 创建backfill失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	backfillId, ok := result["id"].(string)
	if !ok {
		fmt.Println("[UpdateBackfill] backfillId 获取失败")
		return
	}

	// 更新 backfill
	updateBody := map[string]interface{}{
		"backfill": map[string]interface{}{
			"id": backfillId,
			"search_fields": map[string]interface{}{
				"tags": []string{"battle-royale-updated"},
			},
		},
	}
	updateJson, _ := json.Marshal(updateBody)
	req, _ := http.NewRequest("PUT", baseURL+"/backfill/"+backfillId, bytes.NewBuffer(updateJson))
	req.Header.Set("Content-Type", "application/json")
	w := &http.Client{}
	resp2, err := w.Do(req)
	if err != nil {
		fmt.Println("[UpdateBackfill] 更新请求失败:", err)
		return
	}
	defer resp2.Body.Close()
	bodyBytes, _ := io.ReadAll(resp2.Body)
	fmt.Printf("[UpdateBackfill] 响应状态码: %d\n", resp2.StatusCode)
	fmt.Println("[UpdateBackfill] 响应内容:", string(bodyBytes))
}

func TestIntegration_AcknowledgeBackfill(t *testing.T) {
	// 先创建一个 backfill
	body := map[string]interface{}{
		"backfill": map[string]interface{}{
			"search_fields": map[string]interface{}{
				"tags": []string{"battle-royale"},
			},
		},
	}
	jsonBody, _ := json.Marshal(body)
	resp, err := http.Post(baseURL+"/backfill", "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Println("[AcknowledgeBackfill] 创建backfill失败:", err)
		return
	}
	defer resp.Body.Close()
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	backfillId, ok := result["id"].(string)
	if !ok {
		fmt.Println("[AcknowledgeBackfill] backfillId 获取失败")
		return
	}

	// acknowledge
	ackBody := map[string]interface{}{
		"assignment": map[string]interface{}{
			"ack": true,
		},
	}
	ackJson, _ := json.Marshal(ackBody)
	resp2, err := http.Post(baseURL+"/backfill/"+backfillId+"/ack", "application/json", bytes.NewBuffer(ackJson))
	if err != nil {
		fmt.Println("[AcknowledgeBackfill] ack请求失败:", err)
		return
	}
	defer resp2.Body.Close()
	bodyBytes, _ := io.ReadAll(resp2.Body)
	fmt.Printf("[AcknowledgeBackfill] 响应状态码: %d\n", resp2.StatusCode)
	fmt.Println("[AcknowledgeBackfill] 响应内容:", string(bodyBytes))
}

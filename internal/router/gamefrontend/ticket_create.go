package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// CreateTicket godoc
// @Summary 创建匹配Ticket
// @Description 提交一个新的匹配Ticket
// @Tags Ticket
// @Accept json
// @Produce json

// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /ticket [post]
func CreateTicket(c *gin.Context) {
	var req openmatch.OpenMatchTicket
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "参数错误: " + err.Error()})
		return
	}
	// 移除对 req.WantsToBeMonster 的校验
	resp, err := openmatch.GetFrontendClient().CreateTicket(c.Request.Context(), req)
	if err != nil {
		c.<PERSON>(500, gin.H{"error": "Open Match 提交失败: " + err.Error()})
		return
	}
	c.JSO<PERSON>(200, resp)
}

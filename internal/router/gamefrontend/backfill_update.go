package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// UpdateBackfill godoc
// @Summary 更新Backfill
// @Description 更新指定ID的Backfill
// @Tags Backfill
// @Accept json
// @Produce json
// @Param backfill_id path string true "BackfillID"
// @Param backfill body openmatch.UpdateBackfillRequest true "Backfill参数"

// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /backfill/{backfill_id} [put]
func UpdateBackfill(c *gin.Context) {
	backfillID := c.Param("backfill_id")
	var req openmatch.UpdateBackfillRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, openmatch.ErrorResponse{Error: err.Error()})
		return
	}
	resp, err := openmatch.GetFrontendClient().UpdateBackfill(c.Request.Context(), backfillID, req)
	if err != nil {
		c.JSON(500, openmatch.ErrorResponse{Error: err.Error()})
		return
	}
	c.JSON(200, resp)
}

package gamefrontend

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/openmatch"
	"github.com/gin-gonic/gin"
)

// GetBackfill godoc
// @Summary 查询Backfill详情
// @Description 查询指定ID的Backfill详情
// @Tags Backfill
// @Param backfill_id path string true "BackfillID"
// @Success 200 {object} openmatch.Backfill
// @Failure 500 {object} map[string]string
// @Router /backfill/{backfill_id} [get]
func GetBackfill(c *gin.Context) {
	backfillID := c.Param("backfill_id")
	resp, err := openmatch.GetFrontendClient().GetBackfill(c.Request.Context(), backfillID)
	if err != nil {
		c.JSON(500, openmatch.ErrorResponse{Error: err.Error()})
		return
	}
	c.JSON(200, resp)
}

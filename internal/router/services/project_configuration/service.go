package project_configuration

import (
	"errors"

	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/logger"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/context"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ProjectConfigurationService interface {
	GetProjectConfiguration(c *gin.Context, projectId int64) (*model.ProjectConfiguration, error)
	UpdateProjectConfiguration(c *gin.Context, projectId int64, param parameter.ServiceProjectConfiguration) (*model.ProjectConfiguration, error)
}

type projectConfigurationService struct {
	db *gorm.DB
}

func NewProjectConfigurationService(db *gorm.DB) ProjectConfigurationService {
	return &projectConfigurationService{db: db}
}

func getDefaultProjectConfiguration(projectId int64, tenantID int64) *model.ProjectConfiguration {
	return &model.ProjectConfiguration{
		ProjectID:     projectId,
		TenantID:      tenantID,
		ServerTimeout: 300,
		ServerOS:      "linux",
		CallbackURL:   "",
	}
}

func (s *projectConfigurationService) GetProjectConfiguration(c *gin.Context, projectId int64) (*model.ProjectConfiguration, error) {
	tenantID, _ := context.GetTenantID(c)
	projectConfiguration := &model.ProjectConfiguration{}
	if err := s.db.Where("project_id = ? AND tenant_id = ?", projectId, tenantID).First(projectConfiguration).Error; err != nil {
		// 如果不存在创建一个默认配置
		if errors.Is(err, gorm.ErrRecordNotFound) {
			projectConfiguration = getDefaultProjectConfiguration(projectId, tenantID)
			if err := s.db.Create(&projectConfiguration).Error; err != nil {
				logger.Error("GetProjectConfiguration Create", zap.Error(err))
				return nil, err
			}
			return projectConfiguration, nil
		}
		logger.Error("GetProjectConfiguration First", zap.Error(err))
		return nil, err
	}
	return projectConfiguration, nil
}

func (s *projectConfigurationService) UpdateProjectConfiguration(c *gin.Context, projectId int64, param parameter.ServiceProjectConfiguration) (*model.ProjectConfiguration, error) {
	tenantID, _ := context.GetTenantID(c)
	projectConfiguration := &model.ProjectConfiguration{}
	if err := s.db.Where("project_id = ? AND tenant_id = ?", projectId, tenantID).First(projectConfiguration).Error; err != nil {
		return nil, err
	}
	projectConfiguration.ServerTimeout = param.ServerTimeout
	projectConfiguration.ServerOS = param.ServerOS
	projectConfiguration.CallbackURL = param.CallbackURL
	if err := s.db.Save(&projectConfiguration).Error; err != nil {
		return nil, err
	}
	return projectConfiguration, nil
}

package project_configuration

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/database"
	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册路由（使用默认数据库）
func RegisterRoutes(r *gin.RouterGroup) {
	service := NewProjectConfigurationService(database.DB)
	handler := NewProjectConfigurationHandler(service)
	registerRoutersWhithHandler(r, handler)
}

// 注册路由（支持依赖注入） 可用于测试
func registerWhithService(r *gin.RouterGroup, handler *ProjectConfigurationHandler) {
	registerRoutersWhithHandler(r, handler)
}

func registerRoutersWhithHandler(r *gin.RouterGroup, handler *ProjectConfigurationHandler) {
	projectConfiguration := r.Group("/project-configuration")
	{
		projectConfiguration.GET("/:projectId", handler.GetProjectConfiguration)
		projectConfiguration.PUT("/:projectId", handler.UpdateProjectConfiguration)
	}
}

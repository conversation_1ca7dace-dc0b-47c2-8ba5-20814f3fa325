package project_configuration

import (
	"strconv"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/response"
	"github.com/gin-gonic/gin"
)

type ProjectConfigurationHandler struct {
	service ProjectConfigurationService
}

func NewProjectConfigurationHandler(service ProjectConfigurationService) *ProjectConfigurationHandler {
	return &ProjectConfigurationHandler{service: service}
}

// GetProjectConfiguration 获取项目设置
// @Summary 获取项目设置
// @Description 获取项目设置
// @Tags 项目设置
// @Accept json
// @Produce json
// @Param projectId path int true "项目ID"
// @Success 200 {object} response.CommonResponse{data=parameter.ServiceProjectConfigurationResponse} "成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/project-configuration/{projectId} [get]
func (h *ProjectConfigurationHandler) GetProjectConfiguration(c *gin.Context) {
	projectId, err := strconv.ParseInt(c.Param("projectId"), 10, 64)
	if err != nil {
		response.HandleErrorWithAudit(c,
			"GetProjectConfiguration",
			"ProjectConfiguration",
			err,
			"请求参数错误",
			response.CodeInvalidParameter,
		)
		return
	}
	projCfg, err := h.service.GetProjectConfiguration(c, projectId)
	if err != nil {
		response.HandleErrorWithAudit(c,
			"GetProjectConfiguration",
			"ProjectConfiguration",
			err,
			"获取项目设置失败",
			response.CodeFail,
		)
		return
	}
	responseData := (*parameter.ServiceProjectConfigurationResponse)(projCfg)
	response.ResponseWithAudit(c,
		"GetProjectConfiguration",
		"ProjectConfiguration",
		"获取项目设置成功",
		true,
		response.CodeSuccess,
		responseData,
	)
}

// UpdateProjectConfiguration 更新项目设置
// @Summary 更新项目设置
// @Description 更新项目设置
// @Tags 项目设置
// @Accept json
// @Produce json
// @Param projectId path int true "项目ID"
// @Param param body parameter.ServiceProjectConfiguration true "项目设置"
// @Success 200 {object} response.CommonResponse{data=parameter.ServiceProjectConfigurationResponse} "成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/project-configuration/{projectId} [put]
func (h *ProjectConfigurationHandler) UpdateProjectConfiguration(c *gin.Context) {
	var param parameter.ServiceProjectConfiguration
	if err := c.ShouldBindJSON(&param); err != nil {
		response.HandleErrorWithAudit(c,
			"UpdateProjectConfiguration",
			"ProjectConfiguration",
			err,
			"请求参数错误",
			response.CodeInvalidParameter,
		)
		return
	}
	projectId, err := strconv.ParseInt(c.Param("projectId"), 10, 64)
	if err != nil {
		response.HandleErrorWithAudit(c,
			"UpdateProjectConfiguration",
			"ProjectConfiguration",
			err,
			"请求参数错误",
			response.CodeInvalidParameter,
		)
		return
	}
	projCfg, err := h.service.UpdateProjectConfiguration(c, projectId, param)
	if err != nil {
		response.HandleErrorWithAudit(c,
			"UpdateProjectConfiguration",
			"ProjectConfiguration",
			err,
			"更新项目设置失败",
			response.CodeFail,
		)
		return
	}
	responseData := (*parameter.ServiceProjectConfigurationResponse)(projCfg)
	response.ResponseWithAudit(c,
		"UpdateProjectConfiguration",
		"ProjectConfiguration",
		"更新项目设置成功",
		true,
		response.CodeSuccess,
		responseData,
	)
}

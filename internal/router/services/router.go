package services

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/router/services/allocation"
	imagebuild "git.mg.xyz/paas-group/ros-group/multiverse/internal/router/services/image_build"
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/router/services/project_configuration"
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/router/services/startup_configuration"
	"github.com/gin-gonic/gin"
)

func RegisterRoutes(r *gin.RouterGroup) {
	services := r.Group("/services")
	{
		project_configuration.RegisterRoutes(services)
		startup_configuration.RegisterRoutes(services)
		imagebuild.RegisterRoutes(services)
		allocation.RegisterRoutes(services)
	}
}

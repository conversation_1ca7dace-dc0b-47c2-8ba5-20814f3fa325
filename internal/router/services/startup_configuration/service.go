package startup_configuration

import (
	"errors"
	"fmt"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/router/services/project_configuration"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/logger"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/context"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/utils/uuid"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type StartupConfigurationService interface {
	GetStartupConfiguration(c *gin.Context, id int64) (*model.StartupConfiguration, error)
	CreateStartupConfiguration(c *gin.Context, param parameter.ServiceStartupConfigurationCreateReq) (*model.StartupConfiguration, error)
	UpdateStartupConfiguration(c *gin.Context, id int64, param parameter.ServiceStartupConfigurationUpdateReq) (*model.StartupConfiguration, error)
	DeleteStartupConfiguration(c *gin.Context, id int64) error
	ListStartupConfigurations(c *gin.Context, projectId int64, tenantId int64) ([]*model.StartupConfiguration, error)
}

type startupConfigurationService struct {
	db *gorm.DB
}

func NewStartupConfigurationService(db *gorm.DB) StartupConfigurationService {
	return &startupConfigurationService{db: db}
}

func (s *startupConfigurationService) GetStartupConfiguration(c *gin.Context, id int64) (*model.StartupConfiguration, error) {
	tenantID, _ := context.GetTenantID(c)
	startupConfig := &model.StartupConfiguration{}
	if err := s.db.Where("id = ? AND tenant_id = ?", id, tenantID).First(startupConfig).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("启动配置不存在")
		}
		logger.Error("GetStartupConfiguration", zap.Error(err))
		return nil, err
	}
	return startupConfig, nil
}

// 获取项目配置
func (s *startupConfigurationService) GetProjectConfiguration(c *gin.Context, projectId int64, tenantId int64) (*model.ProjectConfiguration, error) {
	projectConfig, err := project_configuration.NewProjectConfigurationService(s.db).GetProjectConfiguration(c, projectId)
	if err != nil {
		return nil, err
	}
	return projectConfig, nil
}

func (s *startupConfigurationService) CreateStartupConfiguration(c *gin.Context, param parameter.ServiceStartupConfigurationCreateReq) (*model.StartupConfiguration, error) {
	projectId, _ := context.GetProjectID(c)
	tenantID, _ := context.GetTenantID(c)
	// 获取项目配置
	projectConfig, err := s.GetProjectConfiguration(c, projectId, tenantID)
	if err != nil {
		return nil, err
	}
	startupConfig := &model.StartupConfiguration{
		ProjectID:              projectId,
		TenantID:               tenantID,
		ProjectConfigurationID: projectConfig.ID,
		UUID:                   uuid.Generate(),
		Name:                   param.Name,
	}

	// TODO 镜像构建没做完这里提供 假数据
	imageConfig := &model.ImageConfig{
		StartupID:       startupConfig.ID,
		StartupConfigCT: time.Now().Unix(),
		Image:           "nginx:latest",
		ImageTag:        fmt.Sprintf("%s-%s", startupConfig.Name, uuid.Generate()),
		CPU:             0.2,
		Ports:           []*model.Port{{Port: 80, Protocol: "TCP", Name: "http"}},
	}
	if err := s.db.Create(imageConfig).Error; err != nil {
		logger.Error("CreateStartupConfiguration", zap.Error(err))
		return nil, err
	}

	startupConfig.ImageConfigID = imageConfig.ID
	startupConfig.ImageTag = imageConfig.ImageTag
	startupConfig.ImageConfigCT = time.Now().Unix()

	if err := s.db.Create(startupConfig).Error; err != nil {
		logger.Error("CreateStartupConfiguration", zap.Error(err))
		return nil, err
	}
	return startupConfig, nil
}

func (s *startupConfigurationService) UpdateStartupConfiguration(c *gin.Context, id int64, param parameter.ServiceStartupConfigurationUpdateReq) (*model.StartupConfiguration, error) {
	tenantID, _ := context.GetTenantID(c)
	startupConfig := &model.StartupConfiguration{}
	if err := s.db.Where("id = ? AND tenant_id = ?", id, tenantID).First(startupConfig).Error; err != nil {
		return nil, err
	}
	startupConfig.ImageConfigID = param.ImageConfigID
	// TODO 获取镜像配置信息
	if err := s.db.Save(startupConfig).Error; err != nil {
		return nil, err
	}
	return startupConfig, nil
}

func (s *startupConfigurationService) DeleteStartupConfiguration(c *gin.Context, id int64) error {
	tenantID, _ := context.GetTenantID(c)
	if err := s.db.Where("id = ? AND tenant_id = ?", id, tenantID).Delete(&model.StartupConfiguration{}).Error; err != nil {
		logger.Error("DeleteStartupConfiguration", zap.Error(err))
		return err
	}
	return nil
}

func (s *startupConfigurationService) ListStartupConfigurations(c *gin.Context, projectId int64, tenantId int64) ([]*model.StartupConfiguration, error) {
	var startupConfigs []*model.StartupConfiguration
	if err := s.db.Where("project_id = ? AND tenant_id = ?", projectId, tenantId).Find(&startupConfigs).Error; err != nil {
		logger.Error("ListStartupConfigurations", zap.Error(err))
		return nil, err
	}
	return startupConfigs, nil
}

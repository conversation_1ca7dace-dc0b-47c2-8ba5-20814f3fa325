package startup_configuration

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/database"
	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册路由（使用默认数据库）
func RegisterRoutes(r *gin.RouterGroup) {
	service := NewStartupConfigurationService(database.DB)
	handler := NewStartupConfigurationHandler(service)
	registerRoutersWithHandler(r, handler)
}

// 注册路由（支持依赖注入） 可用于测试
func registerWithService(r *gin.RouterGroup, handler *StartupConfigurationHandler) {
	registerRoutersWithHandler(r, handler)
}

func registerRoutersWithHandler(r *gin.RouterGroup, handler *StartupConfigurationHandler) {
	startupConfiguration := r.Group("/startup-configurations")
	{
		startupConfiguration.GET("", handler.ListStartupConfigurations)
		startupConfiguration.POST("", handler.CreateStartupConfiguration)
		startupConfiguration.GET("/:id", handler.GetStartupConfiguration)
		startupConfiguration.PUT("/:id", handler.UpdateStartupConfiguration)
		startupConfiguration.DELETE("/:id", handler.DeleteStartupConfiguration)
	}
}

package startup_configuration

import (
	"strconv"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/context"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/response"
	"github.com/gin-gonic/gin"
)

type StartupConfigurationHandler struct {
	service StartupConfigurationService
}

func NewStartupConfigurationHandler(service StartupConfigurationService) *StartupConfigurationHandler {
	return &StartupConfigurationHandler{service: service}
}

// GetStartupConfiguration 获取启动配置
// @Summary 获取启动配置
// @Description 获取启动配置
// @Tags 启动配置
// @Accept json
// @Produce json
// @Param id path int true "启动配置ID"
// @Success 200 {object} response.CommonResponse{data=parameter.ServiceStartupConfigurationResponse} "成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/startup-configurations/{id} [get]
func (h *StartupConfigurationHandler) GetStartupConfiguration(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.HandleErrorWithAudit(c, "GetStartupConfiguration", "StartupConfiguration", err, "请求参数错误", response.CodeInvalidParameter)
		return
	}
	startupConfig, err := h.service.GetStartupConfiguration(c, id)
	if err != nil {
		response.HandleErrorWithAudit(c, "GetStartupConfiguration", "StartupConfiguration", err, "获取启动配置失败", response.CodeFail)
		return
	}
	responseData := (*parameter.ServiceStartupConfigurationResponse)(startupConfig)
	response.ResponseWithAudit(c, "GetStartupConfiguration", "StartupConfiguration", "获取启动配置成功", true, response.CodeSuccess, responseData)
}

// CreateStartupConfiguration 创建启动配置
// @Summary 创建启动配置
// @Description 创建启动配置
// @Tags 启动配置
// @Accept json
// @Produce json
// @Param param body parameter.ServiceStartupConfigurationCreateReq true "启动配置"
// @Success 200 {object} response.CommonResponse{data=parameter.ServiceStartupConfigurationResponse} "成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/startup-configurations [post]
func (h *StartupConfigurationHandler) CreateStartupConfiguration(c *gin.Context) {
	var param parameter.ServiceStartupConfigurationCreateReq
	if err := c.ShouldBindJSON(&param); err != nil {
		response.HandleErrorWithAudit(c, "CreateStartupConfiguration", "StartupConfiguration", err, "请求参数错误", response.CodeInvalidParameter)
		return
	}
	startupConfig, err := h.service.CreateStartupConfiguration(c, param)
	if err != nil {
		response.HandleErrorWithAudit(c, "CreateStartupConfiguration", "StartupConfiguration", err, "创建启动配置失败", response.CodeFail)
		return
	}
	responseData := (*parameter.ServiceStartupConfigurationResponse)(startupConfig)
	response.ResponseWithAudit(c, "CreateStartupConfiguration", "StartupConfiguration", "创建启动配置成功", true, response.CodeSuccess, responseData)
}

// UpdateStartupConfiguration 更新启动配置
// @Summary 更新启动配置
// @Description 更新启动配置
// @Tags 启动配置
// @Accept json
// @Produce json
// @Param id path int true "启动配置ID"
// @Param param body parameter.ServiceStartupConfigurationCreateReq true "启动配置"
// @Success 200 {object} response.CommonResponse{data=parameter.ServiceStartupConfigurationResponse} "成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/startup-configurations/{id} [put]
func (h *StartupConfigurationHandler) UpdateStartupConfiguration(c *gin.Context) {
	var param parameter.ServiceStartupConfigurationUpdateReq
	if err := c.ShouldBindJSON(&param); err != nil {
		response.HandleErrorWithAudit(c, "UpdateStartupConfiguration", "StartupConfiguration", err, "请求参数错误", response.CodeInvalidParameter)
		return
	}
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.HandleErrorWithAudit(c, "UpdateStartupConfiguration", "StartupConfiguration", err, "请求参数错误", response.CodeInvalidParameter)
		return
	}
	startupConfig, err := h.service.UpdateStartupConfiguration(c, id, param)
	if err != nil {
		response.HandleErrorWithAudit(c, "UpdateStartupConfiguration", "StartupConfiguration", err, "更新启动配置失败", response.CodeFail)
		return
	}
	responseData := (*parameter.ServiceStartupConfigurationResponse)(startupConfig)
	response.ResponseWithAudit(c, "UpdateStartupConfiguration", "StartupConfiguration", "更新启动配置成功", true, response.CodeSuccess, responseData)
}

// DeleteStartupConfiguration 删除启动配置
// @Summary 删除启动配置
// @Description 删除启动配置
// @Tags 启动配置
// @Accept json
// @Produce json
// @Param id path int true "启动配置ID"
// @Success 200 {object} response.CommonResponse "成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/startup-configurations/{id} [delete]
func (h *StartupConfigurationHandler) DeleteStartupConfiguration(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.HandleErrorWithAudit(c, "DeleteStartupConfiguration", "StartupConfiguration", err, "请求参数错误", response.CodeInvalidParameter)
		return
	}
	err = h.service.DeleteStartupConfiguration(c, id)
	if err != nil {
		response.HandleErrorWithAudit(c, "DeleteStartupConfiguration", "StartupConfiguration", err, "删除启动配置失败", response.CodeFail)
		return
	}
	response.ResponseWithAudit(c, "DeleteStartupConfiguration", "StartupConfiguration", "删除启动配置成功", true, response.CodeSuccess, nil)
}

// ListStartupConfigurations 获取启动配置列表
// @Summary 获取启动配置列表
// @Description 获取启动配置列表
// @Tags 启动配置
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=[]parameter.ServiceStartupConfigurationResponse} "成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/startup-configurations [get]
func (h *StartupConfigurationHandler) ListStartupConfigurations(c *gin.Context) {
	projectId, _ := context.GetProjectID(c)
	tenantId, _ := context.GetTenantID(c)
	startupConfigs, err := h.service.ListStartupConfigurations(c, projectId, tenantId)
	if err != nil {
		response.HandleErrorWithAudit(c, "ListStartupConfigurations", "StartupConfiguration", err, "获取启动配置列表失败", response.CodeFail)
		return
	}
	response.ResponseWithAudit(c, "ListStartupConfigurations", "StartupConfiguration", "获取启动配置列表成功", true, response.CodeSuccess, startupConfigs)
}

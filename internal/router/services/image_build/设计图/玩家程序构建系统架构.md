# 玩家程序构建系统架构

> 需要安装插件 Markdown Preview Merm

```mermaid
sequenceDiagram
    participant Player as 玩家
    participant UI as Web界面
    participant API as API服务
    participant ZipHandler as ZIP处理器
    participant Security as 安全检查
    participant DockerGen as Dockerfile生成器
    participant <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>构建器
    participant Registry as 镜像仓库
    
    Player->>UI: 1. 选择基础镜像
    Player->>UI: 2. 上传ZIP包
    Player->>UI: 3. 指定入口程序
    UI->>API: 4. 提交构建请求
    
    API->>ZipHandler: 5. 处理ZIP包
    ZipHandler->>ZipHandler: 6. 解压ZIP文件
    ZipHandler-->>API: 7. 解压完成
    
    API->>Security: 8. 安全检查
    Security-->>API: 9. 检查结果
    
    alt 安全检查通过
        API->>DockerGen: 10. 生成Dockerfile
        DockerGen->>DockerGen: 11. 创建简单Dockerfile
        DockerGen-->>API: 12. Dockerfile生成完成
        
        API->>Kaniko: 13. 启动构建任务
        Kaniko->>Kaniko: 14. 构建镜像
        Kaniko->>Registry: 15. 推送镜像
        Registry-->>UI: 16. 构建完成通知
        UI-->>Player: 17. 显示镜像信息
    else 安全检查失败
        API-->>UI: 拒绝构建
        UI-->>Player: 显示错误信息
    end
```
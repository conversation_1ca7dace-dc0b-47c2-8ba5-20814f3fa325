# 构建流程图

> 需要安装插件 Markdown Preview Merm

```mermaid
sequenceDiagram
    participant Player as 玩家
    participant UI as Web界面
    participant API as API服务
    participant Security as 安全检查
    participant Storage as 文件存储
    participant Queue as 构建队列
    participant Ka<PERSON><PERSON> as Ka<PERSON>o构建器
    participant Registry as 镜像仓库
    
    Player->>UI: 1. 选择运行时环境
    Player->>UI: 2. 上传程序文件
    UI->>API: 3. 提交构建请求
    
    API->>Security: 4. 安全检查
    Security-->>API: 4.1 检查结果
    
    alt 安全检查通过
        API->>Storage: 5. 存储文件
        Storage-->>API: 5.1 存储成功
        
        API->>Queue: 6. 加入构建队列
        Queue-->>UI: 6.1 返回构建ID
        
        Queue->>Kaniko: 7. 触发构建任务
        Kaniko->>Storage: 8. 获取源码
        Kaniko->>Kaniko: 9. 生成Dockerfile
        Kaniko->>Kaniko: 10. 执行构建
        Kaniko->>Registry: 11. 推送镜像
        
        Registry-->>UI: 12. 构建完成通知
        UI-->>Player: 13. 显示构建结果
    else 安全检查失败
        API-->>UI: 拒绝构建
        UI-->>Player: 显示错误信息
    end
```
package imagebuild

import (
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/response"
	"github.com/gin-gonic/gin"
)

type ImageBuildHandler struct {
	service ImageBuildService
}

func NewImageBuildHandler(service ImageBuildService) *ImageBuildHandler {
	return &ImageBuildHandler{
		service: service,
	}
}

// GetRuntimes 获取支持的运行时环境
// @Summary 获取支持的运行时环境
// @Description 获取支持的运行时环境列表
// @Tags 镜像构建
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=parameter.ImageBuildRuntimesResponse} "成功"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/image-build/runtimes [get]
func (h *ImageBuildHandler) GetRuntimes(c *gin.Context) {
	runtimes, err := h.service.GetRuntimes(c)
	if err != nil {
		response.HandleErrorWithAudit(c,
			"GetRuntimes",
			"ImageBuild",
			err,
			"获取运行时环境失败",
			http.StatusInternalServerError)
		return
	}

	response.ResponseWithAudit(c,
		"GetRuntimes",
		"ImageBuild",
		"获取运行时环境成功",
		true,
		response.CodeSuccess,
		parameter.ImageBuildRuntimesResponse{
			BaseImages: runtimes,
		},
	)
}

// HandleUpload 处理文件上传
// @Summary 上传项目文件并构建镜像
// @Description 上传ZIP文件并启动镜像构建
// @Tags 镜像构建
// @Accept multipart/form-data
// @Produce json
// @Param baseImage formData string true "基础镜像"
// @Param projectName formData string true "项目名称"
// @Param description formData string false "项目描述"
// @Param entryPoint formData string true "入口程序"
// @Param zipFile formData file true "ZIP文件"
// @Success 200 {object} response.CommonResponse{data=parameter.ImageBuildUploadResponse} "成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/image-build/upload [post]
func (h *ImageBuildHandler) HandleUpload(c *gin.Context) {
	var req parameter.ImageBuildUploadRequest

	// 解析表单数据
	req.BaseImage = c.PostForm("baseImage")
	req.ProjectName = c.PostForm("projectName")
	req.Description = c.PostForm("description")
	req.EntryPoint = c.PostForm("entryPoint")

	// 验证必填字段
	if req.BaseImage == "" || req.ProjectName == "" || req.EntryPoint == "" {
		response.HandleErrorWithAudit(c,
			"HandleUpload",
			"ImageBuild",
			nil,
			"缺少必填参数",
			http.StatusBadRequest)
		return
	}

	// 获取上传的ZIP文件
	file, err := c.FormFile("zipFile")
	if err != nil {
		response.HandleErrorWithAudit(c,
			"HandleUpload",
			"ImageBuild",
			err,
			"请选择要上传的ZIP文件",
			http.StatusBadRequest)
		return
	}

	result, err := h.service.HandleUpload(c, req, file)
	if err != nil {
		response.HandleErrorWithAudit(c,
			"HandleUpload",
			"ImageBuild",
			err,
			"文件上传失败",
			http.StatusInternalServerError)
		return
	}
	response.ResponseWithAudit(c,
		"HandleUpload",
		"ImageBuild",
		"文件上传成功",
		true,
		response.CodeSuccess,
		result,
	)
}

// GetBuildStatus 获取构建状态
// @Summary 获取构建状态
// @Description 根据构建ID获取构建状态
// @Tags 镜像构建
// @Accept json
// @Produce json
// @Param buildId path string true "构建ID"
// @Success 200 {object} response.CommonResponse{data=parameter.ImageBuildStatusResponse} "成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 404 {object} response.CommonResponse{error=string} "构建任务不存在"
// @Router /api/v1/ros/multiverse/services/image-build/build/{buildId} [get]
func (h *ImageBuildHandler) GetBuildStatus(c *gin.Context) {
	buildID := c.Param("buildId")
	if buildID == "" {
		response.HandleErrorWithAudit(c,
			"GetBuildStatus",
			"ImageBuild",
			nil,
			"构建ID不能为空",
			http.StatusBadRequest)
		return
	}

	status, err := h.service.GetBuildStatus(c, buildID)
	if err != nil {
		response.HandleErrorWithAudit(c,
			"GetBuildStatus",
			"ImageBuild",
			err,
			"获取构建状态失败",
			http.StatusNotFound)
		return
	}

	response.ResponseWithAudit(c,
		"GetBuildStatus",
		"ImageBuild",
		"获取构建状态成功",
		true,
		response.CodeSuccess,
		parameter.ImageBuildStatusResponse{
			Build: status,
		},
	)
}

// GetAllBuilds 获取所有构建任务
// @Summary 获取所有构建任务
// @Description 获取所有构建任务列表
// @Tags 镜像构建
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=parameter.ImageBuildListResponse} "成功"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/image-build/builds [get]
func (h *ImageBuildHandler) GetAllBuilds(c *gin.Context) {
	builds, err := h.service.GetAllBuilds(c)
	if err != nil {
		response.HandleErrorWithAudit(c,
			"GetAllBuilds",
			"ImageBuild",
			err,
			"获取构建列表失败",
			http.StatusInternalServerError)
		return
	}

	response.ResponseWithAudit(c,
		"GetAllBuilds",
		"ImageBuild",
		"获取构建列表成功",
		true,
		response.CodeSuccess,
		parameter.ImageBuildListResponse{
			Builds: builds,
		},
	)
}

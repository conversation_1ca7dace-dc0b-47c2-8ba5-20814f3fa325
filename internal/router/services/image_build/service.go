package imagebuild

import (
	"fmt"
	"log"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	ib "git.mg.xyz/paas-group/ros-group/multiverse/pkg/imagebuild"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type ImageBuildService interface {
	GetRuntimes(c *gin.Context) (map[string]*model.BaseImageConfig, error)
	HandleUpload(c *gin.Context, req parameter.ImageBuildUploadRequest, file *multipart.FileHeader) (*parameter.ImageBuildUploadResponse, error)
	GetBuildStatus(c *gin.Context, buildID string) (*ib.BuildStatus, error)
	GetAllBuilds(c *gin.Context) ([]*ib.BuildStatus, error)
}

type imageBuildService struct {
	kanikoBuilder *ib.KanikoBuilder
	zipHandler    *ib.ZipHandler
}

func NewImageBuildService() ImageBuildService {
	// 初始化ZIP处理器
	zipHandler := ib.NewZipHandler()

	// 尝试初始化Kaniko构建器（可选）
	kanikoBuilder, _ := ib.NewKanikoBuilder("default")

	return &imageBuildService{
		kanikoBuilder: kanikoBuilder,
		zipHandler:    zipHandler,
	}
}

// 支持的基础镜像 // TODO 从数据库读取
var SupportedBaseImages = map[string]*model.BaseImageConfig{
	"dotnet-runtime-7.0": {
		Name:        ".NET Runtime 7.0",
		Image:       "mcr.microsoft.com/dotnet/runtime:7.0",
		Description: "Microsoft .NET Runtime 7.0",
		Category:    "runtime",
		Icon:        "🟣",
	},
	"ubuntu-20.04": {
		Name:        "Ubuntu 20.04",
		Image:       "ubuntu:20.04",
		Description: "Ubuntu 20.04 LTS",
		Category:    "os",
		Icon:        "🟠",
	},
	"dotnet-runtime-7.0-alpine": {
		Name:        ".NET Runtime 7.0 Alpine",
		Image:       "mcr.microsoft.com/dotnet/runtime:7.0-alpine",
		Description: "Microsoft .NET Runtime 7.0 on Alpine Linux",
		Category:    "runtime",
		Icon:        "🟣",
	},
	"dotnet-runtime-6.0": {
		Name:        ".NET Runtime 6.0",
		Image:       "mcr.microsoft.com/dotnet/runtime:6.0",
		Description: "Microsoft .NET Runtime 6.0",
		Category:    "runtime",
		Icon:        "🟣",
	},
	"dotnet-runtime-6.0-alpine": {
		Name:        ".NET Runtime 6.0 Alpine",
		Image:       "mcr.microsoft.com/dotnet/runtime:6.0-alpine",
		Description: "Microsoft .NET Runtime 6.0 on Alpine Linux",
		Category:    "runtime",
		Icon:        "🟣",
	},
	"dotnet-runtime-8.0": {
		Name:        ".NET Runtime 8.0",
		Image:       "mcr.microsoft.com/dotnet/runtime:8.0",
		Description: "Microsoft .NET Runtime 8.0",
		Category:    "runtime",
		Icon:        "🟣",
	},
	"dotnet-runtime-8.0-alpine": {
		Name:        ".NET Runtime 8.0 Alpine",
		Image:       "mcr.microsoft.com/dotnet/runtime:8.0-alpine",
		Description: "Microsoft .NET Runtime 8.0 on Alpine Linux",
		Category:    "runtime",
		Icon:        "🟣",
	},
	"jre-11": {
		Name:        "Java Runtime Environment 11",
		Image:       "openjdk:11-jre-slim",
		Description: "OpenJDK Java Runtime Environment 11",
		Category:    "runtime",
		Icon:        "☕",
	},
	"jre-17": {
		Name:        "Java Runtime Environment 17",
		Image:       "openjdk:17-jre-slim",
		Description: "OpenJDK Java Runtime Environment 17",
		Category:    "runtime",
		Icon:        "☕",
	},
	"python-3.9": {
		Name:        "Python 3.9",
		Image:       "python:3.9-slim",
		Description: "Python 3.9 Runtime",
		Category:    "runtime",
		Icon:        "🐍",
	},
	"python-3.11": {
		Name:        "Python 3.11",
		Image:       "python:3.11-slim",
		Description: "Python 3.11 Runtime",
		Category:    "runtime",
		Icon:        "🐍",
	},
	"node-16": {
		Name:        "Node.js 16",
		Image:       "node:16-alpine",
		Description: "Node.js 16 Runtime",
		Category:    "runtime",
		Icon:        "🟢",
	},
	"node-18": {
		Name:        "Node.js 18",
		Image:       "node:18-alpine",
		Description: "Node.js 18 Runtime",
		Category:    "runtime",
		Icon:        "🟢",
	},
	"alpine-3.18": {
		Name:        "Alpine Linux 3.18",
		Image:       "alpine:3.18",
		Description: "Alpine Linux 3.18",
		Category:    "os",
		Icon:        "🏔️",
	},
}

func (s *imageBuildService) GetRuntimes(c *gin.Context) (map[string]*model.BaseImageConfig, error) {
	return SupportedBaseImages, nil
}

func (s *imageBuildService) HandleUpload(c *gin.Context, req parameter.ImageBuildUploadRequest, file *multipart.FileHeader) (*parameter.ImageBuildUploadResponse, error) {
	// 验证基础镜像
	_, exists := SupportedBaseImages[req.BaseImage]
	if !exists {
		return nil, fmt.Errorf("不支持的基础镜像")
	}

	// 验证文件类型
	if !strings.HasSuffix(strings.ToLower(file.Filename), ".zip") {
		return nil, fmt.Errorf("只支持ZIP文件格式")
	}

	// 检查文件大小（限制100MB）
	if file.Size > 100*1024*1024 {
		return nil, fmt.Errorf("ZIP文件太大，最大支持100MB")
	}

	// 创建项目目录
	projectID := uuid.New().String()
	projectDir := filepath.Join("./uploads", projectID)
	os.MkdirAll(projectDir, 0755)

	// 保存ZIP文件
	zipFilePath := filepath.Join(projectDir, file.Filename)
	if err := c.SaveUploadedFile(file, zipFilePath); err != nil {
		os.RemoveAll(projectDir)
		return nil, fmt.Errorf("保存ZIP文件失败: %v", err)
	}

	// 解压ZIP文件
	extractDir := filepath.Join(projectDir, "extracted")
	zipResult, err := s.zipHandler.ProcessZipFile(zipFilePath, extractDir)
	if err != nil {
		os.RemoveAll(projectDir)
		return nil, fmt.Errorf("处理ZIP文件失败: %v", err)
	}

	// 查找可执行文件
	executables, err := s.zipHandler.FindExecutableFiles(extractDir)
	if err != nil {
		log.Printf("查找可执行文件失败: %v", err)
	}

	// 创建构建任务
	buildID := uuid.New().String()
	buildStatus := &ib.BuildStatus{
		ID:         buildID,
		ProjectID:  projectID,
		Status:     "pending",
		BaseImage:  req.BaseImage,
		EntryPoint: req.EntryPoint,
		CreatedAt:  time.Now(),
		Logs: []string{
			fmt.Sprintf("ZIP文件处理成功: %s", s.zipHandler.GetSummary(zipResult)),
			fmt.Sprintf("找到 %d 个可执行文件", len(executables)),
		},
	}
	ib.BuildStatuses[buildID] = buildStatus

	// 异步启动构建任务
	go s.startBuildTask(buildID, projectID, req.BaseImage, req.ProjectName, req.EntryPoint, "")

	return &parameter.ImageBuildUploadResponse{
		Success:   true,
		Message:   "文件上传成功，开始构建",
		ProjectID: projectID,
		BuildID:   buildID,
	}, nil
}

func (s *imageBuildService) GetBuildStatus(c *gin.Context, buildID string) (*ib.BuildStatus, error) {
	status, exists := ib.BuildStatuses[buildID]
	if !exists {
		return nil, fmt.Errorf("构建任务不存在")
	}
	return status, nil
}

func (s *imageBuildService) GetAllBuilds(c *gin.Context) ([]*ib.BuildStatus, error) {
	var builds []*ib.BuildStatus
	for _, build := range ib.BuildStatuses {
		builds = append(builds, build)
	}
	return builds, nil
}

// 启动构建任务
func (s *imageBuildService) startBuildTask(buildID, projectID, baseImage, projectName, entryPoint, workingDir string) {
	status := ib.BuildStatuses[buildID]
	status.Status = "building"
	status.Logs = append(status.Logs, "开始构建镜像...")

	// 如果Kaniko构建器可用，使用真实构建
	if s.kanikoBuilder != nil {
		err := s.kanikoBuilder.BuildImage(buildID, projectID, baseImage, projectName, entryPoint, workingDir)
		if err != nil {
			log.Printf("Kaniko构建失败: %v", err)
			// 回退到模拟构建
			s.simulateBuild(buildID, projectName)
		}
		return
	}

	// 模拟构建
	s.simulateBuild(buildID, projectName)
}

// 模拟构建过程
func (s *imageBuildService) simulateBuild(buildID, projectName string) {
	status := ib.BuildStatuses[buildID]

	// 模拟构建过程
	time.Sleep(2 * time.Second)
	status.Logs = append(status.Logs, "生成Dockerfile...")

	time.Sleep(3 * time.Second)
	status.Logs = append(status.Logs, "执行Kaniko构建...")

	time.Sleep(5 * time.Second)
	status.Logs = append(status.Logs, "推送镜像到仓库...")

	// 模拟构建成功
	time.Sleep(2 * time.Second)
	now := time.Now()
	status.Status = "success"
	status.CompletedAt = &now
	status.ImageName = fmt.Sprintf("player-apps/%s:%s", projectName, buildID[:8])
	status.Logs = append(status.Logs, fmt.Sprintf("构建成功！镜像: %s", status.ImageName))
}

package imagebuild

import (
	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册路由（使用默认数据库）
func RegisterRoutes(r *gin.RouterGroup) {
	service := NewImageBuildService()
	handler := NewImageBuildHandler(service)
	registerRoutersWithHandler(r, handler)
}

func registerRoutersWithHandler(r *gin.RouterGroup, handler *ImageBuildHandler) {
	buildGroup := r.Group("/image-build")
	{
		buildGroup.GET("/runtimes", handler.GetRuntimes)
		buildGroup.POST("/upload", handler.HandleUpload)
		buildGroup.GET("/build/:buildId", handler.GetBuildStatus)
		buildGroup.GET("/builds", handler.GetAllBuilds)

		// 测试页面路由
		buildGroup.GET("/test", func(c *gin.Context) {
			c.HTML(200, "index.html", gin.H{
				"title": "镜像构建测试页面",
			})
		})
	}
}

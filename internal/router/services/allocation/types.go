package allocation

import "git.mg.xyz/paas-group/ros-group/multiverse/internal/model"

// 请求分配服务器
type GssAllocationRequest struct {
	StartupConfigurationID int64             `json:"startup_configuration_id" binding:"required"` // 启动配置ID
	Region                 string            `json:"region" binding:"required"`                   // 区域
	AllocationTTL          int64             `json:"allocation_ttl" binding:"required,min=300"`   // 最长存活时间
	Env                    map[string]string `json:"env" binding:"required"`                      // 环境变量
}

// 响应分配服务器数据
type GssAllocationRespData model.ServicesAllocation

package allocation

import (
	"errors"
	"fmt"

	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/context"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/k8smgr"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/serverallocation"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/utils/uuid"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AllocationService 分配服务接口
type AllocationService interface {
	// 分配游戏服务器
	AllocateGameServer(c *gin.Context, req *GssAllocationRequest) (*model.ServicesAllocation, error)

	// // 列出游戏服务器
	// ListGameServers(c *gin.Context, limit int, offset int) ([]*model.ServicesAllocation, error)

	// // 释放游戏服务器
	// DeallocateGameServer(c *gin.Context, gameServerID string) error
}

// allocationService 分配服务实现
type allocationService struct {
	db        *gorm.DB
	k8ssvc    *serverallocation.K8sService
	namespace string
}

// NewAllocationService 创建分配服务
func NewAllocationService(db *gorm.DB) AllocationService {
	// 默认命名空间 // TODO 确定
	namespace := "game-servers"

	return &allocationService{
		db:        db,
		k8ssvc:    serverallocation.K8sServiceSingleton(),
		namespace: namespace,
	}
}

// getProjectConfiguration 获取项目配置
func (s *allocationService) getProjectConfiguration(projectID, tenantID int64) (*model.ProjectConfiguration, error) {
	var projectConfig model.ProjectConfiguration
	err := s.db.Where("id = ? AND tenant_id = ?", projectID, tenantID).First(&projectConfig).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New("项目配置不存在")
	}
	if err != nil {
		return nil, err
	}
	return &projectConfig, nil
}

// AllocateGameServer 分配游戏服务器
func (s *allocationService) AllocateGameServer(c *gin.Context, req *GssAllocationRequest) (*model.ServicesAllocation, error) {
	// 获取租户ID
	projectID, _ := context.GetProjectID(c)
	tenantID, _ := context.GetTenantID(c)

	// 获取项目配置
	projectConfig, err := s.getProjectConfiguration(projectID, tenantID)
	if err != nil {
		return nil, err
	}

	// 查询启动配置
	startupConfig, err := s.getStartupConfiguration(req.StartupConfigurationID, projectID, tenantID)
	if err != nil {
		return nil, err
	}

	// 查询镜像配置
	imageConfig, err := s.getImageConfig(startupConfig.ImageConfigID)
	if err != nil {
		return nil, err
	}

	// TODO 检查是否有可用的游戏服务器

	// 保存分配信息到数据库
	allocation := &model.ServicesAllocation{
		UUID:                     uuid.Generate(),
		ProjectConfigurationID:   projectConfig.ID,
		StartupConfigurationID:   startupConfig.ID,
		StartupConfigurationName: startupConfig.Name,
		Region:                   req.Region,
		AllocationTTL:            req.AllocationTTL,
		// Env:                      req.Env,
		Status: string(k8smgr.GameServerStateNotReady),
		Msg:    "分配中",
	}
	err = s.db.Create(allocation).Error
	if err != nil {
		return nil, err
	}

	// 转换端口配置
	var ports []k8smgr.GameServerPort
	for _, port := range imageConfig.Ports {
		ports = append(ports, k8smgr.GameServerPort{
			Name:     port.Name,
			Port:     int32(port.Port),
			Protocol: port.Protocol,
		})
	}

	gssCreateReq := &k8smgr.GameServerSetCreateRequest{
		Name:      fmt.Sprintf("%s-%s", startupConfig.Name, allocation.UUID),
		Namespace: serverallocation.GetGSSNamespace(),
		Replicas:  1,
		Image:     imageConfig.Image,
		Ports:     ports,
		Env:       req.Env,
		Resources: k8smgr.ResourceRequirements{
			Requests: k8smgr.ResourceList{
				CPU:    fmt.Sprintf("%f", imageConfig.CPU),
				Memory: fmt.Sprintf("%dMi", serverallocation.CalcMemoryForCPU(imageConfig.CPU)),
			},
		},
		Labels:      map[string]string{"uuid": fmt.Sprintf("%v", allocation.UUID)},
		Annotations: map[string]string{"uuid": fmt.Sprintf("%v", allocation.UUID)},
	}

	// 自动分配游戏服务器
	gssInfo, err := s.k8ssvc.AutoAllocateGSS(c, req.Region, gssCreateReq)
	if err != nil {
		fmt.Printf("Failed to allocate GameServerSet: %v\n", err)
		return nil, err
	}
	fmt.Println("GameServerSet allocated successfully:", gssInfo)

	return allocation, nil
}

// ListGameServers 列出游戏服务器
func (s *allocationService) ListGameServers(c *gin.Context, page int, limit int) ([]*model.ServicesAllocation, error) {
	// projectId, _ := context.GetProjectID(c)
	// tenantId, _ := context.GetTenantID(c)
	// if s.gameServerManager == nil {
	// 	return s.getMockGameServerList(fmt.Sprintf("%d-%d", projectId, tenantId))
	// }

	// gameServers, err := s.gameServerManager.ListGameServers(fmt.Sprintf("%d-%d", projectId, tenantId))
	// if err != nil {
	// 	return &allocation.GameServerListResponse{
	// 		Success: false,
	// 		Message: fmt.Sprintf("获取游戏服务器列表失败: %v", err),
	// 	}, err
	// }

	// return &allocation.GameServerListResponse{
	// 	Success:     true,
	// 	Message:     "获取游戏服务器列表成功",
	// 	GameServers: gameServers,
	// 	Total:       len(gameServers),
	// }, nil
	return nil, nil
}

// DeallocateGameServer 释放游戏服务器
func (s *allocationService) DeallocateGameServer(c *gin.Context, gameServerID string) error {
	// if s.gameServerManager == nil {
	// 	log.Printf("模拟释放游戏服务器: %s", gameServerID)
	// 	return nil
	// }

	// return s.gameServerManager.DeleteGameServer(gameServerID)

	return nil
}

// getImageConfig 获取镜像配置
func (s *allocationService) getImageConfig(id int64) (*model.ImageConfig, error) {
	var imageConfig model.ImageConfig
	err := s.db.Where("id = ?", id).First(&imageConfig).Error
	if err != nil {
		return nil, err
	}
	return &imageConfig, nil
}

// getStartupConfiguration 获取启动配置
func (s *allocationService) getStartupConfiguration(id, projectId, tenantID int64) (*model.StartupConfiguration, error) {
	var startupConfig model.StartupConfiguration
	err := s.db.Where("id = ? AND project_id = ? AND tenant_id = ?", id, projectId, tenantID).First(&startupConfig).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("启动配置不存在")
		}
		return nil, err
	}
	return &startupConfig, nil
}

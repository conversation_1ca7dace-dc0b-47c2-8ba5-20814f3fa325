package allocation

import (
	"net/http"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/response"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/validators"
	"github.com/gin-gonic/gin"
)

// AllocationHandler 分配处理器
type AllocationHandler struct {
	service AllocationService
}

// NewAllocationHandler 创建分配处理器
func NewAllocationHandler(service AllocationService) *AllocationHandler {
	return &AllocationHandler{
		service: service,
	}
}

// AllocateGameServer 分配游戏服务器
// @Summary 分配游戏服务器
// @Description 根据启动配置分配一个游戏服务器实例
// @Tags 游戏服务器分配
// @Accept json
// @Produce json
// @Param request body GssAllocationRequest true "分配请求"
// @Success 200 {object} response.CommonResponse{data=GssAllocationRespData} "分配成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/allocation/gameservers [post]
func (h *AllocationHandler) AllocateGameServer(c *gin.Context) {
	var req GssAllocationRequest
	if err := validators.ShouldBindJSONWrap(c, &req); err != nil {
		response.HandleErrorWithAudit(c,
			"AllocateGameServer",
			"GameServerAllocation",
			err,
			"请求参数错误",
			http.StatusBadRequest)
		return
	}

	result, err := h.service.AllocateGameServer(c, &req)
	if err != nil {
		response.HandleErrorWithAudit(c,
			"AllocateGameServer",
			"GameServerAllocation",
			err,
			"分配游戏服务器失败",
			http.StatusInternalServerError)
		return
	}

	response.ResponseWithAudit(c,
		"AllocateGameServer",
		"GameServerAllocation",
		"分配游戏服务器成功",
		true,
		response.CodeSuccess,
		result,
	)
}

// ListGameServers 列出游戏服务器
// @Summary 列出游戏服务器
// @Description 根据启动配置ID列出相关的游戏服务器
// @Tags 游戏服务器分配
// @Accept json
// @Produce json
// @Param page query int false "页码" default(0)
// @Param limit query int false "每页数量" default(10)
// @Success 200 {object} response.CommonResponse{data=[]GssAllocationRespData} "获取成功"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/allocation/gameservers [get]
func (h *AllocationHandler) ListGameServers(c *gin.Context) {
	// page, _ := strconv.Atoi(c.Query("page"))
	// limit, _ := strconv.Atoi(c.Query("limit"))
	// if page < 0 {
	// 	page = 0
	// }
	// if limit < 0 {
	// 	limit = 10
	// }
	// offset := page * limit
	// result, err := h.service.ListGameServers(c, limit, offset)
	// if err != nil {
	// 	response.HandleErrorWithAudit(c,
	// 		"ListGameServers",
	// 		"GameServerAllocation",
	// 		err,
	// 		"获取游戏服务器列表失败",
	// 		http.StatusInternalServerError)
	// 	return
	// }

	// response.ResponseWithAudit(c,
	// 	"ListGameServers",
	// 	"GameServerAllocation",
	// 	"获取游戏服务器列表成功",
	// 	result.Success,
	// 	response.CodeSuccess,
	// 	result,
	// )
}

// DeallocateGameServer 释放游戏服务器
// @Summary 释放游戏服务器
// @Description 释放指定的游戏服务器，使其可以被重新分配或销毁
// @Tags 游戏服务器分配
// @Accept json
// @Produce json
// @Param gameServerId path string true "游戏服务器ID"
// @Success 200 {object} response.CommonResponse{data=string} "释放成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/allocation/gameservers/{gameServerId} [delete]
func (h *AllocationHandler) DeallocateGameServer(c *gin.Context) {
	// gameServerID := c.Param("gameServerId")
	// if gameServerID == "" {
	// 	response.HandleErrorWithAudit(c,
	// 		"DeallocateGameServer",
	// 		"GameServerAllocation",
	// 		nil,
	// 		"游戏服务器ID不能为空",
	// 		http.StatusBadRequest)
	// 	return
	// }

	// err := h.service.DeallocateGameServer(c, gameServerID)
	// if err != nil {
	// 	response.HandleErrorWithAudit(c,
	// 		"DeallocateGameServer",
	// 		"GameServerAllocation",
	// 		err,
	// 		"释放游戏服务器失败",
	// 		http.StatusInternalServerError)
	// 	return
	// }

	// response.ResponseWithAudit(c,
	// 	"DeallocateGameServer",
	// 	"GameServerAllocation",
	// 	"释放游戏服务器成功",
	// 	true,
	// 	response.CodeSuccess,
	// 	"游戏服务器已成功释放",
	// )
}

// GetGameServerLogs 获取游戏服务器日志
// @Summary 获取游戏服务器日志
// @Description 获取指定游戏服务器的运行日志
// @Tags 游戏服务器分配
// @Accept json
// @Produce json
// @Param gameServerId path string true "游戏服务器ID"
// @Param lines query int false "日志行数" default(100)
// @Success 200 {object} response.CommonResponse{data=GssAllocationRespData} "获取成功"
// @Failure 400 {object} response.CommonResponse{error=string} "请求参数错误"
// @Failure 500 {object} response.CommonResponse{error=string} "服务器内部错误"
// @Router /api/v1/ros/multiverse/services/allocation/gameservers/{gameServerId}/logs [get]
func (h *AllocationHandler) GetGameServerLogs(c *gin.Context) {
	// gameServerID := c.Param("gameServerId")
	// if gameServerID == "" {
	// 	response.HandleErrorWithAudit(c,
	// 		"GetGameServerLogs",
	// 		"GameServerAllocation",
	// 		nil,
	// 		"游戏服务器ID不能为空",
	// 		http.StatusBadRequest)
	// 	return
	// }

	// lines := 100 // 默认100行
	// if linesStr := c.Query("lines"); linesStr != "" {
	// 	if l, err := strconv.Atoi(linesStr); err == nil && l > 0 {
	// 		lines = l
	// 	}
	// }

	// // 这里应该调用服务层获取日志
	// // logs, err := h.service.GetGameServerLogs(c, gameServerID, lines)

	// // 模拟日志响应
	// logsResponse := GameServerLogsResponse{
	// 	GameServerID: gameServerID,
	// 	Lines:        lines,
	// 	Logs: []string{
	// 		"2024-01-01 10:00:00 [INFO] Game server starting...",
	// 		"2024-01-01 10:00:01 [INFO] Loading configuration...",
	// 		"2024-01-01 10:00:02 [INFO] Server ready, listening on port 7777",
	// 		"2024-01-01 10:00:03 [INFO] Waiting for players...",
	// 	},
	// 	Timestamp: "2024-01-01T10:00:03Z",
	// }

	// response.ResponseWithAudit(c,
	// 	"GetGameServerLogs",
	// 	"GameServerAllocation",
	// 	"获取游戏服务器日志成功",
	// 	true,
	// 	response.CodeSuccess,
	// 	logsResponse,
	// )
}

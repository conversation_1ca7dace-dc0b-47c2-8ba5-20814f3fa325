package allocation

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/database"
	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册路由（使用默认数据库）
func RegisterRoutes(r *gin.RouterGroup) {
	service := NewAllocationService(database.DB)
	handler := NewAllocationHandler(service)
	registerRoutersWithHandler(r, handler)
}

// registerRoutersWithHandler 注册路由（支持依赖注入）
func registerRoutersWithHandler(r *gin.RouterGroup, handler *AllocationHandler) {
	allocationGroup := r.Group("/allocation")
	{
		// 游戏服务器管理
		gameServerGroup := allocationGroup.Group("/gameservers")
		{
			// 分配游戏服务器
			gameServerGroup.POST("", handler.AllocateGameServer)

			// 列出游戏服务器
			gameServerGroup.GET("", handler.ListGameServers)

			// 释放游戏服务器
			gameServerGroup.DELETE("/:gameServerId", handler.DeallocateGameServer)

			// 获取游戏服务器日志
			gameServerGroup.GET("/:gameServerId/logs", handler.GetGameServerLogs)
		}
	}
}

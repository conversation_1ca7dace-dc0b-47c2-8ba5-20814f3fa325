package router

import (
	"time"

	_ "git.mg.xyz/paas-group/ros-group/multiverse/docs"
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/router/services"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/middleware"

	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// RegisterRoutes 注册所有路由
func RegisterRoutes(r *gin.Engine) {

	// 注册 Prometheus metrics 端点，添加过滤配置
	r.GET("/metrics", gin.WrapH(promhttp.HandlerFor(
		prometheus.DefaultGatherer,
		promhttp.HandlerOpts{
			// 禁用默认的请求计数
			DisableCompression: true,
			// 自定义错误处理
			ErrorHandling: promhttp.HTTPErrorOnError,
			// 设置超时为 10 秒
			Timeout: 10 * time.Second,
		},
	)))

	// API v1 路由组
	v1 := r.Group("/api/v1/ros/multiverse")
	{
		authorized := v1.Group("")
		// authorized.Use(middleware.AuthMiddleware())
		// authorized.Use(middleware.JWTAuth())
		authorized.Use(middleware.FakerMiddleware())
		{
			services.RegisterRoutes(authorized)
		}
	}

	// Swagger 文档路由
	v1.GET("/swagger/*any", SwaggerBasicAuth(), ginSwagger.WrapHandler(swaggerfiles.Handler))
}

// BasicAuth 中间件
func SwaggerBasicAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		enable := config.GlobalConfig.SwaggerBasicAuth.Enable
		if !enable {
			c.Next()
			return
		}
		username := config.GlobalConfig.SwaggerBasicAuth.Username
		password := config.GlobalConfig.SwaggerBasicAuth.Password
		u, p, ok := c.Request.BasicAuth()
		if !ok || u != username || p != password {
			c.Header("WWW-Authenticate", `Basic realm=\"Authorization Required\"`)
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}
		c.Next()
	}
}

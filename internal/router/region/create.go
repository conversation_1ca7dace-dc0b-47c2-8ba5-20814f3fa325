package region

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/database"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/utils"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"time"
)

func CreateUserRegionInfo(c *gin.Context) {
	var input parameter.CreateUserRegionRequest
	if err := c.ShouldBindJSON(&input); err != nil {
		log.Errorf("CreateUserRegionInfo ShouldBindJSON error: %v", err)
		utils.ResponseWithGin(c, "请求参数错误", false, utils.CodeInvalidParameter, "")
		return
	}
	//从数据字典中获取启用的地域数据
	var DictType []model.DictType
	err := database.DB.Model(&model.DictType{}).Where("name = ?", "region").Where("enabled = ?", 1).Find(&DictType).Error
	if err != nil {
		log.Errorf("获取字典类型失败 err: %v", err)
		utils.ResponseWithGin(c, "获取字典类型失败", false, utils.CodeDatabaseError, "")
		return
	}
	var List []string
	for _, v := range DictType {
		List = append(List, v.DictType)
	}
	var DictData []model.DictData
	err = database.DB.Model(&model.DictData{}).Where("enabled = ?", 1).Where("dict_type IN ?", List).Find(&DictData).Error
	if err != nil {
		log.Errorf("获取字典数据失败 err: %v", err)
		utils.ResponseWithGin(c, "获取字典数据失败", false, utils.CodeDatabaseError, "")
		return
	}
	//根据用户初始化绑定地域信息
	var userRegionList []model.UserRegion
	for _, v := range DictData {
		var userRegion model.UserRegion
		userRegion.UserId = input.UserId
		userRegion.RegionName = v.DictType
		userRegion.Zone = v.DictValue
		userRegion.Enable = 0
		userRegion.DisableTime = time.Now()
		userRegionList = append(userRegionList, userRegion)
	}
	err = database.DB.Model(&model.UserRegion{}).Create(&userRegionList).Error
	if err != nil {
		log.Errorf("<UNK> err: %v", err)
		utils.ResponseWithGin(c, "创建用户绑定地域关系失败", false, utils.CodeDatabaseError, "")
		return
	}
	utils.ResponseWithGin(c, "success", true, utils.CodeSuccess, "")
}

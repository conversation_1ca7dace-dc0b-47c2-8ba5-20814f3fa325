package region

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/database"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/utils"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

func UserRegionList(c *gin.Context) {
	var input parameter.UserRegionListRequest
	if err := c.ShouldBindQuery(&input); err != nil {
		log.Errorf("userRegionList ShouldBindJSON error: %v", err)
		utils.ResponseWithGin(c, "请求   参数错误", false, utils.CodeInvalidParameter, "")
		return
	}
	//设置默认值
	if input.Page < 1 {
		input.Page = 1
	}
	if input.PageSize < 1 {
		input.PageSize = 10
	}

	query := database.DB.Model(&model.UserRegion{})
	if input.UserName != "" {
		query = query.Where("user_name like ?", "%"+input.UserName+"%")
	}
	if input.RegionName != "" {
		query = query.Where("region_name = ?", input.RegionName)
	}
	if input.Zone != "" {
		query = query.Where("zone = ?", input.Zone)
	}
	if input.Enabled != 0 {
		query = query.Where("enabled = ?", input.Enabled)
	}
	var total int64
	if err := query.Count(&total).Error; err != nil {
		log.Errorf("userRegionList get Count error: %v", err)
		utils.ResponseWithGin(c, "获取数据总数失败", false, utils.CodeDatabaseError, "")
		return
	}
	var Data []model.UserRegion
	offset := input.PageSize * (input.Page - 1)
	err := query.Offset(offset).Limit(input.PageSize).Find(&Data).Error
	if err != nil {
		log.Errorf("userRegionList query error: %v", err)
		utils.ResponseWithGin(c, "获取用户绑定地域数据失败", false, utils.CodeDatabaseError, "")
		return
	}
	utils.ResponseWithGin(c, "success", true, utils.CodeSuccess, parameter.UserRegionListResponse{total, input.PageSize, Data})
}

package region

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/database"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/utils"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

func UserRegionDisabled(c *gin.Context) {
	var input parameter.UserRegionEnabledOrDisabled
	if err := c.ShouldBindJSON(&input); err != nil {
		log.Errorf("bind param failed, err:%v", err)
		utils.ResponseWithGin(c, "请求参数错误", false, utils.CodeInvalidParameter, "")
		return
	}
	err := database.DB.Model(&model.UserRegion{}).Where("id = ?", input.Id).Update("enabled", 1).Error
	if err != nil {
		log.Errorf("UserRegionEnabled: update user region failed, err:%v", err)
		utils.ResponseWithGin(c, "数据库跟新失败", false, utils.CodeDatabaseError, "")
		return
	}
	utils.ResponseWithGin(c, "success", true, utils.CodeSuccess, "success")
}

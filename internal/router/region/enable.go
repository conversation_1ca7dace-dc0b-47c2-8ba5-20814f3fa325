package region

import (
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/model"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/database"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/parameter"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/utils"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

func UserRegionEnabled(c *gin.Context) {
	var input parameter.UserRegionEnabledOrDisabled
	if err := c.ShouldBindJSON(&input); err != nil {
		log.Errorf("UserRegionEnabled: bind param failed: %v", err)
		utils.ResponseWithGin(c, "请求参数错误", false, utils.CodeInvalidParameter, "")
		return
	}
	err := database.DB.Model(&model.UserRegion{}).Where("id = ?", input.Id).Update("enabled", 2).Error
	if err != nil {
		log.Errorf("UserRegionEnabled: update user region failed: %v", err)
		utils.ResponseWithGin(c, "请用地域失败", false, utils.CodeDatabaseError, "")
		return
	}
	utils.ResponseWithGin(c, "success", true, utils.CodeSuccess, "success")
}

package main

import (
	"errors"
	"fmt"
	"html/template"
	"log"
	"os"
	"path/filepath"
	"reflect"
	"strings"

	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

func hiddenJsonFields(fields ...string) []gen.ModelOpt {
	var opts []gen.ModelOpt
	for _, field := range fields {
		opts = append(opts, gen.FieldJSONTag(field, "-"))
	}
	return opts
}

func toCamel(name string) string {
	parts := strings.Split(name, "_")
	for i, p := range parts {
		if len(p) > 0 {
			parts[i] = strings.ToUpper(p[:1]) + p[1:]
		}
	}
	return strings.Join(parts, "")
}

func main() {
	g := gen.NewGenerator(gen.Config{
		OutPath:      "./pkg/gen/query",                                                  // 生成代码的输出目录
		ModelPkgPath: "./pkg/gen/model",                                                  // 模型代码的输出目录
		Mode:         gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
	})
	const dsn = "root:MTVjZjc2YWEz@tcp(*************:32306)/multiverse?charset=utf8mb4&parseTime=True&loc=Local"
	// const dsn = "root:laoji666@tcp(localhost:3306)/func?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn))
	if err != nil {
		panic(err)
	}

	// 使用数据库连接
	g.UseDB(db)

	var tableNames []string
	db.Raw("SELECT table_name FROM information_schema.tables WHERE table_schema = ?", "multiverse").Scan(&tableNames)

	var models []interface{}
	for _, table := range tableNames {
		opts := hiddenJsonFields("db_version", "deleted", "updated_at", "created_at")
		model := g.GenerateModelAs(table, toCamel(table), opts...)
		models = append(models, model)
	}

	g.ApplyBasic(models...)
	g.Execute()

	// // 生成 Gin 控制器
	// generateGinControllers(models)
	// // 生成测试方法
	// generateGinTests(models)
}

type TemplateData struct {
	ModelPkgPath      string // 例如 "gen/model"
	QueryPkgPath      string // 例如 "gen/query"
	ModelName         string // 例如 "User"
	ModelTableComment string // 例如 "用户"
	RouteName         string // 例如 "users"
	ControllerName    string // 可选，"UserController"
	ProjectPath       string // 必须：项目根路径，例如 "github.com/yourname/yourproject"
	ModelNameLower    string // 小写形式，用于路径等
	LtSymbol          template.HTML
}

func generateGinControllers(tableModels []interface{}) {
	projectPath := getProjectPath()
	templatePath := "./cmd/gen/templates/gin_controller.tmpl"
	controllerDir := "./pkg/gen/controller"

	if _, err := os.Stat(controllerDir); os.IsNotExist(err) {
		os.MkdirAll(controllerDir, os.ModePerm)
	}

	tmpl, err := template.ParseFiles(templatePath)
	if err != nil {
		log.Fatalf("加载模板失败: %v", err)
	}

	for _, table := range tableModels {
		modelName, err := getModelName(table)
		if err != nil {
			log.Printf("获取模型名称失败: %v", err)
			continue
		}
		modelTableComment, err := getModelTableComment(table)
		if err != nil {
			log.Printf("获取模型注释失败: %v", err)
			continue
		}

		modelNameTrimmed := strings.TrimSuffix(modelName, "Do")
		modelNameLower := strings.ToLower(modelNameTrimmed)
		controllerFileName := filepath.Join(controllerDir, fmt.Sprintf("%s_controller.go", modelNameLower))

		data := TemplateData{
			ModelName:         modelName,
			ModelTableComment: modelTableComment,
			ModelNameLower:    modelNameLower,
			RouteName:         modelNameLower,
			ModelPkgPath:      "func.ros.mg.xyz/pkg/gen/model",
			QueryPkgPath:      "func.ros.mg.xyz/pkg/gen/query",
			ProjectPath:       projectPath,
			LtSymbol:          "<",
		}

		file, err := os.Create(controllerFileName)
		if err != nil {
			log.Printf("创建控制器文件失败 (%s): %v", controllerFileName, err)
			continue
		}
		defer file.Close()

		if err := tmpl.Execute(file, data); err != nil {
			log.Printf("渲染模板失败 (%s): %v", controllerFileName, err)
			continue
		}

		fmt.Printf("✅ 已生成控制器: %s\n", controllerFileName)
	}
}

func generateGinTests(tableModels []interface{}) {
	projectPath := getProjectPath()
	templatePath := "./cmd/gen/templates/gin_controller_test.tmpl"
	outputDir := "./pkg/gen/test"

	if _, err := os.Stat(outputDir); os.IsNotExist(err) {
		os.MkdirAll(outputDir, os.ModePerm)
	}

	tmpl, err := template.ParseFiles(templatePath)
	if err != nil {
		log.Fatalf("加载 HTTP 客户端测试模板失败: %v", err)
	}

	for _, table := range tableModels {
		modelName, err := getModelName(table)
		if err != nil {
			log.Printf("获取模型名称失败: %v", err)
			continue
		}

		modelNameTrimmed := strings.TrimSuffix(modelName, "Do")
		modelNameLower := strings.ToLower(modelNameTrimmed)
		testFileName := filepath.Join(outputDir, fmt.Sprintf("%s_client_test.go", modelNameLower))

		data := TemplateData{
			ModelName:      modelNameTrimmed,
			ModelNameLower: modelNameLower,
			RouteName:      modelNameLower,
			ModelPkgPath:   "func.ros.mg.xyz/pkg/gen/model",
			QueryPkgPath:   "func.ros.mg.xyz/pkg/gen/query",
			ProjectPath:    projectPath,
		}

		file, err := os.Create(testFileName)
		if err != nil {
			log.Printf("创建 HTTP 测试文件失败 (%s): %v", testFileName, err)
			continue
		}
		defer file.Close()

		if err := tmpl.Execute(file, data); err != nil {
			log.Printf("渲染模板失败 (%s): %v", testFileName, err)
			continue
		}

		fmt.Printf("🌐 已生成客户端测试文件: %s\n", testFileName)
	}
}

// getModelName 通过反射获取模型名称
func getModelName(table interface{}) (string, error) {
	// 获取值的反射类型
	val := reflect.ValueOf(table)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	// 确保我们处理的是结构体
	if val.Kind() != reflect.Struct {
		return "", fmt.Errorf("expected struct, got %s", val.Kind())
	}

	// 尝试从结构体字段中获取模型名称
	typ := val.Type()
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		// 查找可能包含模型名称的字段
		if field.Name == "ModelName" || field.Name == "Name" || strings.Contains(strings.ToLower(field.Name), "model") {
			if fieldValue := val.Field(i); fieldValue.IsValid() && fieldValue.CanInterface() {
				if name, ok := fieldValue.Interface().(string); ok {
					return name, nil
				}
			}
		}
	}

	// 如果没有找到特定字段，使用结构体名称
	return typ.Name(), nil
}

func getModelTableComment(table interface{}) (string, error) {
	// 获取值的反射类型
	val := reflect.ValueOf(table)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	// 确保我们处理的是结构体
	if val.Kind() != reflect.Struct {
		return "", fmt.Errorf("expected struct, got %s", val.Kind())
	}

	// 尝试从结构体字段中获取模型名称
	typ := val.Type()
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		if field.Name == "TableComment" {
			if fieldValue := val.Field(i); fieldValue.IsValid() && fieldValue.CanInterface() {
				if name, ok := fieldValue.Interface().(string); ok {
					return name, nil
				}
			}
		}
	}
	return "", errors.New("TableComment not found")
}

func getProjectPath() string {
	// 获取项目路径
	currentDir, err := os.Getwd()
	if err != nil {
		log.Fatal(err)
	}
	return currentDir
}

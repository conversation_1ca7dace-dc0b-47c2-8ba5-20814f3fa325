package main

import (
	"flag"
	"fmt"

	_ "git.mg.xyz/paas-group/ros-group/multiverse/docs"
	"git.mg.xyz/paas-group/ros-group/multiverse/internal/router/gamefrontend"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func main() {
	configPath := flag.String("config", "./config.yaml", "配置文件路径")
	flag.Parse()
	config.LoadGameFrontendConfig(*configPath)

	r := gin.Default()
	if config.GameFrontendCfg.SwaggerEnable {
		r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}
	gamefrontend.RegisterTicketRoutes(r)
	addr := fmt.Sprintf(":%d", config.GameFrontendCfg.Port)
	r.<PERSON>(addr)
}

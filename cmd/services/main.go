package main

import (
	"context"
	"flag"
	"strings"

	"time"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"git.mg.xyz/paas-group/ros-group/multiverse/internal/router"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/cache"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/config"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/database"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/logger"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/middleware"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg/serverallocation"
	_ "git.mg.xyz/paas-group/ros-group/multiverse/pkg/validators" // 初始化自定义验证器
)

// @title           Multiverse API
// @version         1.0
// @description     This is the API server for multiverse service.
// @termsOfService  http://swagger.io/terms/

// @host      localhost:8080
// @BasePath  /

func main() {
	// 命令行参数
	var configPath string
	flag.StringVar(&configPath, "c", "configs", "配置文件目录路径(简写)")
	flag.Parse()

	// 初始化日志
	logger.InitLogger()
	defer logger.Sync()

	// 初始化配置
	if err := config.InitConfig(configPath); err != nil {
		logger.Fatal("初始化配置失败", zap.Error(err))
	}

	// 初始化数据库
	if err := database.InitDB(); err != nil {
		logger.Fatal("初始化数据库失败", zap.Error(err))
	}

	// 初始化Redis
	if err := cache.InitRedis(); err != nil {
		logger.Fatal("初始化Redis失败", zap.Error(err))
	}
	defer cache.Close()

	// 创建 Gin 引擎
	r := gin.New() // 使用 gin.New() 而不是 gin.Default()，因为我们要自定义日志中间件

	// 加载HTML模板
	r.LoadHTMLGlob("templates/*/*/*")

	// 添加 Recovery 中间件
	r.Use(gin.Recovery())
	// 添加自定义日志中间件
	r.Use(middleware.Logger())
	// 添加 metrics 中间件
	r.Use(middleware.Metrics())

	// 添加缓存中间件
	r.Use(middleware.Cache(middleware.CacheConfig{
		Expiration: 3 * time.Second, // 设置较短的过期时间
		Prefix:     "api:cache",     // 设置缓存前缀
		SkipCache: func(c *gin.Context) bool {
			// 跳过非GET请求
			if c.Request.Method != "GET" {
				return true
			}
			// 跳过带有特定查询参数的请求
			if c.Query("nocache") == "true" {
				return true
			}
			// 跳过特定路径
			path := c.Request.URL.Path
			skipPaths := []string{
				"/api/v1/ros/func/funcinst/swagger",   // Swagger UI
				"/api/v1/ros/func/funcinst/swagger/*", // Swagger 资源
				"/metrics",                            // Prometheus metrics
				"/debug/pprof",                        // pprof
				"/debug/pprof/*",                      // pprof 资源
			}
			for _, skipPath := range skipPaths {
				if strings.HasPrefix(path, skipPath) {
					return true
				}
			}
			return false
		},
	}))
	// 注册 pprof 路由
	pprof.Register(r)
	// 注册路由
	router.RegisterRoutes(r)

	// 启动k8s多集群管理服务
	k8ssvc := serverallocation.K8sServiceSingleton()
	k8ssvc.Start(context.Background())
	defer k8ssvc.Stop()

	// 启动服务器
	logger.Info("服务器启动", zap.String("address", config.GlobalConfig.Server.Address))
	if err := r.Run(config.GlobalConfig.Server.Address); err != nil {
		logger.Fatal("启动服务器失败", zap.Error(err))
	}
}

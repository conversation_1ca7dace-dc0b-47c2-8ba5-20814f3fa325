package main

import (
	"fmt"
	"log"
	"syscall"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/common/utils"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/config"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/crontab"
	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/dao"

	"github.com/fvbock/endless"
	"github.com/gin-gonic/gin"

	"git.mg.xyz/paas-group/ros-group/multiverse/pkg-bak/routers"
)

func init() {
	config.Setup()
	dao.OrmSetup()
	dao.RedisSetup()
	utils.Setup()
}

func main() {
	gin.SetMode(config.ServerSetting.RunMode)

	if err := crontab.InitCron(); err != nil {
		log.Fatalf("Cron task setup failed: %v", err)
	}
	routersInit := routers.InitRouter()
	readTimeout := config.ServerSetting.ReadTimeout
	writeTimeout := config.ServerSetting.WriteTimeout
	endPoint := fmt.Sprintf(":%d", config.ServerSetting.HttpPort)
	maxHeaderBytes := 1 << 20

	// Graceful Restart
	endless.DefaultReadTimeOut = readTimeout
	endless.DefaultWriteTimeOut = writeTimeout
	endless.DefaultMaxHeaderBytes = maxHeaderBytes
	server := endless.NewServer(endPoint, routersInit)
	server.BeforeBegin = func(add string) {
		log.Printf("Actual pid is %d", syscall.Getpid())
	}

	err := server.ListenAndServe()
	if err != nil {
		log.Printf("Server err: %v", err)
	}
}

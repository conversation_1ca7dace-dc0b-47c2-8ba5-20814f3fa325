# 服务器配置
server:
  address: ":8081"
  # 环境: 生产环境prod、测试环境test、开发环境dev
  env: "dev"

# 数据库配置
database:
  host: "*************"
  port: 32306
  username: "root"
  password: "MTVjZjc2YWEz"
  name: "multiverse"
# JWT配置
jwt:
  secret: "rlSM6VHdro175qIFhtcMpY7L8uIleq7081PDrsCWuQ0="
  expiration: 24

# 日志配置
log:
  level: "info"

# Redis配置
redis:
  host: "*************" # Redis服务器地址
  port: 6450 # Redis端口
  password: "a5c7d93a40e46e2a3e17b524b628572e" # Redis密码，如果有的话
  db: 0 # Redis数据库编号
  enable_api_cache: true # 是否启用接口缓存

# Kafka配置
kafka:
  brokers:
    - *************:9092
    - *************:9092
    - *************:9092
  topics:
    multiverse: "multiverse"
  consumer_group: "multiverse_consumer" # 消费者组
  version: "3.3" # Kafka版本
  security_protocol: "PLAINTEXT" # 安全协议
  sasl_username: "" # SASL用户名
  sasl_password: "" # SASL密码

# Elasticsearch配置
elasticsearch:
  hosts:
    - "https://**********:9200" # ES服务器地址
  username: "elastic" # ES用户名，如果有的话
  password: "MTVjZjc2YWEz" # ES密码，如果有的话
  sniff: false # 是否启用嗅探
  index_prefix: "audit_logs" # 索引前缀
  ca_cert_path: "" # CA证书路径，如有自签名证书请填写
  insecure_skip_verify: true # 跳过证书校验（仅开发环境建议）

# Jaeger配置
jaeger:
  service_name: "multiverse-service" # 服务名称
  otlp_endpoint: "**********:4318" # Jaeger OTLP HTTP 端点
  environment: "development" # 环境名称
  service_version: "1.0.0" # 服务版本

# S3配置
s3:
  endpoint: "oss-cn-hangzhou.aliyuncs.com" # S3服务端点
  access_key_id: "LTAI5tC28LXuraAU91ueSCok" # 访问密钥ID
  access_key_secret: "******************************" # 访问密钥密码
  # bucket: "roc-multiverse" # 存储桶名称
  bucket: "roc-func" # 存储桶名称-零时使用func项目的存储桶
  region: "cn-hangzhou" # 区域
  use_ssl: true # 是否使用SSL
  force_path_style: false # 使用虚拟主机样式访问

# # Canal配置
# canal:
#   address: *************
#   port: 11111
#   username: root
#   password: root
#   destination: multiverse
#   so_timeout: 60000
#   idle_timeout: 3600000
#   enable: false

swagger_basic_auth:
  username: multiverse
  password: multiverse@#$
  enable: false


# faker context 数据
faker:
  enable: true
  project_id: 1
  tenant_id: 1
  user_id: 1000000000
  user_uuid: "1234567890"
  user_name: "faker"
  user_email: "<EMAIL>"
  user_phone: "1234567890"
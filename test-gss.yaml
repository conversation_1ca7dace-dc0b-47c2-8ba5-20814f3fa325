apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: test-gss
  namespace: multiverse-game-server
spec:
  replicas: 1
  gameServerTemplate:
    spec:
      containers:
      - name: game-server
        image: nginx:latest
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        resources:
          requests:
            cpu: "0.1"
            memory: "128Mi"

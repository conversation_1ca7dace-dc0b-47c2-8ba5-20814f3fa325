basePath: /
definitions:
  anypb.Any:
    properties:
      type_url:
        description: |-
          A URL/resource name that uniquely identifies the type of the serialized
          protocol buffer message. This string must contain at least
          one "/" character. The last segment of the URL's path must represent
          the fully qualified name of the type (as in
          `path/google.protobuf.Duration`). The name should be in a canonical form
          (e.g., leading "." is not accepted).

          In practice, teams usually precompile into the binary all types that they
          expect it to use in the context of Any. However, for URLs which use the
          scheme `http`, `https`, or no scheme, one can optionally set up a type
          server that maps type URLs to message definitions as follows:

            - If no scheme is provided, `https` is assumed.
            - An HTTP GET on the URL must yield a [google.protobuf.Type][]
              value in binary format, or produce an error.
            - Applications are allowed to cache lookup results based on the
              URL, or have them precompiled into a binary to avoid any
              lookup. Therefore, binary compatibility needs to be preserved
              on changes to types. (Use versioned type names to manage
              breaking changes.)

          Note: this functionality is not currently available in the official
          protobuf release, and it is not used for type URLs beginning with
          type.googleapis.com. As of May 2023, there are no widely used type server
          implementations and no plans to implement one.

          Schemes other than `http`, `https` (or the empty scheme) might be
          used with implementation specific semantics.
        type: string
      value:
        description: Must be a valid serialized protocol buffer of the above specified
          type.
        items:
          type: integer
        type: array
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_internal_model.BaseImageConfig:
    properties:
      category:
        type: string
      description:
        type: string
      icon:
        type: string
      image:
        type: string
      name:
        type: string
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_internal_model.Port:
    properties:
      name:
        type: string
      port:
        type: integer
      protocol:
        type: string
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_internal_openmatch.Backfill:
    properties:
      assignment:
        additionalProperties: true
        description: 分配信息
        type: object
      extensions:
        additionalProperties: true
        description: 扩展字段
        type: object
      id:
        description: Backfill 唯一ID
        type: string
      searchFields:
        additionalProperties: true
        description: 匹配搜索字段
        type: object
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_imagebuild.BuildStatus:
    properties:
      baseImage:
        type: string
      completedAt:
        type: string
      createdAt:
        type: string
      entryPoint:
        type: string
      error:
        type: string
      id:
        type: string
      imageName:
        type: string
      logs:
        items:
          type: string
        type: array
      projectId:
        type: string
      status:
        description: pending, building, success, failed
        type: string
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildListResponse:
    properties:
      builds:
        items:
          $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_imagebuild.BuildStatus'
        type: array
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildRuntimesResponse:
    properties:
      baseImages:
        additionalProperties:
          $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_internal_model.BaseImageConfig'
        type: object
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildStatusResponse:
    properties:
      build:
        $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_imagebuild.BuildStatus'
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildUploadResponse:
    properties:
      buildId:
        type: string
      message:
        type: string
      projectId:
        type: string
      success:
        type: boolean
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfiguration:
    properties:
      callback_url:
        type: string
      server_os:
        type: string
      server_timeout:
        type: integer
    required:
    - server_os
    - server_timeout
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfigurationResponse:
    properties:
      callbackURL:
        type: string
      createdAt:
        description: |-
          CreatedAt time.Time `gorm:"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:创建时间"` // 创建时间（非空，默认当前时间）
          UpdatedAt time.Time `gorm:"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:更新时间"` // 更新时间（非空，默认当前时间，更新时自动更新）
          swagger:ignore
        type: string
      dbVersion:
        description: swagger:ignore
        type: integer
      deleted:
        description: swagger:ignore
        type: boolean
      id:
        description: 主键ID
        type: integer
      projectID:
        description: 所属项目 ID
        type: integer
      serverOS:
        type: string
      serverTimeout:
        type: integer
      tenantID:
        description: 所属租户 ID
        type: integer
      updatedAt:
        description: swagger:ignore                           // 创建时间（非空，默认当前时间）
        type: string
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationCreateReq:
    properties:
      name:
        description: 启动配置名字 长度大于1小于128个字符
        type: string
    required:
    - name
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse:
    properties:
      createdAt:
        description: |-
          CreatedAt time.Time `gorm:"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:创建时间"` // 创建时间（非空，默认当前时间）
          UpdatedAt time.Time `gorm:"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:更新时间"` // 更新时间（非空，默认当前时间，更新时自动更新）
          swagger:ignore
        type: string
      dbVersion:
        description: swagger:ignore
        type: integer
      deleted:
        description: swagger:ignore
        type: boolean
      id:
        description: 主键ID
        type: integer
      imageConfigCT:
        type: integer
      imageConfigID:
        type: integer
      imageTag:
        type: string
      name:
        type: string
      projectConfigurationID:
        type: integer
      projectID:
        description: 所属项目 ID
        type: integer
      tenantID:
        description: 所属租户 ID
        type: integer
      updatedAt:
        description: swagger:ignore                           // 创建时间（非空，默认当前时间）
        type: string
      uuid:
        type: string
    type: object
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CodeType:
    enum:
    - 6400
    - 6499
    - 6500
    - 6501
    - 6502
    - 6503
    - 6504
    - 6505
    - 6506
    - 6600
    - 6601
    - 6602
    - 6603
    - 6700
    - 6701
    - 6702
    - 6800
    - 6801
    type: integer
    x-enum-comments:
      CodeAccountLocked: 账号被锁定
      CodeAlreadyExists: 资源已存在
      CodeDatabaseError: 数据库操作失败
      CodeDuplicateEntry: 记录已存在（唯一约束冲突）
      CodeExternalServiceError: 调用外部服务失败
      CodeExternalServiceTimeout: 调用外部服务超时
      CodeFail: 通用失败
      CodeForbidden: 禁止访问
      CodeIncorrectPassword: 密码不正确
      CodeInvalidParameter: 参数无效
      CodeMethodNotAllowed: 方法不允许
      CodeNotFound: 资源不存在
      CodeRecordNotFound: 记录不存在
      CodeSuccess: 通用成功
      CodeTooManyRequests: 请求过多
      CodeUnauthorized: 未授权
      CodeUserAlreadyExists: 用户已存在
      CodeUserNotFound: 用户不存在
    x-enum-varnames:
    - CodeSuccess
    - CodeFail
    - CodeInvalidParameter
    - CodeNotFound
    - CodeAlreadyExists
    - CodeUnauthorized
    - CodeForbidden
    - CodeMethodNotAllowed
    - CodeTooManyRequests
    - CodeUserNotFound
    - CodeUserAlreadyExists
    - CodeIncorrectPassword
    - CodeAccountLocked
    - CodeDatabaseError
    - CodeRecordNotFound
    - CodeDuplicateEntry
    - CodeExternalServiceError
    - CodeExternalServiceTimeout
  git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse:
    properties:
      code:
        $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CodeType'
      data: {}
      error:
        type: string
      message:
        type: string
    type: object
  internal_router_services_allocation.GssAllocationRequest:
    properties:
      allocation_ttl:
        description: 最长存活时间
        minimum: 300
        type: integer
      env:
        additionalProperties:
          type: string
        description: 环境变量
        type: object
      region:
        description: 区域
        type: string
      startup_configuration_id:
        description: 启动配置ID
        type: integer
    required:
    - allocation_ttl
    - env
    - region
    - startup_configuration_id
    type: object
  internal_router_services_allocation.GssAllocationRespData:
    properties:
      allocationTTL:
        type: integer
      createdAt:
        type: string
      createdByUser:
        type: string
      dbVersion:
        description: swagger:ignore
        type: integer
      deleted:
        description: swagger:ignore
        type: boolean
      deletedAt:
        type: string
      deletedByUser:
        type: string
      env:
        additionalProperties:
          type: string
        type: object
      id:
        description: 主键ID
        type: integer
      modifiedAt:
        type: string
      modifiedByUser:
        type: string
      msg:
        type: string
      ports:
        items:
          $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_internal_model.Port'
        type: array
      projectConfigurationID:
        type: integer
      region:
        description: 区域名称
        type: string
      startupConfigurationID:
        type: integer
      startupConfigurationName:
        type: string
      status:
        type: string
      updatedAt:
        description: swagger:ignore                           // 创建时间（非空，默认当前时间）
        type: string
      uuid:
        type: string
    type: object
  pb.Assignment:
    properties:
      connection:
        description: Connection information for this Assignment.
        type: string
      extensions:
        additionalProperties:
          $ref: '#/definitions/anypb.Any'
        description: |-
          Customized information not inspected by Open Match, to be used by the match
          making function, evaluator, and components making calls to Open Match.
          Optional, depending on the requirements of the connected systems.
        type: object
    type: object
  pb.SearchFields:
    properties:
      double_args:
        additionalProperties:
          type: number
        description: Float arguments.  Filterable on ranges.
        type: object
      string_args:
        additionalProperties:
          type: string
        description: String arguments.  Filterable on equality.
        type: object
      tags:
        description: Filterable on presence or absence of given value.
        items:
          type: string
        type: array
    type: object
  pb.Ticket:
    properties:
      assignment:
        allOf:
        - $ref: '#/definitions/pb.Assignment'
        description: |-
          An Assignment represents a game server assignment associated with a Ticket,
          or whatever finalized matched state means for your use case.
          Open Match does not require or inspect any fields on Assignment.
      create_time:
        allOf:
        - $ref: '#/definitions/timestamppb.Timestamp'
        description: |-
          Create time is the time the Ticket was created. It is populated by Open
          Match at the time of Ticket creation.
      extensions:
        additionalProperties:
          $ref: '#/definitions/anypb.Any'
        description: |-
          Customized information not inspected by Open Match, to be used by the match
          making function, evaluator, and components making calls to Open Match.
          Optional, depending on the requirements of the connected systems.
        type: object
      id:
        description: Id represents an auto-generated Id issued by Open Match.
        type: string
      persistent_field:
        additionalProperties:
          $ref: '#/definitions/anypb.Any'
        description: |-
          Customized information not inspected by Open Match, to be kept persistent
          throughout the life-cycle of a ticket.
          Optional, depending on the requirements of the connected systems.
        type: object
      search_fields:
        allOf:
        - $ref: '#/definitions/pb.SearchFields'
        description: |-
          Search fields are the fields which Open Match is aware of, and can be used
          when specifying filters.
    type: object
  timestamppb.Timestamp:
    properties:
      nanos:
        description: |-
          Non-negative fractions of a second at nanosecond resolution. Negative
          second values with fractions must still have non-negative nanos values
          that count forward in time. Must be from 0 to 999,999,999
          inclusive.
        type: integer
      seconds:
        description: |-
          Represents seconds of UTC time since Unix epoch
          1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
          9999-12-31T23:59:59Z inclusive.
        type: integer
    type: object
host: localhost:8080
info:
  contact: {}
  description: This is the API server for multiverse service.
  termsOfService: http://swagger.io/terms/
  title: Multiverse API
  version: "1.0"
paths:
  /api/v1/ros/multiverse/services/allocation/allocate:
    post:
      consumes:
      - application/json
      description: 根据启动配置分配一个游戏服务器实例
      parameters:
      - description: 分配请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/internal_router_services_allocation.GssAllocationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 分配成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/internal_router_services_allocation.GssAllocationRespData'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 分配游戏服务器
      tags:
      - 游戏服务器分配
  /api/v1/ros/multiverse/services/allocation/gameservers:
    get:
      consumes:
      - application/json
      description: 根据启动配置ID列出相关的游戏服务器
      parameters:
      - default: 0
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/internal_router_services_allocation.GssAllocationRespData'
                  type: array
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 列出游戏服务器
      tags:
      - 游戏服务器分配
  /api/v1/ros/multiverse/services/allocation/gameservers/{gameServerId}:
    delete:
      consumes:
      - application/json
      description: 释放指定的游戏服务器，使其可以被重新分配或销毁
      parameters:
      - description: 游戏服务器ID
        in: path
        name: gameServerId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 释放成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  type: string
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 释放游戏服务器
      tags:
      - 游戏服务器分配
  /api/v1/ros/multiverse/services/allocation/gameservers/{gameServerId}/logs:
    get:
      consumes:
      - application/json
      description: 获取指定游戏服务器的运行日志
      parameters:
      - description: 游戏服务器ID
        in: path
        name: gameServerId
        required: true
        type: string
      - default: 100
        description: 日志行数
        in: query
        name: lines
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/internal_router_services_allocation.GssAllocationRespData'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 获取游戏服务器日志
      tags:
      - 游戏服务器分配
  /api/v1/ros/multiverse/services/image-build/build/{buildId}:
    get:
      consumes:
      - application/json
      description: 根据构建ID获取构建状态
      parameters:
      - description: 构建ID
        in: path
        name: buildId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildStatusResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "404":
          description: 构建任务不存在
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 获取构建状态
      tags:
      - 镜像构建
  /api/v1/ros/multiverse/services/image-build/builds:
    get:
      consumes:
      - application/json
      description: 获取所有构建任务列表
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildListResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 获取所有构建任务
      tags:
      - 镜像构建
  /api/v1/ros/multiverse/services/image-build/runtimes:
    get:
      consumes:
      - application/json
      description: 获取支持的运行时环境列表
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildRuntimesResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 获取支持的运行时环境
      tags:
      - 镜像构建
  /api/v1/ros/multiverse/services/image-build/upload:
    post:
      consumes:
      - multipart/form-data
      description: 上传ZIP文件并启动镜像构建
      parameters:
      - description: 基础镜像
        in: formData
        name: baseImage
        required: true
        type: string
      - description: 项目名称
        in: formData
        name: projectName
        required: true
        type: string
      - description: 项目描述
        in: formData
        name: description
        type: string
      - description: 入口程序
        in: formData
        name: entryPoint
        required: true
        type: string
      - description: ZIP文件
        in: formData
        name: zipFile
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildUploadResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 上传项目文件并构建镜像
      tags:
      - 镜像构建
  /api/v1/ros/multiverse/services/project-configuration/{projectId}:
    get:
      consumes:
      - application/json
      description: 获取项目设置
      parameters:
      - description: 项目ID
        in: path
        name: projectId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfigurationResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 获取项目设置
      tags:
      - 项目设置
    put:
      consumes:
      - application/json
      description: 更新项目设置
      parameters:
      - description: 项目ID
        in: path
        name: projectId
        required: true
        type: integer
      - description: 项目设置
        in: body
        name: param
        required: true
        schema:
          $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfiguration'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfigurationResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 更新项目设置
      tags:
      - 项目设置
  /api/v1/ros/multiverse/services/startup-configurations:
    get:
      consumes:
      - application/json
      description: 获取启动配置列表
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse'
                  type: array
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 获取启动配置列表
      tags:
      - 启动配置
    post:
      consumes:
      - application/json
      description: 创建启动配置
      parameters:
      - description: 启动配置
        in: body
        name: param
        required: true
        schema:
          $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 创建启动配置
      tags:
      - 启动配置
  /api/v1/ros/multiverse/services/startup-configurations/{id}:
    delete:
      consumes:
      - application/json
      description: 删除启动配置
      parameters:
      - description: 启动配置ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 删除启动配置
      tags:
      - 启动配置
    get:
      consumes:
      - application/json
      description: 获取启动配置
      parameters:
      - description: 启动配置ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 获取启动配置
      tags:
      - 启动配置
    put:
      consumes:
      - application/json
      description: 更新启动配置
      parameters:
      - description: 启动配置ID
        in: path
        name: id
        required: true
        type: integer
      - description: 启动配置
        in: body
        name: param
        required: true
        schema:
          $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse'
            - properties:
                error:
                  type: string
              type: object
      summary: 更新启动配置
      tags:
      - 启动配置
  /backfill:
    post:
      responses:
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
  /backfill/{backfill_id}:
    delete:
      description: 删除指定ID的Backfill
      parameters:
      - description: BackfillID
        in: path
        name: backfill_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 删除Backfill
      tags:
      - Backfill
    get:
      description: 查询指定ID的Backfill详情
      parameters:
      - description: BackfillID
        in: path
        name: backfill_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_internal_openmatch.Backfill'
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 查询Backfill详情
      tags:
      - Backfill
    put:
      responses:
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
  /backfill/{backfill_id}/ack:
    post:
      responses:
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
  /ticket:
    post:
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
  /ticket/{ticket_id}:
    delete:
      description: 删除指定ID的Ticket
      parameters:
      - description: TicketID
        in: path
        name: ticket_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 删除Ticket
      tags:
      - Ticket
    get:
      description: 查询指定ID的Ticket详情
      parameters:
      - description: TicketID
        in: path
        name: ticket_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pb.Ticket'
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 查询Ticket详情
      tags:
      - Ticket
  /ticket/{ticket_id}/assignment:
    get:
      description: 查询指定Ticket的Assignment信息
      parameters:
      - description: TicketID
        in: path
        name: ticket_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 查询Ticket的Assignment
      tags:
      - Ticket
  /ticket/{ticket_id}/poll-assignment:
    get:
      description: 轮询指定Ticket的Assignment，最多30秒
      parameters:
      - description: TicketID
        in: path
        name: ticket_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 长轮询Ticket Assignment
      tags:
      - Ticket
  /ticket/{ticket_id}/watch-assignments:
    get:
      description: 流式监听指定Ticket的Assignment（简单实现为多次拉取）
      parameters:
      - description: TicketID
        in: path
        name: ticket_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 流式监听Ticket Assignment
      tags:
      - Ticket
swagger: "2.0"

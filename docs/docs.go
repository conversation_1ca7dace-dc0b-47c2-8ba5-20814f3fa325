// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/ros/multiverse/services/allocation/allocate": {
            "post": {
                "description": "根据启动配置分配一个游戏服务器实例",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "游戏服务器分配"
                ],
                "summary": "分配游戏服务器",
                "parameters": [
                    {
                        "description": "分配请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/internal_router_services_allocation.GssAllocationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分配成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/internal_router_services_allocation.GssAllocationRespData"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/allocation/gameservers": {
            "get": {
                "description": "根据启动配置ID列出相关的游戏服务器",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "游戏服务器分配"
                ],
                "summary": "列出游戏服务器",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/internal_router_services_allocation.GssAllocationRespData"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/allocation/gameservers/{gameServerId}": {
            "delete": {
                "description": "释放指定的游戏服务器，使其可以被重新分配或销毁",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "游戏服务器分配"
                ],
                "summary": "释放游戏服务器",
                "parameters": [
                    {
                        "type": "string",
                        "description": "游戏服务器ID",
                        "name": "gameServerId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "释放成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/allocation/gameservers/{gameServerId}/logs": {
            "get": {
                "description": "获取指定游戏服务器的运行日志",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "游戏服务器分配"
                ],
                "summary": "获取游戏服务器日志",
                "parameters": [
                    {
                        "type": "string",
                        "description": "游戏服务器ID",
                        "name": "gameServerId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 100,
                        "description": "日志行数",
                        "name": "lines",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/internal_router_services_allocation.GssAllocationRespData"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/image-build/build/{buildId}": {
            "get": {
                "description": "根据构建ID获取构建状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "镜像构建"
                ],
                "summary": "获取构建状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "构建ID",
                        "name": "buildId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildStatusResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "构建任务不存在",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/image-build/builds": {
            "get": {
                "description": "获取所有构建任务列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "镜像构建"
                ],
                "summary": "获取所有构建任务",
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/image-build/runtimes": {
            "get": {
                "description": "获取支持的运行时环境列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "镜像构建"
                ],
                "summary": "获取支持的运行时环境",
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildRuntimesResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/image-build/upload": {
            "post": {
                "description": "上传ZIP文件并启动镜像构建",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "镜像构建"
                ],
                "summary": "上传项目文件并构建镜像",
                "parameters": [
                    {
                        "type": "string",
                        "description": "基础镜像",
                        "name": "baseImage",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "项目名称",
                        "name": "projectName",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "项目描述",
                        "name": "description",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "入口程序",
                        "name": "entryPoint",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "ZIP文件",
                        "name": "zipFile",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildUploadResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/project-configuration/{projectId}": {
            "get": {
                "description": "获取项目设置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目设置"
                ],
                "summary": "获取项目设置",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "projectId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfigurationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "description": "更新项目设置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目设置"
                ],
                "summary": "更新项目设置",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "projectId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "项目设置",
                        "name": "param",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfiguration"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfigurationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/startup-configurations": {
            "get": {
                "description": "获取启动配置列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "启动配置"
                ],
                "summary": "获取启动配置列表",
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "description": "创建启动配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "启动配置"
                ],
                "summary": "创建启动配置",
                "parameters": [
                    {
                        "description": "启动配置",
                        "name": "param",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationCreateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/v1/ros/multiverse/services/startup-configurations/{id}": {
            "get": {
                "description": "获取启动配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "启动配置"
                ],
                "summary": "获取启动配置",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "启动配置ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "description": "更新启动配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "启动配置"
                ],
                "summary": "更新启动配置",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "启动配置ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "启动配置",
                        "name": "param",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationCreateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "description": "删除启动配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "启动配置"
                ],
                "summary": "删除启动配置",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "启动配置ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "error": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/backfill": {
            "post": {
                "responses": {
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/backfill/{backfill_id}": {
            "get": {
                "description": "查询指定ID的Backfill详情",
                "tags": [
                    "Backfill"
                ],
                "summary": "查询Backfill详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "BackfillID",
                        "name": "backfill_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_internal_openmatch.Backfill"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "put": {
                "responses": {
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "delete": {
                "description": "删除指定ID的Backfill",
                "tags": [
                    "Backfill"
                ],
                "summary": "删除Backfill",
                "parameters": [
                    {
                        "type": "string",
                        "description": "BackfillID",
                        "name": "backfill_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/backfill/{backfill_id}/ack": {
            "post": {
                "responses": {
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/ticket": {
            "post": {
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/ticket/{ticket_id}": {
            "get": {
                "description": "查询指定ID的Ticket详情",
                "tags": [
                    "Ticket"
                ],
                "summary": "查询Ticket详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "TicketID",
                        "name": "ticket_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pb.Ticket"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "delete": {
                "description": "删除指定ID的Ticket",
                "tags": [
                    "Ticket"
                ],
                "summary": "删除Ticket",
                "parameters": [
                    {
                        "type": "string",
                        "description": "TicketID",
                        "name": "ticket_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/ticket/{ticket_id}/assignment": {
            "get": {
                "description": "查询指定Ticket的Assignment信息",
                "tags": [
                    "Ticket"
                ],
                "summary": "查询Ticket的Assignment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "TicketID",
                        "name": "ticket_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/ticket/{ticket_id}/poll-assignment": {
            "get": {
                "description": "轮询指定Ticket的Assignment，最多30秒",
                "tags": [
                    "Ticket"
                ],
                "summary": "长轮询Ticket Assignment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "TicketID",
                        "name": "ticket_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/ticket/{ticket_id}/watch-assignments": {
            "get": {
                "description": "流式监听指定Ticket的Assignment（简单实现为多次拉取）",
                "tags": [
                    "Ticket"
                ],
                "summary": "流式监听Ticket Assignment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "TicketID",
                        "name": "ticket_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "anypb.Any": {
            "type": "object",
            "properties": {
                "type_url": {
                    "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n` + "`" + `path/google.protobuf.Duration` + "`" + `). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme ` + "`" + `http` + "`" + `, ` + "`" + `https` + "`" + `, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n  - If no scheme is provided, ` + "`" + `https` + "`" + ` is assumed.\n  - An HTTP GET on the URL must yield a [google.protobuf.Type][]\n    value in binary format, or produce an error.\n  - Applications are allowed to cache lookup results based on the\n    URL, or have them precompiled into a binary to avoid any\n    lookup. Therefore, binary compatibility needs to be preserved\n    on changes to types. (Use versioned type names to manage\n    breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com. As of May 2023, there are no widely used type server\nimplementations and no plans to implement one.\n\nSchemes other than ` + "`" + `http` + "`" + `, ` + "`" + `https` + "`" + ` (or the empty scheme) might be\nused with implementation specific semantics.",
                    "type": "string"
                },
                "value": {
                    "description": "Must be a valid serialized protocol buffer of the above specified type.",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_internal_model.BaseImageConfig": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "icon": {
                    "type": "string"
                },
                "image": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_internal_model.Port": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "port": {
                    "type": "integer"
                },
                "protocol": {
                    "type": "string"
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_internal_openmatch.Backfill": {
            "type": "object",
            "properties": {
                "assignment": {
                    "description": "分配信息",
                    "type": "object",
                    "additionalProperties": true
                },
                "extensions": {
                    "description": "扩展字段",
                    "type": "object",
                    "additionalProperties": true
                },
                "id": {
                    "description": "Backfill 唯一ID",
                    "type": "string"
                },
                "searchFields": {
                    "description": "匹配搜索字段",
                    "type": "object",
                    "additionalProperties": true
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_imagebuild.BuildStatus": {
            "type": "object",
            "properties": {
                "baseImage": {
                    "type": "string"
                },
                "completedAt": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "entryPoint": {
                    "type": "string"
                },
                "error": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "imageName": {
                    "type": "string"
                },
                "logs": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "projectId": {
                    "type": "string"
                },
                "status": {
                    "description": "pending, building, success, failed",
                    "type": "string"
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildListResponse": {
            "type": "object",
            "properties": {
                "builds": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_imagebuild.BuildStatus"
                    }
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildRuntimesResponse": {
            "type": "object",
            "properties": {
                "baseImages": {
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_internal_model.BaseImageConfig"
                    }
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildStatusResponse": {
            "type": "object",
            "properties": {
                "build": {
                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_imagebuild.BuildStatus"
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ImageBuildUploadResponse": {
            "type": "object",
            "properties": {
                "buildId": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "projectId": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfiguration": {
            "type": "object",
            "required": [
                "server_os",
                "server_timeout"
            ],
            "properties": {
                "callback_url": {
                    "type": "string"
                },
                "server_os": {
                    "type": "string"
                },
                "server_timeout": {
                    "type": "integer"
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceProjectConfigurationResponse": {
            "type": "object",
            "properties": {
                "callbackURL": {
                    "type": "string"
                },
                "createdAt": {
                    "description": "CreatedAt time.Time ` + "`" + `gorm:\"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:创建时间\"` + "`" + ` // 创建时间（非空，默认当前时间）\nUpdatedAt time.Time ` + "`" + `gorm:\"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:更新时间\"` + "`" + ` // 更新时间（非空，默认当前时间，更新时自动更新）\nswagger:ignore",
                    "type": "string"
                },
                "dbVersion": {
                    "description": "swagger:ignore",
                    "type": "integer"
                },
                "deleted": {
                    "description": "swagger:ignore",
                    "type": "boolean"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "projectID": {
                    "description": "所属项目 ID",
                    "type": "integer"
                },
                "serverOS": {
                    "type": "string"
                },
                "serverTimeout": {
                    "type": "integer"
                },
                "tenantID": {
                    "description": "所属租户 ID",
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "swagger:ignore                           // 创建时间（非空，默认当前时间）",
                    "type": "string"
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationCreateReq": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "name": {
                    "description": "启动配置名字 长度大于1小于128个字符",
                    "type": "string"
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_parameter.ServiceStartupConfigurationResponse": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "description": "CreatedAt time.Time ` + "`" + `gorm:\"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:创建时间\"` + "`" + ` // 创建时间（非空，默认当前时间）\nUpdatedAt time.Time ` + "`" + `gorm:\"type:timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;comment:更新时间\"` + "`" + ` // 更新时间（非空，默认当前时间，更新时自动更新）\nswagger:ignore",
                    "type": "string"
                },
                "dbVersion": {
                    "description": "swagger:ignore",
                    "type": "integer"
                },
                "deleted": {
                    "description": "swagger:ignore",
                    "type": "boolean"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "imageConfigCT": {
                    "type": "integer"
                },
                "imageConfigID": {
                    "type": "integer"
                },
                "imageTag": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "projectConfigurationID": {
                    "type": "integer"
                },
                "projectID": {
                    "description": "所属项目 ID",
                    "type": "integer"
                },
                "tenantID": {
                    "description": "所属租户 ID",
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "swagger:ignore                           // 创建时间（非空，默认当前时间）",
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CodeType": {
            "type": "integer",
            "enum": [
                6400,
                6499,
                6500,
                6501,
                6502,
                6503,
                6504,
                6505,
                6506,
                6600,
                6601,
                6602,
                6603,
                6700,
                6701,
                6702,
                6800,
                6801
            ],
            "x-enum-comments": {
                "CodeAccountLocked": "账号被锁定",
                "CodeAlreadyExists": "资源已存在",
                "CodeDatabaseError": "数据库操作失败",
                "CodeDuplicateEntry": "记录已存在（唯一约束冲突）",
                "CodeExternalServiceError": "调用外部服务失败",
                "CodeExternalServiceTimeout": "调用外部服务超时",
                "CodeFail": "通用失败",
                "CodeForbidden": "禁止访问",
                "CodeIncorrectPassword": "密码不正确",
                "CodeInvalidParameter": "参数无效",
                "CodeMethodNotAllowed": "方法不允许",
                "CodeNotFound": "资源不存在",
                "CodeRecordNotFound": "记录不存在",
                "CodeSuccess": "通用成功",
                "CodeTooManyRequests": "请求过多",
                "CodeUnauthorized": "未授权",
                "CodeUserAlreadyExists": "用户已存在",
                "CodeUserNotFound": "用户不存在"
            },
            "x-enum-varnames": [
                "CodeSuccess",
                "CodeFail",
                "CodeInvalidParameter",
                "CodeNotFound",
                "CodeAlreadyExists",
                "CodeUnauthorized",
                "CodeForbidden",
                "CodeMethodNotAllowed",
                "CodeTooManyRequests",
                "CodeUserNotFound",
                "CodeUserAlreadyExists",
                "CodeIncorrectPassword",
                "CodeAccountLocked",
                "CodeDatabaseError",
                "CodeRecordNotFound",
                "CodeDuplicateEntry",
                "CodeExternalServiceError",
                "CodeExternalServiceTimeout"
            ]
        },
        "git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CommonResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_pkg_response.CodeType"
                },
                "data": {},
                "error": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "internal_router_services_allocation.GssAllocationRequest": {
            "type": "object",
            "required": [
                "allocation_ttl",
                "env",
                "region",
                "startup_configuration_id"
            ],
            "properties": {
                "allocation_ttl": {
                    "description": "最长存活时间",
                    "type": "integer",
                    "minimum": 300
                },
                "env": {
                    "description": "环境变量",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "region": {
                    "description": "区域",
                    "type": "string"
                },
                "startup_configuration_id": {
                    "description": "启动配置ID",
                    "type": "integer"
                }
            }
        },
        "internal_router_services_allocation.GssAllocationRespData": {
            "type": "object",
            "properties": {
                "allocationTTL": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "createdByUser": {
                    "type": "string"
                },
                "dbVersion": {
                    "description": "swagger:ignore",
                    "type": "integer"
                },
                "deleted": {
                    "description": "swagger:ignore",
                    "type": "boolean"
                },
                "deletedAt": {
                    "type": "string"
                },
                "deletedByUser": {
                    "type": "string"
                },
                "env": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "modifiedAt": {
                    "type": "string"
                },
                "modifiedByUser": {
                    "type": "string"
                },
                "msg": {
                    "type": "string"
                },
                "ports": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/git_mg_xyz_paas-group_ros-group_multiverse_internal_model.Port"
                    }
                },
                "projectConfigurationID": {
                    "type": "integer"
                },
                "region": {
                    "description": "区域名称",
                    "type": "string"
                },
                "startupConfigurationID": {
                    "type": "integer"
                },
                "startupConfigurationName": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "updatedAt": {
                    "description": "swagger:ignore                           // 创建时间（非空，默认当前时间）",
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "pb.Assignment": {
            "type": "object",
            "properties": {
                "connection": {
                    "description": "Connection information for this Assignment.",
                    "type": "string"
                },
                "extensions": {
                    "description": "Customized information not inspected by Open Match, to be used by the match\nmaking function, evaluator, and components making calls to Open Match.\nOptional, depending on the requirements of the connected systems.",
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/anypb.Any"
                    }
                }
            }
        },
        "pb.SearchFields": {
            "type": "object",
            "properties": {
                "double_args": {
                    "description": "Float arguments.  Filterable on ranges.",
                    "type": "object",
                    "additionalProperties": {
                        "type": "number"
                    }
                },
                "string_args": {
                    "description": "String arguments.  Filterable on equality.",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "tags": {
                    "description": "Filterable on presence or absence of given value.",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "pb.Ticket": {
            "type": "object",
            "properties": {
                "assignment": {
                    "description": "An Assignment represents a game server assignment associated with a Ticket,\nor whatever finalized matched state means for your use case.\nOpen Match does not require or inspect any fields on Assignment.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/pb.Assignment"
                        }
                    ]
                },
                "create_time": {
                    "description": "Create time is the time the Ticket was created. It is populated by Open\nMatch at the time of Ticket creation.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/timestamppb.Timestamp"
                        }
                    ]
                },
                "extensions": {
                    "description": "Customized information not inspected by Open Match, to be used by the match\nmaking function, evaluator, and components making calls to Open Match.\nOptional, depending on the requirements of the connected systems.",
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/anypb.Any"
                    }
                },
                "id": {
                    "description": "Id represents an auto-generated Id issued by Open Match.",
                    "type": "string"
                },
                "persistent_field": {
                    "description": "Customized information not inspected by Open Match, to be kept persistent\nthroughout the life-cycle of a ticket.\nOptional, depending on the requirements of the connected systems.",
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/anypb.Any"
                    }
                },
                "search_fields": {
                    "description": "Search fields are the fields which Open Match is aware of, and can be used\nwhen specifying filters.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/pb.SearchFields"
                        }
                    ]
                }
            }
        },
        "timestamppb.Timestamp": {
            "type": "object",
            "properties": {
                "nanos": {
                    "description": "Non-negative fractions of a second at nanosecond resolution. Negative\nsecond values with fractions must still have non-negative nanos values\nthat count forward in time. Must be from 0 to 999,999,999\ninclusive.",
                    "type": "integer"
                },
                "seconds": {
                    "description": "Represents seconds of UTC time since Unix epoch\n1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to\n9999-12-31T23:59:59Z inclusive.",
                    "type": "integer"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/",
	Schemes:          []string{},
	Title:            "Multiverse API",
	Description:      "This is the API server for multiverse service.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}

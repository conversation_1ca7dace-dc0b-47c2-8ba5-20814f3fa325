# Rancher集群导入指南 (更新版)

## 🔄 重要更新
Rancher现在使用宿主机网络模式运行，新的配置信息：
- **Rancher URL**: https://*************:443 或 https://localhost:443
- **新的Bootstrap Password**: hszzrlnx8dw9462xzt92zb87bh6cpmxlwvfxcwspx6k5pqtt6kmwh8

## 集群信息总览

### 1m1w集群
- **集群名称**: kind-1m1w
- **API端点**: https://*************:36823 (宿主机IP)
- **备用端点**: https://127.0.0.1:36823
- **节点配置**: 1 Master + 1 Worker

### 1m3w集群
- **集群名称**: kind-1m3w
- **API端点**: https://*************:45875 (宿主机IP)
- **备用端点**: https://127.0.0.1:45875
- **节点配置**: 1 Master + 3 Workers

## 在Rancher中导入集群的步骤

### 步骤1: 创建集群导入
1. 在Rancher主界面，点击 **"Import Existing"**
2. 选择 **"Generic"** 作为集群类型
3. 输入集群名称（建议使用: `kind-1m1w` 或 `kind-1m3w`）

### 步骤2: 配置集群连接信息

#### 对于1m1w集群:
- **Cluster Name**: `kind-1m1w`
- **Kubernetes API Server URL**: `https://*************:36823`
- **CA Certificate**: 
```
-----BEGIN CERTIFICATE-----
MIIDBTCCAe2gAwIBAgIISG3QZOnZfP0wDQYJKoZIhvcNAQELBQAwFTETMBEGA1UE
AxMKa3ViZXJuZXRlczAeFw0yNTA3MjkwODM2MDNaFw0zNTA3MjcwODQxMDNaMBUx
EzARBgNVBAMTCmt1YmVybmV0ZXMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC13LwP0Q88jJ4t5bAMDxBlxs01d4vJDLqKIzgnWiI1h4oKF3Doo9K1ZuN4
gSc9skVq2KCn2qQ/6lGsg8vIDNhyNPjUd/i33YX/3OPEbd5Y7Azgm0ZXtrzGeJPC
IgJN5R0/Am9iFEKZXFeTxVoHt06hYKp5lhe/xedLwx86Ydll0G6zdyHrhV6U4tnq
uuSZjKAjPkwLwB7O6XO9C4msKy1Z5vpgzeWOW1fhxR8B213BcNCQnRBJ8FI0fdVo
WcAs1a34qThg6r4ktl2LPnWzLWbu1FikZxCCFPs0blBdJ8IS1pnvBgm8v20wRX94
A8OB0rQWUVNhDxxlLd+fWGoCkzRZAgMBAAGjWTBXMA4GA1UdDwEB/wQEAwICpDAP
BgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBQ2d6QzAIGvE/huEjvVFKtE9fW10jAV
BgNVHREEDjAMggprdWJlcm5ldGVzMA0GCSqGSIb3DQEBCwUAA4IBAQBmqd+JuhWh
fLYR1FJed5ODcbXV9F8OVO3ZRiijeVzKxDA5lqbmFZO5sOKRjNMTPRksyOTDRcSb
EtUgnGWw2ueVLvTw6qmDbrw2BPyWVH7weMKsuvNHQhplsPjPMtNifr6bn4un0G2I
wxBy3NJKlyB6XPm2NgaRfKKyoxgEcCJxruCdopo62itM0uMsKgnTP4KeBm8c8t00
0eMtOsikd0gpOXxt9+nRBw3smH7Pp4dI511g2d2BNv72wCdzuj0yTF8ayzsL6Nyc
Xlda4BwQFb9UG38ZJA0p8eGzF3skcP0iJ6wqExrubXCIumZ8GswitrXjnVpW+8e9
ErAQ3wS9C72Q
-----END CERTIFICATE-----
```

#### 对于1m3w集群:
- **Cluster Name**: `kind-1m3w`
- **Kubernetes API Server URL**: `https://*************:45875`
- **CA Certificate**:
```
-----BEGIN CERTIFICATE-----
MIIDBTCCAe2gAwIBAgIIbH46/zqquukwDQYJKoZIhvcNAQELBQAwFTETMBEGA1UE
AxMKa3ViZXJuZXRlczAeFw0yNTA3MjMwMTMwMTlaFw0zNTA3MjEwMTM1MTlaMBUx
EzARBgNVBAMTCmt1YmVybmV0ZXMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC4sU2IgDnVwUD9eoDIEZ9RSguvxizzpgBg+QkWHVgRipNJofP2Ex5FT66S
9MJKabbVbqBtJOD6EGGCLXrTexbUpmUt3y50tgN0F0c12xbF3eqxuKX498D3hhNv
KNchnqOYiAq8cSWLIzFK33bfQWbe6adpa7pQqNCp7ncqVXjUwtPY0g1i3DkYpkZ7
58tgQN+uX8+OuBlVKkFgAqainO9uNM0bynsvDXvY/zp7U5FcFlKZj7cE2MG5oKUH
+Jom1SJTNKd557oPT1Zx6eVaF9wTTdGXiB14wA9rlUgj17cZHIKREFbqbve2Xmpr
0T3QibGZCDY3GO8OsaUxAtWISsWNAgMBAAGjWTBXMA4GA1UdDwEB/wQEAwICpDAP
BgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBQqTRTTpPSovu4wMMwIcZTVObO3bzAV
BgNVHREEDjAMggprdWJlcm5ldGVzMA0GCSqGSIb3DQEBCwUAA4IBAQCALFYzQmEi
ZNZIHw8McIaQF0dsH6d9knUzsn4hG5RBztzO1FOVNP0ykhKI+syNybXkvNITBXI9
ey4dHs/UtPKzsJ+unm0K7YDDbbfRdDtUdqTGUrkFoL/fQLR6JMdghtSDp9bVG8XA
IlC+Mwri0X5mBWZdz0DFJvHQEPhl8PknpYe3tKk17O8493Ori9lBxVyi4l/49NMm
Ro84myaOaPIr+zvC6mLlaIqRLoW+AGospeMIDHtMYe2MwD1IUGVJjFuE+gHWkWPX
pJyS8zHeo+BxMo7iL6GdJ5Ivn6546NIoQnxCbJMLqrB9Uz5mHH+Mpkm8JZ43kICI
wPx8XcIIho3S
-----END CERTIFICATE-----
```

## 重要注意事项

### 网络配置
- 使用 `**********` 而不是 `127.0.0.1` 作为API服务器地址
- 这是Docker桥接网络的网关IP，Rancher容器可以通过此IP访问Kind集群

### 认证配置
- Kind集群使用自签名证书
- 需要提供完整的CA证书内容
- 确保证书格式正确（包含BEGIN和END标记）

### 故障排除
如果导入失败，检查：
1. 网络连通性：Rancher容器是否能访问Kind集群API
2. 证书验证：CA证书是否正确
3. 端口映射：Kind集群的端口是否正确映射

## 验证导入成功
导入成功后，你应该能在Rancher界面看到：
- 集群状态为 "Active"
- 节点信息正确显示
- 可以查看和管理集群资源

apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: gameserver-set-v1
  namespace: game-build
spec:
  replicas: 1
  gameServerTemplate:
    metadata:
      labels:
        app: gameserver-v1
    spec:
      imagePullSecrets:
        - name: harbor-auth
      containers:
        - name: gameserver
          image: hb.mg.xyz/multiverse/gameserver:v1
          ports:
            - name: gameport
              containerPort: 7777
              protocol: TCP
          env:
            - name: ENV
              value: "production"
          readinessProbe:
            tcpSocket:
              port: 7777
            initialDelaySeconds: 5
            periodSeconds: 10
      terminationGracePeriodSeconds: 10
